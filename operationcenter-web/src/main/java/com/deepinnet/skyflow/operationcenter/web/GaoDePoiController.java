package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.localdata.integration.model.outsidebean.GDPageResult;
import com.deepinnet.localdata.integration.model.outsidebean.PoiSearchKeyResponse;
import com.deepinnet.localdata.integration.model.outsidebean.PoiSearchRequest;
import com.deepinnet.skyflow.operationcenter.service.client.GaoDeEnterpriseMapRemoteClient;
import com.deepinnet.spatiotemporalplatform.client.area.GeoFenceClient;
import com.deepinnet.spatiotemporalplatform.model.area.PoiKeywordQueryParams;
import com.deepinnet.spatiotemporalplatform.model.area.PoiKeywordResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Description:
 * Date: 2025/7/27
 * Author: lijunheng
 */
@RestController
@RequestMapping("/poi/search")
public class GaoDePoiController {

    @Resource
    private GaoDeEnterpriseMapRemoteClient gaoDeEnterpriseMapRemoteClient;

    @Resource
    private GeoFenceClient geoFenceClient;

    @ApiOperation("高德POI搜索")
    @PostMapping("/key/page")
    public Result<GDPageResult<PoiSearchKeyResponse>> searchKeyPoi(@RequestBody PoiSearchRequest request) {
        GDPageResult<PoiSearchKeyResponse> result = gaoDeEnterpriseMapRemoteClient.searchKeyPoi(request);
        return Result.success(result);
    }

    @ApiOperation("POI搜索-关键字搜索")
    @PostMapping("/poi/keyword/search")
    public Result<PoiKeywordResponse> poiKeywordSearch(@RequestBody PoiKeywordQueryParams poiKeywordQueryParams) {
        return geoFenceClient.poiKeywordSearch(poiKeywordQueryParams);
    }
}
