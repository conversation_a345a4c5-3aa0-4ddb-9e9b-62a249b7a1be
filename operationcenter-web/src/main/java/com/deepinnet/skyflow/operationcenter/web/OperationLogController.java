package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.OperationLogDTO;
import com.deepinnet.skyflow.operationcenter.dto.OperationLogQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogService;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 操作日志控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/operation/log")
@Api(tags = "操作日志管理")
@Validated
public class OperationLogController {

    @Resource
    private OperationLogService operationLogService;

    @PostMapping("/page")
    @ApiOperation("分页查询操作日志")
    public Result<CommonPage<OperationLogDTO>> pageQueryOperationLog(
            @RequestBody @Valid OperationLogQueryDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        CommonPage<OperationLogDTO> page = operationLogService.pageQueryOperationLog(queryDTO);
        return Result.success(page);
    }

    @GetMapping("/detail/{id}")
    @ApiOperation("获取操作日志详情")
    public Result<OperationLogDTO> getOperationLogDetail(
            @PathVariable @NotNull(message = "日志ID不能为空") Long id) {
        OperationLogDTO operationLogDTO = operationLogService.getOperationLogById(id);
        return Result.success(operationLogDTO);
    }
}
