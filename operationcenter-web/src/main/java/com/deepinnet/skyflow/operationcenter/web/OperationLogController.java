package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.OperationLogClient;
import com.deepinnet.infra.api.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 操作日志控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/operation/log")
@Api(tags = "操作日志管理")
@Validated
public class OperationLogController {

    @Resource
    private OperationLogClient operationLogClient;

    @PostMapping("/page")
    @ApiOperation("分页查询操作日志")
    public Result<CommonPage<OperationLogDTO>> pageQueryOperationLog(
            @RequestBody @Valid OperationLogQueryDTO queryDTO) {
        return operationLogClient.pageQueryOperationLog(queryDTO);
    }
}
