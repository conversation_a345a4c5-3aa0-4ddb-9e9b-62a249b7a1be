package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.FlightDraftDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDraftQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.draft.FlightDraftService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 飞行草稿控制器
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@RestController
@RequestMapping("/flight/draft")
@Api(tags = "飞行草稿管理")
public class FlightDraftController {

    @Resource
    private FlightDraftService flightDraftService;

    @PostMapping("/save")
    @ApiOperation("保存草稿")
    public Result<String> saveDraft(@RequestBody FlightDraftDTO flightDraftDTO) {
        String code = flightDraftService.saveDraft(flightDraftDTO);
        return Result.success(code);
    }

    @PostMapping("/update")
    @ApiOperation("更新草稿")
    public Result<Boolean> updateDraft(@RequestBody FlightDraftDTO flightDraftDTO) {
        flightDraftService.updateDraft(flightDraftDTO);
        return Result.success(Boolean.TRUE);
    }

    @GetMapping("/remove")
    @ApiOperation("删除草稿")
    public Result<Boolean> removeDraft(@RequestParam String code) {
        boolean success = flightDraftService.removeDraft(code);
        return Result.success(success);
    }

    @GetMapping("/detail")
    @ApiOperation("根据ID查询草稿")
    public Result<FlightDraftDTO> getDraftById(@RequestParam String code) {
        FlightDraftDTO result = flightDraftService.getDraft(code);
        return Result.success(result);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询草稿列表")
    public Result<CommonPage<FlightDraftDTO>> pageDrafts(@RequestBody FlightDraftQueryDTO queryDTO) {
        CommonPage<FlightDraftDTO> result = flightDraftService.pageDrafts(queryDTO);
        return Result.success(result);
    }
}