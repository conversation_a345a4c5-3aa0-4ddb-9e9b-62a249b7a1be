package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.response.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Creator zengjuerui
 * Date 2025-06-06
 **/

@RestController
@RequestMapping("/common")
public class CommonController {

    @GetMapping("/serverTime")
    @ApiOperation(value = "获取服务器时间", notes = "返回服务器当前时间戳")
    public Result<Long> getServerTime() {
        return Result.success(System.currentTimeMillis());
    }
}
