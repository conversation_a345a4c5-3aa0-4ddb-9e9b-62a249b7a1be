package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.BizStatQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.workbench.FlightStatisticsService;
import com.deepinnet.skyflow.operationcenter.vo.BizStatVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightWorkbenchCoreVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *    工作台首页统计数据接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/18
 */

@Api(tags = "[客户工作台] => 工作台首页")
@Validated
@RestController
@RequestMapping("/workbenches")
@RequiredArgsConstructor
public class FlightStatisticsController {

    private final FlightStatisticsService flightStatisticsService;

    @GetMapping("/core/view")
    @ApiOperation("[工作台首页]=>核心数据概览")
    public Result<FlightWorkbenchCoreVO> getCoreView() {
        return Result.success(flightStatisticsService.getCoreView());
    }

    @PostMapping("/range/detail")
    @ApiOperation("[工作台首页]=>业务数据明细")
    public Result<List<BizStatVO>> rangeDetail(@RequestBody BizStatQueryDTO dto) {
        return Result.success(flightStatisticsService.getRangeDetail(dto));
    }

    @PostMapping("/range/detail/export")
    @ApiOperation("[工作台首页]=>业务数据明细导出")
    public void exportRangeDetail(@RequestBody BizStatQueryDTO dto, HttpServletResponse response) {
        flightStatisticsService.exportRangeDetail(dto, response);
    }

    @GetMapping("/pending/approve")
    @ApiOperation("[工作台首页]=>待我审核审批单数量")
    public Result<Integer> pendingApprovalCount(@ApiParam(name = "type"
            , value = "业务类型 (FLIGHT_DEMAND-需求; FLIGHT_DEMAND_PLAN-飞行规划)"
            , required = true
            , allowableValues = "FLIGHT_DEMAND,FLIGHT_DEMAND_PLAN") @RequestParam(name = "type") String type) {
        return Result.success(flightStatisticsService.pendingApprovalCount(type));
    }

}
