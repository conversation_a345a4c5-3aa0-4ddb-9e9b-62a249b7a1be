package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.client.base.DictionaryItemClient;
import com.deepinnet.spatiotemporalplatform.dto.DictionaryItemBatchOperationDTO;
import com.deepinnet.spatiotemporalplatform.dto.DictionaryItemDTO;
import com.deepinnet.spatiotemporalplatform.dto.DictionaryItemQueryDTO;
import com.deepinnet.spatiotemporalplatform.dto.IdDTO;
import com.deepinnet.spatiotemporalplatform.vo.DictionaryItemVO;
import com.deepinnet.spatiotemporalplatform.vo.DictionaryTreeVO;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 字典项控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dictionary/item")
@Api(tags = "字典项管理")
@Validated
public class DictionaryItemController {

    @Resource
    private DictionaryItemClient dictionaryItemClient;

    @PostMapping("/create")
    @ApiOperation("创建字典项")
    public Result<Long> createDictionaryItem(@RequestBody @Valid DictionaryItemDTO dictionaryItemDTO) {
        dictionaryItemDTO.setTenantId(TenantIdUtil.getTenantId());
        return dictionaryItemClient.createDictionaryItem(dictionaryItemDTO);
    }

    @PostMapping("/batch-create")
    @ApiOperation("批量创建字典项")
    public Result<Boolean> batchCreateDictionaryItems(@RequestBody @Valid List<DictionaryItemDTO> dictionaryItemDTOList) {
        if (dictionaryItemDTOList != null) {
            for (DictionaryItemDTO item : dictionaryItemDTOList) {
                item.setTenantId(TenantIdUtil.getTenantId());
            }
        }
       return dictionaryItemClient.batchCreateDictionaryItems(dictionaryItemDTOList);
    }

    @PostMapping("/update")
    @ApiOperation("更新字典项")
    public Result<Boolean> updateDictionaryItem(@RequestBody @Valid DictionaryItemDTO dictionaryItemDTO) {
        dictionaryItemDTO.setTenantId(TenantIdUtil.getTenantId());
        return dictionaryItemClient.updateDictionaryItem(dictionaryItemDTO);
    }

    @PostMapping("/get")
    @ApiOperation("根据ID获取字典项")
    public Result<DictionaryItemVO> getDictionaryItemById(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                            @ApiParam(value = "字典项ID DTO", required = true) IdDTO idDTO) {
        return dictionaryItemClient.getDictionaryItemById(idDTO);
    }

    @GetMapping("/getByCode")
    @ApiOperation("根据字典ID和编码获取字典项")
    public Result<DictionaryItemVO> getDictionaryItemByCode(
            @RequestParam @Valid @NotNull(message = "字典ID不能为空")
            @ApiParam(value = "字典ID", required = true) Long dictionaryId,
            @RequestParam @Valid @NotNull(message = "字典项编码不能为空")
            @ApiParam(value = "字典项编码", required = true) String code) {
        return dictionaryItemClient.getDictionaryItemByCode(dictionaryId, code);
    }

    @GetMapping("/listByDictionaryId")
    @ApiOperation("根据字典ID获取字典项列表")
    public Result<List<DictionaryItemVO>> listDictionaryItemsByDictionaryId(
            @RequestParam @Valid @NotNull(message = "字典ID不能为空")
            @ApiParam(value = "字典ID", required = true) Long dictionaryId) {
        return dictionaryItemClient.listDictionaryItemsByDictionaryId(dictionaryId);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询字典项")
    public Result<CommonPage<DictionaryItemVO>> pageQueryDictionaryItems(@RequestBody @Valid DictionaryItemQueryDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        return dictionaryItemClient.pageQueryDictionaryItems(queryDTO);
    }

    @PostMapping("/delete")
    @ApiOperation("删除字典项")
    public Result<Boolean> deleteDictionaryItem(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                        @ApiParam(value = "字典项ID DTO", required = true) IdDTO idDTO) {
        return dictionaryItemClient.deleteDictionaryItem(idDTO);
    }

    @GetMapping("/tree")
    @ApiOperation("根据字典类型和租户ID获取字典树")
    public Result<List<DictionaryTreeVO>> getDictionaryTreeByTypeAndTenantId(
            @RequestParam @Valid @NotBlank(message = "字典类型不能为空")
            @ApiParam(value = "字典类型", required = true) String type) {
        return dictionaryItemClient.getDictionaryTreeByTypeAndTenantId(type, TenantIdUtil.getTenantId());
    }

    @GetMapping("/listByTypeAndParentCode")
    @ApiOperation("根据租户ID、字典类型和父编码查询字典项列表")
    public Result<List<DictionaryItemVO>> listDictionaryItemsByTypeAndParentCode(
            @RequestParam @Valid @NotBlank(message = "字典类型不能为空")
            @ApiParam(value = "字典类型", required = true) String type,
            @RequestParam(required = false)
            @ApiParam(value = "父编码", required = true) String parentCode) {
        return dictionaryItemClient.listDictionaryItemsByTypeAndParentCode(type, parentCode, TenantIdUtil.getTenantId());
    }

    @PostMapping("/batch-operation")
    @ApiOperation("批量操作字典项（删除和新增）")
    public Result<Boolean> batchOperateDictionaryItems(@RequestBody @Valid DictionaryItemBatchOperationDTO batchOperationDTO) {
        return dictionaryItemClient.batchOperateDictionaryItems(batchOperationDTO);
    }
}