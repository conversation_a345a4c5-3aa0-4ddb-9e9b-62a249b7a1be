package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.ApprovalClient;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.vo.ApprovalVO;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 审核控制器
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
@Api(tags = "审核管理")
@RestController
@RequestMapping("/approval")
public class ApprovalController {

    @Resource
    private ApprovalClient approvalClient;

    @ApiOperation("分页查询审核记录")
    @PostMapping("/page")
    public Result<CommonPage<ApprovalVO>> pageQuery(@RequestBody @Valid ApprovalQueryDTO queryDTO) {
        return approvalClient.pageQuery(queryDTO);
    }

    @ApiOperation("获取审核记录详情")
    @GetMapping("/detail")
    public Result<ApprovalDetailDTO> getDetail(@ApiParam(value = "申请ID", required = true) String approvalId) {
        return approvalClient.getDetail(approvalId);
    }

    @ApiOperation("提交审核处理")
    @PostMapping("/process")
    public Result<Boolean> process(@RequestBody @Valid ApprovalProcessDTO processDTO) {
        return approvalClient.process(processDTO);
    }
} 