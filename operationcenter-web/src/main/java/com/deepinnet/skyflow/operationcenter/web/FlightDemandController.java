package com.deepinnet.skyflow.operationcenter.web;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.DataAccessDTO;
import com.deepinnet.skyflow.operationcenter.biz.FlightDemandBizManager;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandAssignServiceProviderWayEnum;
import com.deepinnet.skyflow.operationcenter.service.approval.instance.ApproveConvert;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.skyflow.operationcenter.service.demand.FlightDemandService;
import com.deepinnet.skyflow.operationcenter.service.norepeat.BizKeyType;
import com.deepinnet.skyflow.operationcenter.service.norepeat.NoRepeatSubmit;
import com.deepinnet.skyflow.operationcenter.service.norepeat.RepeatMode;
import com.deepinnet.skyflow.operationcenter.service.task.FlightDemandMatchTask;
import com.deepinnet.skyflow.operationcenter.service.task.FlightDemandMergeTask;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR> wong
 * @create 2024/11/27 11:39
 * @Description
 */
@RestController
@Api(tags = "飞行需求管理")
@Validated
@RequestMapping("/demand")
public class FlightDemandController {

    @Resource
    private FlightDemandBizManager demandBizManager;

    @Resource
    private FlightDemandService demandService;

    @Resource
    private FlightDemandMergeTask flightDemandMergeTask;

    @Resource
    private FlightDemandMatchTask flightDemandMatchTask;

    @Resource
    private ThreadPoolExecutor commonTaskExecutor;

    @Resource
    private UserRemoteClient userRemoteClient;

    @Resource
    private ApproveConvert approveConvert;

    @PostMapping("/create")
    @ApiOperation("创建飞行需求")
    public Result<String> createFlightDemand(@RequestBody @Valid FlightDemandDTO flightDemandDTO) {
        String demandCode = demandBizManager.saveFlightDemand(flightDemandDTO);
        return Result.success(demandCode);
    }

    @GetMapping("/validate/allow/edit")
    @ApiOperation("校验是否允许修改需求")
    public Result<Boolean> validateAllowEditDemand(@RequestParam String demandNo) {
        demandService.validateAllowEditDemand(demandNo, UserUtil.getUserNo());
        return Result.success(Boolean.TRUE);
    }

    @NoRepeatSubmit(mode = RepeatMode.PARAM_HASH,
            bizKeys = {@NoRepeatSubmit.BizKey(type = BizKeyType.QUERY_USER_ID)})
    @PostMapping("/edit")
    @ApiOperation("编辑飞行需求")
    public Result<String> editFlightDemand(@RequestBody @Valid FlightDemandDTO flightDemandDTO) {
        String demandCode = demandBizManager.editDemand(flightDemandDTO);
        return Result.success(demandCode);
    }

    @PostMapping("/draft/publish")
    @ApiOperation("发布草稿")
    public Result<Boolean> publishDraft(@RequestBody FlightDraftDTO flightDraftDTO) {
        flightDraftDTO.setTenantId(TenantIdUtil.getTenantId());
        demandBizManager.publishDraft(flightDraftDTO);
        return Result.success(Boolean.TRUE);
    }

    @GetMapping("/pass/get")
    @ApiOperation("后台接口-根据需求编号获取需求详情")
    public Result<FlightDemandVO> getPassFlightDemandByNo(@RequestParam @NotBlank(message = "需求编号不能为空")
                                                              @ApiParam(value = "需求编号", required = true) String demandNo) {
        FlightDemandVO flightDemandVO = demandBizManager.getPassFlightDemandByNo(demandNo);
        return Result.success(flightDemandVO);
    }


    @PostMapping("/pass/list")
    @ApiOperation("后台接口-根据需求编号获取需求详情列表")
    public Result<List<FlightDemandVO>> getPassFlightDemandByNoList(@RequestBody @Valid FlightDemandQueryDTO queryDTO) {
        List<FlightDemandVO> flightDemandVO = demandBizManager.getPassFlightDemandByNoList(queryDTO.getDemandNoList());
        return Result.success(flightDemandVO);
    }

    @GetMapping("/customer/get")
    @ApiOperation("客户平台-根据需求编号获取需求详情")
    public Result<FlightDemandVO> getCustomerFlightDemandByNo(@RequestParam @NotBlank(message = "需求编号不能为空")
                                                      @ApiParam(value = "需求编号", required = true) String demandNo) {
        FlightDemandVO flightDemandVO = demandBizManager.getCustomerFlightDemandByNo(demandNo);
        return Result.success(flightDemandVO);
    }

    @GetMapping("/op/get")
    @ApiOperation("服务平台、运营管理平台-根据需求编号获取需求详情")
    public Result<FlightDemandVO> getOpFlightDemandByNo(@RequestParam @NotBlank(message = "需求编号不能为空")
                                                      @ApiParam(value = "需求编号", required = true) String demandNo) {
        FlightDemandVO flightDemandVO = demandBizManager.getOpFlightDemandByNo(demandNo);
        return Result.success(flightDemandVO);
    }

    @PostMapping("/page/customer/matchmaking")
    @ApiOperation("客户平台-撮合场景-分页查询飞行需求")
    public Result<CommonPage<FlightDemandDTO>> pageQueryFlightDemandCustomerMatchmaking(@RequestBody @Valid FlightDemandQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();
        String userNo = UserUtil.getUserNo();
        queryDTO.setTenantId(tenantId);
        queryDTO.setPublisherNoList(ListUtil.toList(userNo));
        queryDTO.setCurUserCode(userNo);
        CommonPage<FlightDemandDTO> page = demandBizManager.pageQueryCustomerFlightDemand(queryDTO);
        return Result.success(page);
    }

    @PostMapping("/page/customer/onenet")
    @ApiOperation("客户平台-一网统飞场景-分页查询飞行需求")
    public Result<CommonPage<FlightDemandDTO>> pageQueryFlightDemandCustomerOneNet(@RequestBody @Valid FlightDemandQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();
        queryDTO.setTenantId(tenantId);
        DataAccessDTO availableQueryData = userRemoteClient.getAvailableQueryData();
        List<String> supportQueryUserNos = availableQueryData.getSupportQueryUserNos();
        if (CollectionUtil.isEmpty(supportQueryUserNos)) {
            return Result.success(CommonPage.buildEmptyPage());
        } else if (supportQueryUserNos.size() == 1 && Objects.equals(DataScopeConstants.SUPER_ADMIN, supportQueryUserNos.get(0))) {
            //管理员账号不需要传递用户编号
        } else {
            queryDTO.setPublisherNoList(supportQueryUserNos);
        }
        String userNo = UserUtil.getUserNo();
        queryDTO.setCurUserCode(userNo);
        CommonPage<FlightDemandDTO> page = demandBizManager.pageQueryCustomerFlightDemand(queryDTO);
        return Result.success(page);
    }

    @PostMapping("/page/service")
    @ApiOperation("服务平台-分页查询飞行需求")
    public Result<CommonPage<FlightDemandDTO>> pageQueryFlightDemandService(@RequestBody @Valid FlightDemandQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();
        String userNo = UserUtil.getUserNo();
        queryDTO.setTenantId(tenantId);
        queryDTO.setServiceProviderNo(userNo);
        CommonPage<FlightDemandDTO> page = demandBizManager.pageQueryOpFlightDemand(queryDTO);
        return Result.success(page);
    }

    @PostMapping("/page/op/manage")
    @ApiOperation("运营管理平台-分页查询飞行需求")
    public Result<CommonPage<FlightDemandDTO>> pageQueryFlightDemandOpManage(@RequestBody @Valid FlightDemandQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();
        queryDTO.setTenantId(tenantId);
        CommonPage<FlightDemandDTO> page = demandBizManager.pageQueryOpFlightDemand(queryDTO);
        return Result.success(page);
    }

    @ApiOperation(value = "[飞行接口] => 根据计划id查询需求")
    @GetMapping("/get/by/plan/id")
    public Result<FlightDemandDTO> getFlightDemandByPlanId(@RequestParam String planId) {
        return Result.success(demandBizManager.getFlightDemandByPlanId(planId));
    }

    @ApiOperation(value = "[飞行接口] => 分配服务商")
    @PostMapping("/assign/service/provider")
    public Result<Boolean> assignServiceProvider(@RequestBody FlightDemandAssignDTO dto) {
        dto.setAssignWay(FlightDemandAssignServiceProviderWayEnum.USER);
        dto.setAssignOperatorUserNo(UserUtil.getUserNo());
        dto.setAssignTime(LocalDateTime.now());
        dto.setTenantId(TenantIdUtil.getTenantId());
        return Result.success(demandBizManager.assignServiceProvider(dto));
    }

    @ApiOperation("检查服务商和需求是否匹配")
    @GetMapping("/check/service/provider")
    public Result<Boolean> checkMatchServiceProvider(@RequestParam("demandCode") String demandCode, @RequestParam("serviceProviderNo") String serviceProviderNo) {
        return Result.success(demandService.checkMatchServiceProvider(demandCode, serviceProviderNo));
    }

    @ApiOperation("近30天飞行计划统计")
    @GetMapping("/flightDemand30DayStatistics")
    public Result<FlightDemandStatsDTO> getFlightDemand90DayStatistics() {

        return Result.success(demandBizManager.get90DayStatistics());
    }

    @ApiOperation("飞行计划统计")
    @PostMapping("/flightDemandStatistics")
    public Result<FlightDemandStatsDTO> getFlightDemandDayStatistics(@RequestBody FlightDemandStatsQueryDTO queryDTO) {
        return Result.success(demandBizManager.getStatistics(queryDTO));
    }

    @ApiOperation("近7天飞行计划数量统计")
    @GetMapping("/flightDemand7DayStatistics")
    public Result<List<FlightDemandDayStatsDTO>> getFlightDemand7DayStatistics() {
        return Result.success(demandBizManager.get7DayStatistics(null));
    }

    @ApiOperation("近7天飞行计划数量统计v2")
    @PostMapping("/v2/flightDemand7DayStatistics")
    public Result<List<FlightDemandDayStatsDTO>> getFlightDemand7DayStatisticsV2(@RequestBody FlightDemandStatsQueryDTO queryDTO) {
        return Result.success(demandBizManager.get7DayStatistics(queryDTO));
    }

    @GetMapping("/planDetail")
    @ApiOperation("获取需求计划详情")
    public Result<FlightDemandPlanDTO> getDemandPlanDetail(@RequestParam(value = "planId") String planId) {
        return Result.success(demandBizManager.getDemandPlanDetail(planId));
    }

    @PostMapping("/merge/query/groups")
    @ApiOperation("分页查询合并处理分组统计")
    public Result<MergeHandleGroupAdvice> queryMergeHandleGroups(@RequestBody @Validated MergeHandleQueryDTO queryDTO) {
        String tenantId = TenantIdUtil.getTenantId();
        queryDTO.setTenantId(tenantId);
        return Result.success(demandService.queryMergeHandleGroups(queryDTO));
    }

    @GetMapping("/merge/detail")
    @ApiOperation("根据合并处理编号获取详情")
    public Result<MergeHandleDetailDTO> getMergeHandleDetail(@RequestParam @NotBlank(message = "合并处理编号不能为空")
                                                             @ApiParam(value = "合并处理编号", required = true) String mergeHandleCode) {
        MergeHandleDetailDTO detail = demandService.getMergeHandleDetail(mergeHandleCode);
        return Result.success(detail);
    }

    @PostMapping("/merge/execute")
    @ApiOperation("执行合并操作")
    public Result<String> executeMerge(@RequestBody @Validated ExecuteMergeDTO executeMergeDTO) {
        executeMergeDTO.setOperatorUserNo(UserUtil.getUserNo());
        executeMergeDTO.setOperatorUserName(UserUtil.getUserName());
        String mergeDemandCode = demandService.executeMerge(executeMergeDTO);
        return Result.success(mergeDemandCode);
    }

    @PostMapping("/merge/cancel")
    @ApiOperation("取消合并操作")
    public Result<Boolean> cancelMerge(@RequestBody @Validated CancelMergeDTO cancelMergeDTO) {
        cancelMergeDTO.setOperatorUserNo(UserUtil.getUserNo());
        cancelMergeDTO.setOperatorUserName(UserUtil.getUserName());
        Boolean result = demandService.cancelMerge(cancelMergeDTO);
        return Result.success(result);
    }

    @ApiOperation("提交审核")
    @PostMapping("/approve")
    public Result<Boolean> approveDemand(@RequestBody ApproveSubmitDTO submitDTO) {
        submitDTO.setApproveUserId(UserUtil.getUserNo());
        demandService.approveDemand(approveConvert.toApprovalEntity(submitDTO));
        return Result.success(Boolean.TRUE);
    }

    @GetMapping("/create/merge/demand/task")
    @ApiOperation("创建合并需求处理任务")
    public Result<Boolean> createMergeDemandTask() {
        commonTaskExecutor.submit(() -> {
            flightDemandMergeTask.execute();
        });
        return Result.success(Boolean.TRUE);
    }

    @GetMapping("/create/demand/match/task")
    @ApiOperation("创建需求匹配任务")
    public Result<Boolean> createDemandMatchTask() {
        commonTaskExecutor.submit(() -> {
            flightDemandMatchTask.execute();
        });
        return Result.success(Boolean.TRUE);
    }
}
