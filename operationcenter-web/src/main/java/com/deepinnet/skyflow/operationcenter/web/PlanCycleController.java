package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.FlightPlanCycleDTO;
import com.deepinnet.skyflow.operationcenter.dto.PageQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.PlanCycleQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.order.FlightCycleService;
import com.deepinnet.skyflow.operationcenter.vo.FlightPlanCycleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *    周期计划
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/11
 */

@Api(tags = "[周期计划] => 周期计划")
@RestController
@RequestMapping("/cycle/plan")
@RequiredArgsConstructor
@Validated
public class PlanCycleController {

    private final FlightCycleService flightCycleService;

    @PostMapping("/list")
    @ApiOperation("[周期计划]=>列表")
    public Result<CommonPage<FlightPlanCycleVO>> list(@RequestBody PlanCycleQueryDTO dto) {
        return Result.success(flightCycleService.queryList(dto));
    }

    @GetMapping("/detail")
    @ApiOperation(value = "[周期计划]=>详情")
    public Result<FlightPlanCycleVO> detail(@RequestParam(value = "cycleNo") String cycleNo) {
        return Result.success(flightCycleService.queryDetail(cycleNo));
    }

    @PostMapping("/save")
    @ApiOperation(value = "[周期计划]=>新增")
    public Result<Boolean> save(@RequestBody FlightPlanCycleDTO dto) {
        return Result.success(flightCycleService.save(dto));
    }

    @PostMapping("/update")
    @ApiOperation(value = "[周期计划]=>更新")
    public Result<Boolean> update(@RequestBody FlightPlanCycleDTO dto) {
        return Result.success(flightCycleService.update(dto));
    }

    @GetMapping("/delete")
    @ApiOperation(value = "[周期计划]=>删除周期计划")
    public Result<Boolean> delete(@RequestParam(value = "cycleNo") String cycleNo) {
        return Result.success(flightCycleService.delete(cycleNo));
    }

}
