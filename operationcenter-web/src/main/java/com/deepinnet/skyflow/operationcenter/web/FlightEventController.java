package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsStatWebQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightEventsWebQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.user.UserService;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.spatiotemporalplatform.client.skyflow.FlightEventClient;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsQueryDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsStatDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsStatQueryDTO;
import com.deepinnet.spatiotemporalplatform.enums.FlightEventTypeEnum;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/27 11:39
 * @Description
 */
@RestController
@Api(tags = "飞行任务事件管理")
@Validated
@RequestMapping("/event")
public class FlightEventController {

    @Resource
    private FlightEventClient flightEventClient;
    @Resource
    private UserService userService;

    /**
     * 保存飞行事件
     *
     * @param flightEventsDTO 飞行事件DTO
     * @return 事件ID
     */
    @ApiOperation(value = "[飞行事件接口] => 保存飞行事件")
    @PostMapping("/save")
    public Result<String> saveEvent(@RequestBody @Valid FlightEventsDTO flightEventsDTO) {
        flightEventsDTO.setTenantId(TenantIdUtil.getTenantId());
        return flightEventClient.saveEvent(flightEventsDTO);
    }

    /**
     * 批量保存飞行事件
     *
     * @param flightEventsDTOList 飞行事件DTO列表
     * @return 是否保存成功
     */
    @ApiOperation(value = "[飞行事件接口] => 批量保存飞行事件")
    @PostMapping("/batch/save")
    public Result<Boolean> saveEventBatch(@RequestBody @Valid List<FlightEventsDTO> flightEventsDTOList) {
        String tenantId = TenantIdUtil.getTenantId();
        flightEventsDTOList.forEach(item -> item.setTenantId(tenantId));
        return flightEventClient.saveEventBatch(flightEventsDTOList);
    }

    /**
     * 根据ID查询飞行事件
     *
     * @param eventId 事件ID
     * @return 飞行事件DTO
     */
    @ApiOperation(value = "[飞行事件接口] => 根据ID查询飞行事件")
    @PostMapping("/detail")
    public Result<FlightEventsDTO> getEventById(@RequestParam("eventId") String eventId) {
        return flightEventClient.getEventById(eventId);
    }

    /**
     * 分页查询飞行事件
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @ApiOperation(value = "[飞行事件接口] => 分页查询飞行事件")
    @PostMapping("/page")
    public Result<CommonPage<FlightEventsDTO>> pageQuery(@RequestBody @Valid FlightEventsQueryDTO queryDTO) {
        queryDTO.setUserNo(UserUtil.getUserNo());
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        queryDTO.setDemandStartTime(LocalDateTime.now().minusMonths(3));
        queryDTO.setDemandEndTime(LocalDateTime.now());
        return flightEventClient.pageQuery(queryDTO);
    }

    @ApiOperation(value = "[飞行事件接口] => 分页查询飞行事件 v2")
    @PostMapping("/v2/page")
    public Result<CommonPage<FlightEventsDTO>> pageQueryV2(@RequestBody @Valid FlightEventsWebQueryDTO query) {


        FlightEventsQueryDTO queryDTO = new FlightEventsQueryDTO();
        queryDTO.setUserNoList(userService.getAvailableUserNoByDepartmentId(query.getDepartmentId()));
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        queryDTO.setDemandStartTime(LocalDateTime.now().minusMonths(3));
        queryDTO.setDemandEndTime(LocalDateTime.now());
        queryDTO.setPageSize(200);
        return flightEventClient.pageQuery(queryDTO);
    }

    /**
     * 根据飞行任务ID查询飞行事件列表
     *
     * @param queryDTO 飞行任务ID
     * @return 飞行事件DTO列表
     */
    @ApiOperation(value = "[飞行事件接口] => 根据飞行任务ID查询飞行事件列表")
    @PostMapping("/list/by/task")
    public Result<List<FlightEventsDTO>> queryListByFlightTaskCode(@RequestBody FlightEventsQueryDTO queryDTO) {
        return flightEventClient.queryListByFlightTaskCode(queryDTO);
    }

    /**
     * 查询飞行事件统计
     *
     * @param queryDTO 查询条件
     * @return 飞行事件统计数据
     */
    @ApiOperation(value = "[飞行事件接口] => 查询飞行事件统计")
    @PostMapping("/stat")
    public Result<List<FlightEventsStatDTO>> queryFlightEventsStat(@RequestBody @Valid FlightEventsStatQueryDTO queryDTO) {
        queryDTO.setUserNo(UserUtil.getUserNo());
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        Result<List<FlightEventsStatDTO>> listResult = flightEventClient.queryFlightEventsStat(queryDTO);
        if (listResult.isSuccess()) {
            listResult.getData().forEach(item -> item.setEventName(FlightEventTypeEnum.valueOf(item.getEventType()).getName()));
        }
        return listResult;
    }

    @ApiOperation(value = "[飞行事件接口] => 查询飞行事件统计v2")
    @PostMapping("/v2/stat")
    public Result<List<FlightEventsStatDTO>> queryFlightEventsStatV2(@RequestBody @Valid FlightEventsStatWebQueryDTO queryDTO) {

        FlightEventsStatQueryDTO statQueryDTO = new FlightEventsStatQueryDTO();
        statQueryDTO.setUserNoList(userService.getAvailableUserNoByDepartmentId(queryDTO.getDepartmentId()));
        statQueryDTO.setTenantId(TenantIdUtil.getTenantId());
        statQueryDTO.setStartTime(queryDTO.getStartTime());
        statQueryDTO.setDemandType(queryDTO.getDemandType());
        Result<List<FlightEventsStatDTO>> listResult = flightEventClient.queryFlightEventsStat(statQueryDTO);
        if (listResult.isSuccess()) {
            listResult.getData().forEach(item -> item.setEventName(FlightEventTypeEnum.valueOf(item.getEventType()).getName()));
        }
        return listResult;
    }

    @ApiOperation(value = "[飞行事件接口] => 查询计划中所有飞行事件")
    @GetMapping("/list/by/planId")
    public Result<List<FlightEventsDTO>> queryListFlightEventByPlanId(@RequestParam("planId") String planId) {
        String json = "[\n" +
                "\t{\"algorithmEventId\":\"test_flight_event_sf_3e0c48bf2bab4440b638c185b252a27f\",\"eventType\":\"PARKING_VIOLATION\",\"eventName\":\"违法掉头\",\"description\":\"检测到违法掉头事件\",\"eventLocation\":\"深圳市南山区科技园\",\"duration\":1,\"status\":\"ONGOING\",\"licensePlate\":null,\"evidenceImages\":null,\"evidenceVideos\":null,\"flightTaskCode\":\"test_PLAN_17cb8f31248546e2a93d5546ace48295\",\"tenantId\":\"sz_unifly\",\"eventStartTime\":\"2025-06-05T12:30:00\",\"eventEndTime\":null,\"id\":1,\"gmtCreated\":\"2025-06-07T20:22:13\",\"gmtModified\":\"2025-06-07T20:22:13\",\"algorithmEventGmtModified\":\"2025-06-11T17:10:39\"}\t" +
                "\t,{\"algorithmEventId\":\"test_flight_event_sf_61e4e06a40c04994a315c34ead9d0d7c\",\"eventType\":\"PARKING_VIOLATION\",\"eventName\":\"道路积水\",\"description\":\"检测到道路积水事件\",\"eventLocation\":\"深圳市南山区科技园\",\"duration\":1,\"status\":\"ONGOING\",\"licensePlate\":null,\"evidenceImages\":null,\"evidenceVideos\":null,\"flightTaskCode\":\"test_PLAN_17cb8f31248546e2a93d5546ace48295\",\"tenantId\":\"sz_unifly\",\"eventStartTime\":\"2025-06-05T11:30:00\",\"eventEndTime\":null,\"id\":2,\"gmtCreated\":\"2025-06-07T20:22:13\",\"gmtModified\":\"2025-06-07T20:22:13\",\"algorithmEventGmtModified\":\"2025-06-11T17:10:39\"}\n" +
                "\t,{\"algorithmEventId\":\"test_flight_event_sf_76043f76a69a4218a42a6bc6d445e973\",\"eventType\":\"PARKING_VIOLATION\",\"eventName\":\"压实线\",\"description\":\"检测到压实线事件\",\"eventLocation\":\"深圳市南山区科技园\",\"duration\":1,\"status\":\"ONGOING\",\"licensePlate\":null,\"evidenceImages\":null,\"evidenceVideos\":null,\"flightTaskCode\":\"test_PLAN_17cb8f31248546e2a93d5546ace48295\",\"tenantId\":\"sz_unifly\",\"eventStartTime\":\"2025-06-05T10:30:00\",\"eventEndTime\":null,\"id\":7,\"gmtCreated\":\"2025-06-07T20:22:13\",\"gmtModified\":\"2025-06-07T20:22:13\",\"algorithmEventGmtModified\":\"2025-06-11T17:10:39\"}\n" +
                "\t,{\"algorithmEventId\":\"test_flight_event_sf_8103247c34e04416ae2e368f3bcb4aad\",\"eventType\":\"PARKING_VIOLATION\",\"eventName\":\"车辆违停\",\"description\":\"检测到车辆违停事件\",\"eventLocation\":\"深圳市南山区科技园\",\"duration\":1,\"status\":\"ONGOING\",\"licensePlate\":null,\"evidenceImages\":null,\"evidenceVideos\":null,\"flightTaskCode\":\"test_PLAN_17cb8f31248546e2a93d5546ace48295\",\"tenantId\":\"sz_unifly\",\"eventStartTime\":\"2025-06-05T09:30:00\",\"eventEndTime\":null,\"id\":8,\"gmtCreated\":\"2025-06-07T20:22:13\",\"gmtModified\":\"2025-06-07T20:22:13\",\"algorithmEventGmtModified\":\"2025-06-11T17:10:39\"}\n" +
                "\t,{\"algorithmEventId\":\"test_flight_event_sf_532d356cba7b4b4281d9994423cd9cd8\",\"eventType\":\"PARKING_VIOLATION\",\"eventName\":\"闯红灯\",\"description\":\"检测到闯红灯事件\",\"eventLocation\":\"深圳市南山区科技园\",\"duration\":1,\"status\":\"ONGOING\",\"licensePlate\":null,\"evidenceImages\":null,\"evidenceVideos\":null,\"flightTaskCode\":\"test_PLAN_17cb8f31248546e2a93d5546ace48295\",\"tenantId\":\"sz_unifly\",\"eventStartTime\":\"2025-06-05T08:30:00\",\"eventEndTime\":null,\"id\":9,\"gmtCreated\":\"2025-06-07T20:22:13\",\"gmtModified\":\"2025-06-07T20:22:13\",\"algorithmEventGmtModified\":\"2025-06-11T17:10:39\"}\n" +
                "]";
        List<FlightEventsDTO> flightEventsDTOList = JsonUtil.parseToList(json, FlightEventsDTO.class);
        return Result.success(flightEventsDTOList);
    }
}