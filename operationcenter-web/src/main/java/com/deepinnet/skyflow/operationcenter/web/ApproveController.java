package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.ApprovalConfigRuleQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.ApproveSubmitDTO;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveInstanceService;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveRuleService;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalInstanceConfigRule;
import com.deepinnet.skyflow.operationcenter.service.approval.instance.ApproveConvert;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.vo.ApprovalConfigRuleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * Description:
 * Date: 2025/7/25
 * Author: lijunheng
 */
@Api(tags = "新-审核管理")
@RestController
@RequestMapping("/approve")
public class ApproveController {

    @Resource
    private ApproveInstanceService approveInstanceService;

    @Resource
    private ApproveRuleService approveRuleService;

    @Resource
    private ApproveConvert approveConvert;

    @ApiOperation("提交审核")
    @PostMapping("/submit")
    public Result<Boolean> submit(@RequestBody ApproveSubmitDTO submitDTO) {
        submitDTO.setApproveUserId(UserUtil.getUserNo());
        approveInstanceService.doApprove(approveConvert.toApprovalEntity(submitDTO));
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/rule/create")
    @ApiOperation("创建审批规则")
    public Result<String> createApproveRule(@RequestBody ApprovalInstanceConfigRule approvalInstanceConfigRule) {
        String approveRuleCode = approveRuleService.createApproveRule(approvalInstanceConfigRule);
        return Result.success(approveRuleCode);
    }

    @GetMapping("/rule/detail")
    @ApiOperation("根据编码查询审批规则")
    public Result<ApprovalConfigRuleVO> getApprovalRule(@ApiParam("规则编码") @RequestParam String code) {
        ApprovalInstanceConfigRule approveRule = approveRuleService.getApproveRule(code);
        //todo 这里对象枚举需要转换
        return Result.success(approveConvert.toVO(approveRule));
    }

    @GetMapping("/rule/list")
    @ApiOperation("分页查询审批规则")
    public Result<CommonPage<ApprovalConfigRuleVO>> listApprovalRules(@RequestBody ApprovalConfigRuleQueryDTO queryDTO) {
        CommonPage<ApprovalInstanceConfigRule> commonPage = approveRuleService.getApprovalConfigRuleList(queryDTO);
        CommonPage<ApprovalConfigRuleVO> result = CommonPage.copyMetaWithNewData(commonPage, approveConvert.toVOList(commonPage.getList()));
        return Result.success(result);
    }
}
