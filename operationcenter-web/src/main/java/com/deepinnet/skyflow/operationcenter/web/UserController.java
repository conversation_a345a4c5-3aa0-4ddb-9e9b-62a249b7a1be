package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.UserClient;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.skyflow.operationcenter.service.user.UserService;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/4/19 15:20
 * @Description
 */

@RestController
@RequestMapping("/user")
@Api(tags = "用户服务")
@Validated
public class UserController {

    @Resource
    private UserClient userClient;

    @Resource
    private UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @ApiOperation("用户注册")
    public Result<Boolean> register(@Valid @RequestBody UserRegisterDTO registerDTO) {
        return userClient.register(registerDTO);
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @ApiOperation("用户登录")
    public Result<UserLoginSuccessDTO> login(@Valid @RequestBody UserLoginDTO loginDTO) {
        return userClient.login(loginDTO);
    }

    /**
     * 获取用户信息
     */
    @PostMapping("/info")
    @ApiOperation("获取用户信息")
    public Result<UserInfoDTO> getAdminInfo() {
        return userClient.getAdminInfo();
    }

    /**
     * 修改用户密码
     */
    @PostMapping("/password")
    @ApiOperation("修改用户密码")
    public Result<Boolean> changePassword(@Valid @RequestBody UserPasswordChangeDTO passwordChangeDTO) {
        return userClient.changePassword(passwordChangeDTO);
    }

    /**
     * 忘记用户密码
     */
    @PostMapping("/password/forget")
    @ApiOperation("忘记用户密码")
    public Result<Boolean> forgetPassword(@Valid @RequestBody UserPasswordForgetDTO passwordForgetDTO) {
        return userClient.forgetPassword(passwordForgetDTO);
    }


    /**
     * 校验用户信息是否完整
     */
    @PostMapping("/checkInfo")
    @ApiOperation("校验用户信息是否完整")
    public Result<Boolean> checkUserInfoStatus() {
        return Result.success(userService.checkUserInfoStatus());
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @ApiOperation("用户登出")
    public Result<Boolean> logout() {
        return userClient.logout();
    }

    /**
     * 根据用户名模糊查询用户信息
     */
    @PostMapping("/query")
    @ApiOperation("根据用户名模糊查询用户信息")
    public Result<List<UserDetailDTO>> getUserDetailList(@RequestBody UserQueryDTO queryDTO) {
        return userClient.getUserDetailList(queryDTO);
    }

    /**
     * 获取用户绑定的部门
     */
    @PostMapping("/department")
    @ApiOperation("获取用户绑定的部门")
    public Result<List<SimpleDepartmentDTO>> getUserDetailList() {
        return userClient.getUserDepartments();
    }

    /**
     * 获取当前用户可见的数据内容
     */
    @PostMapping("/data/access")
    @ApiOperation("获取当前用户可见的数据内容")
    public Result<DataAccessDTO> getAvailableQueryData() {
        return userClient.getAvailableQueryData();
    }

    @GetMapping("/account/initialized")
    @ApiOperation("检查当前用户密码状态（是否已完成初始化）")
    public Result<Boolean> checkUserInitializedStatus() {
        return userClient.checkUserInitializedStatus();
    }

    /**
     * 获取当前用户可见的组织树内容
     */
    @ApiOperation("获取当前用户可见的树形数据内容")
    @PostMapping("/data/accessTree")
    public Result<List<DataScopeTreeNodeDTO>> getAvailableDataScopeTree() {
        return userClient.getAvailableDataScopeTree();
    }

    @ApiOperation("获取当前用户部门下的可见的数据内容")
    @GetMapping("/data/department/access")
    public Result<List<String>> getAvailableDataOnDepartment(@RequestParam(value = "departmentId", required = false) Long departmentId) {
        return Result.success(userService.getAvailableUserNoByDepartmentId(departmentId));
    }
}
