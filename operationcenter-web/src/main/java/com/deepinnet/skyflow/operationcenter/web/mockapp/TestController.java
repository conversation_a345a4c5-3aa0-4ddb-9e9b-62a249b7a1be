package com.deepinnet.skyflow.operationcenter.web.mockapp;

import cn.hutool.core.collection.ListUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.infra.api.dto.DepartmentFlatDTO;
import com.deepinnet.infra.api.dto.UserDetailDTO;
import com.deepinnet.infra.api.dto.UserRootDepartmentDTO;
import com.deepinnet.infra.api.dto.UserRootDepartmentQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.service.client.FlightDepartmentClient;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.spatiotemporalplatform.TestClient;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/4/12 11:21
 * @Description
 */
@RestController
@RequestMapping("/stpf/test")
public class TestController {

    @Resource
    private TestClient testClient;

    @Resource
    private UserRemoteClient userClient;

    @Autowired
    private FlightDepartmentClient departmentClient;

    @PostMapping("/weather/task")
    public Result<Boolean> pageQueryIntelligenceList(String weatherData) {
        return testClient.pageQueryIntelligenceList(weatherData);
    }


    @PostMapping("/task")
    @ApiOperation(value = "测试定时任务")
    public Result<Boolean> testTask() {
        return testClient.testTask();
    }

    @PostMapping("/risk/task")
    @ApiOperation(value = "测试风险预警定时任务")
    public Result<Boolean> testRiskWarningTask() {
        return testClient.testRiskWarningTask();
    }

    @GetMapping("/live_test/start")
    @ApiOperation(value = "算法直播测试")
    public Result<String> liveTest(@RequestParam(value = "planId") String planId
            ,@RequestParam(value = "url") String url, @RequestParam("flightId") String flightId) {
        return testClient.liveTest(planId, url, flightId);
    }

    @GetMapping("/live_test/end")
    @ApiOperation(value = "算法直播测试")
    public Result<String> liveTestEnd(@RequestParam(value = "planId") String planId, @RequestParam("flightId") String flightId) {
        return testClient.liveTestEnd(planId, flightId);
    }

    @GetMapping("report")
    @ApiOperation(value = "生成飞行报告")
    public Result<String> generateFlightReport(@RequestParam(value = "planId") String planId) {
        return testClient.generateFlightReport(planId);
    }

    @GetMapping("/query/department")
    @ApiOperation(value = "测试查询部门")
    public FlightDemandDTO testQueryDepartment(@RequestParam("userNo") String userNo) {
        FlightDemandDTO mergeDemand = new FlightDemandDTO();
//        UserRootDepartmentQueryDTO queryDTO = new UserRootDepartmentQueryDTO();
//        queryDTO.setUserNos(ListUtil.toList(userNo));
//        List<UserRootDepartmentDTO> userRootDepartmentList = departmentClient.getUserRootDepartments(queryDTO);
//        DepartmentFlatDTO departmentDTO = userRootDepartmentList.get(0).getDepartmentDTO();
//        mergeDemand.setOrganizationId(String.valueOf(departmentDTO.getId()));
//        mergeDemand.setOrganizationName(departmentDTO.getName());

        //todo 一级部门下的统筹账号，暂时先写死
        String publisherNo = "uid_1928027131879292928";
        mergeDemand.setPublisherNo(publisherNo);
        List<UserDetailDTO> userDetailDTOList = userClient.batchQueryUser(ListUtil.toList(publisherNo));
        mergeDemand.setPublisherName(userDetailDTOList.get(0).getUserName());
        System.out.println(JsonUtil.toPrettyJsonStr(mergeDemand));
        return mergeDemand;
    }

    @GetMapping("/query/user/without/tenant")
    @ApiOperation(value = "测试查询部门")
    public FlightDemandDTO testQueryDepartmentWithoutTenantId(@RequestParam("userNo") String userNo) {
        FlightDemandDTO mergeDemand = new FlightDemandDTO();
        UserRootDepartmentQueryDTO queryDTO = new UserRootDepartmentQueryDTO();
        queryDTO.setUserNos(ListUtil.toList(userNo));
        List<UserRootDepartmentDTO> userRootDepartmentList = departmentClient.getUserRootDepartments(queryDTO);
        DepartmentFlatDTO departmentDTO = userRootDepartmentList.get(0).getDepartmentDTO();
        mergeDemand.setOrganizationId(String.valueOf(departmentDTO.getId()));
        mergeDemand.setOrganizationName(departmentDTO.getName());

        String publisherNo = userNo;
        mergeDemand.setPublisherNo(publisherNo);
        List<UserDetailDTO> userDetailDTOList = userClient.getUserDetailListWithoutTenantId(ListUtil.toList(publisherNo));
        mergeDemand.setPublisherName(userDetailDTOList.get(0).getUserName());
        System.out.println(JsonUtil.toPrettyJsonStr(mergeDemand));
        return mergeDemand;
    }
}
