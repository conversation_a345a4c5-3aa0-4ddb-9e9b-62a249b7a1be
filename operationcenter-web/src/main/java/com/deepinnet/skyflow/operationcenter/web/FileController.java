package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.FileClient;
import com.deepinnet.infra.api.dto.FileInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 文件上传下载控制器
 * Date: 2025/5/14
 * Author: lijunheng
 */
@Slf4j
@RestController
@RequestMapping("/api/file")
@Api(tags = "文件管理接口")
public class FileController {

    @Resource
    private FileClient fileClient;

    /**
     * 上传文件
     *
     * @param file       文件
     * @return 文件URL
     */
    @PostMapping("/upload")
    @ApiOperation("上传文件")
    public Result<FileInfoDTO> uploadFile(
            @ApiParam(value = "文件", required = true) @RequestPart("file") MultipartFile file) {
        return fileClient.uploadFile(file);
    }

    /**
     * 下载文件
     *
     * @param objectName 对象名称
     * @param filename   下载时的文件名（可选，默认为对象名称）
     * @return 文件流
     */
    @GetMapping("/download/objectName")
    @ApiOperation("下载文件")
    public ResponseEntity<org.springframework.core.io.Resource> downloadFileByObjectName(
            @ApiParam(value = "对象名称", required = true) @RequestParam("objectName") String objectName,
            @ApiParam(value = "下载文件名（可选）") @RequestParam(value = "filename", required = false) String filename) {
        return fileClient.downloadFileByObjectName(objectName, filename);
    }

    /**
     * 下载文件
     *
     * @param fileUrl   文件路径
     * @return 文件流
     */
    @GetMapping("/download/fileUrl")
    @ApiOperation("下载文件")
    public ResponseEntity<org.springframework.core.io.Resource> downloadFileByUrl(
            @ApiParam(value = "文件路径", required = true) @RequestParam("fileUrl") String fileUrl) {
        return fileClient.downloadFileByUrl(fileUrl);
    }


    /**
     * 获取文件URL
     *
     * @param objectName 对象名称
     * @return 文件URL
     */
    @GetMapping("/url")
    @ApiOperation("获取文件URL")
    public Result<String> getFileUrl(
            @ApiParam(value = "对象名称", required = true) @RequestParam("objectName") String objectName) {
        return fileClient.getFileUrl(objectName);
    }
}
