package com.deepinnet.skyflow.operationcenter.web;

import cn.hutool.core.collection.CollStreamUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.CategoryDTO;
import com.deepinnet.skyflow.operationcenter.dto.CategoryQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandPlanStatWebQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.product.CategoryService;
import com.deepinnet.skyflow.operationcenter.service.user.UserService;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.spatiotemporalplatform.client.skyflow.FlightClient;
import com.deepinnet.spatiotemporalplatform.dto.*;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 飞行相关接口
 *
 * <AUTHOR>
 */
@Api(tags = "[低空经济] => 飞行接口")
@RestController
@RequestMapping("/flight")
@RequiredArgsConstructor
@Validated
public class FlightController {

    @Resource
    private FlightClient flightClient;

    @Resource
    private UserService userService;

    @Resource
    private CategoryService categoryService;

    @ApiOperation("统计近三个月内撮合平台在交警类目下的需求的计划统计排行")
    @PostMapping("/demand/plan/stat")
    public Result<List<FlightDemandPlanStatDTO>> queryFlightDemandPlanStat(@RequestBody FlightDemandPlanStatQueryDTO queryDTO) {
        queryDTO.setUserNo(UserUtil.getUserNo());
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        return flightClient.queryFlightDemandPlanStat(queryDTO);
    }

    @ApiOperation("统计近三个月内撮合平台在交警类目下的需求的计划统计排行v2")
    @PostMapping("/demand/plan/v2/stat")
    public Result<List<FlightDemandPlanStatDTO>> queryFlightDemandPlanStatV2(@RequestBody FlightDemandPlanStatWebQueryDTO webQueryDTO) {

        FlightDemandPlanStatQueryDTO queryDTO = new FlightDemandPlanStatQueryDTO();

        BeanUtils.copyProperties(webQueryDTO, queryDTO);

        queryDTO.setUserNoList(userService.getAvailableUserNoByDepartmentId(webQueryDTO.getDepartmentId()));
        queryDTO.setTenantId(TenantIdUtil.getTenantId());

        CategoryQueryDTO categoryQueryDTO = new CategoryQueryDTO();
        categoryQueryDTO.setTenantId(TenantIdUtil.getTenantId());
        List<CategoryDTO> categoryDTOList = categoryService.queryCategoryTree(categoryQueryDTO);
        List<CategoryDTO> leafNodesCollection = new ArrayList<>();
        getLeafNodes(leafNodesCollection, categoryDTOList);

        queryDTO.setCategoryCodeList(CollStreamUtil.toList(leafNodesCollection, CategoryDTO::getCategoryNo));

        var res = flightClient.queryFlightDemandPlanStat(queryDTO);

        Map<String, CategoryDTO> leafNodesMap = CollStreamUtil.toMap(leafNodesCollection, CategoryDTO::getCategoryNo, Function.identity());
        if(CollectionUtils.isNotEmpty(res.getData())) {
            for (FlightDemandPlanStatDTO datum : res.getData()) {
                datum.setDemandCategoryName(leafNodesMap.getOrDefault(datum.getDemandCategoryCode(), new CategoryDTO()).getCategoryName());
            }
        }

        return res;
    }

    @ApiOperation("提供根据计划id返回算法视频流地址")
    @GetMapping("/algorithm/monitor/url")
    public Result<String> queryAlgorithmMonitorUrl(@RequestParam("planId") String planId) {
        return flightClient.queryAlgorithmMonitorUrl(planId);
    }

    private void getLeafNodes(List<CategoryDTO> leafNodesCollection, List<CategoryDTO> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return;
        }
        for (CategoryDTO node : nodes) {
            List<CategoryDTO> children = node.getChildren();
            if (CollectionUtils.isEmpty(children)) {
                leafNodesCollection.add(node);
            } else {
                getLeafNodes(leafNodesCollection, children);
            }
        }
    }
} 