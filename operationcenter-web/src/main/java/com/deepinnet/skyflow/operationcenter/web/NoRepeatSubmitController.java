package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.service.norepeat.TokenNoRepeatSubmitStrategy;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 防重复提交管理
 *
 * <AUTHOR> wong
 * @create 2025/04/25
 */
@Api(tags = "防重复提交管理")
@RestController
@RequestMapping("/no/repeat/submit")
public class NoRepeatSubmitController {

    @Resource
    private TokenNoRepeatSubmitStrategy tokenNoRepeatSubmitStrategy;

    @ApiOperation("获取执行标识，30分钟过期")
    @GetMapping("/token")
    public Result<String> getToken() {
        String token = IdGenerateUtil.getId("NoRepeat");
        tokenNoRepeatSubmitStrategy.putToken(token, 30L, TimeUnit.MINUTES);
        return Result.success(token);
    }
} 