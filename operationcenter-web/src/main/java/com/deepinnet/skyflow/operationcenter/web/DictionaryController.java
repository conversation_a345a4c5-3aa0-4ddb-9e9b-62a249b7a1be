package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.client.base.DictionaryClient;
import com.deepinnet.spatiotemporalplatform.dto.DictionaryDTO;
import com.deepinnet.spatiotemporalplatform.dto.DictionaryQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.DictionaryVO;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 字典控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dictionary")
@Api(tags = "字典管理")
@Validated
public class DictionaryController {

    @Resource
    private DictionaryClient dictionaryClient;

    @PostMapping("/create")
    @ApiOperation("创建字典")
    public Result<Long> createDictionary(@RequestBody @Valid DictionaryDTO dictionaryDTO) {
        dictionaryDTO.setTenantId(TenantIdUtil.getTenantId());
        return dictionaryClient.createDictionary(dictionaryDTO);
    }

    @PostMapping("/update")
    @ApiOperation("更新字典")
    public Result<Boolean> updateDictionary(@RequestBody @Valid DictionaryDTO dictionaryDTO) {
        dictionaryDTO.setTenantId(TenantIdUtil.getTenantId());
        return dictionaryClient.updateDictionary(dictionaryDTO);
    }

    @PostMapping("/get")
    @ApiOperation("根据ID获取字典")
    public Result<DictionaryVO> getDictionaryById(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                            @ApiParam(value = "字典ID", required = true) Long id) {
        return dictionaryClient.getDictionaryById(id);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询字典")
    public Result<CommonPage<DictionaryVO>> pageQueryDictionary(@RequestBody @Valid DictionaryQueryDTO queryDTO) {
        queryDTO.setTenantId(TenantIdUtil.getTenantId());
        return dictionaryClient.pageQueryDictionary(queryDTO);
    }

    @PostMapping("/delete")
    @ApiOperation("删除字典")
    public Result<Boolean> deleteDictionary(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                        @ApiParam(value = "字典ID", required = true) Long id) {
        return dictionaryClient.deleteDictionary(id);
    }
}