package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.MergeDemandRuleDTO;
import com.deepinnet.skyflow.operationcenter.dto.OrderPageQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.rule.DemandMergeRuleService;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.deepinnet.skyflow.operationcenter.vo.MergeRuleConfigVO;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/11
 */

@Api(tags = "[合并规则] => 合并规则")
@RestController
@RequestMapping("/rule")
@RequiredArgsConstructor
@Validated
public class MergeRuleConfigController {

    private final DemandMergeRuleService demandMergeRuleService;

    @GetMapping("/list")
    @ApiOperation("[合并规则]=>规则列表")
    public Result<List<MergeRuleConfigVO>> list() {

        return Result.success(demandMergeRuleService.ruleList());
    }

    @PostMapping("/save")
    @ApiOperation(value = "[合并规则]=>保存规则")
    public Result<Boolean> saveRule(@RequestBody @Validated MergeDemandRuleDTO dto) {
        return Result.success(demandMergeRuleService.save(dto));
    }

}
