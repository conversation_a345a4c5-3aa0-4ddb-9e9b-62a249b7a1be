package com.deepinnet.skyflow.operationcenter.web.query;

import com.deepinnet.digitaltwin.common.error.ErrorCode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;

import java.util.Objects;

/**
 * Creator zengjuerui
 * Date 2025-06-04
 **/

@Data
public class TrafficPoliceQuery {

    @ApiModelProperty("目标点经度")
    private String lng;
    @ApiModelProperty("目标点纬度")
    private String lat;
    @ApiModelProperty("到目标点距离")
    private Integer distanceRange;
    @ApiModelProperty("范围")
    private String areaWkt;


    @JsonIgnore
    public Point emitPoint() {
        return WktUtil.toPoint(lng, lat);
    }

    @JsonIgnore
    public Geometry emitGeometry() {
        Point point = WktUtil.toPoint(lng, lat);
        Geometry geometry;
        if(StringUtils.isNotBlank(areaWkt)) {
            geometry = WktUtil.toGeometry(areaWkt);
        } else {
            if(Objects.isNull(distanceRange)) {
                throw new BizException(ErrorCode.ILLEGAL_PARAMS.getCode(), "范围不可为空");
            }

            geometry = WktUtil.buffer(point, distanceRange);
        }

        return geometry;
    }
}
