package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.AdminClient;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2024/11/27 11:39
 * @Description
 */
@RestController
@Api(tags = "管理后台服务")
@Validated
@RequestMapping("/admin")
public class AdminController {

    @Resource
    private AdminClient adminClient;

    @PostMapping("/account/save")
    @ApiOperation("保存账号")
    public Result<Boolean> saveOrUpdateAccount(@Valid @RequestBody UserAccountSaveDTO saveDTO) {
        return adminClient.saveOrUpdateAccount(saveDTO);
    }

    @GetMapping("/account/delete")
    @ApiOperation("删除账号")
    public Result<Boolean> delete(String accountNo) {
        if (accountNo == null) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        return adminClient.delete(accountNo);
    }

    @PostMapping("/account/page")
    @ApiOperation("分页查询账号")
    public Result<CommonPage<UserInfoDTO>> pageQueryUserAccount(@Valid @RequestBody UserAccountQueryDTO queryDTO) {
        return adminClient.pageQueryUserAccount(queryDTO);
    }

    @GetMapping("/account/detail")
    @ApiOperation("查询账号详情")
    public Result<AccountDTO> getAccountDetail(String accountNo) {
        return adminClient.getAccountDetail(accountNo);
    }

    @PostMapping("/role/page")
    @ApiOperation("分页查询角色")
    public Result<CommonPage<RoleDTO>> pageQueryRoleDTO(@Valid @RequestBody RoleQueryDTO roleQueryDTO) {
        return adminClient.pageQueryRoleDTO(roleQueryDTO);
    }

    @PostMapping("/role/save")
    @ApiOperation("保存或更新角色")
    public Result<Boolean> saveOrUpdateRole(@Valid @RequestBody RoleSaveDTO saveDTO) {
        return adminClient.saveOrUpdateRole(saveDTO);
    }

    @PostMapping("/role/detail")
    @ApiOperation("获取角色详情")
    public Result<RoleDTO> getRoleDetail(@Valid @RequestBody RoleDetailQueryDTO queryDTO) {
        return adminClient.getRoleDetail(queryDTO);
    }

    @GetMapping("/role/delete")
    @ApiOperation("删除角色")
    public Result<Boolean> deleteRole(Long id) {
        return adminClient.deleteRole(id);
    }

    @PostMapping("/permission/tree")
    @ApiOperation("获取权限树")
    public Result<List<PermissionDTO>> getPermissionTree() {
        return adminClient.getPermissionTree();
    }

    @PostMapping("/account/freeze")
    @ApiOperation(value = "冻结账号", notes = "冻结账号后状态码为1，账号将无法登录系统")
    public Result<Boolean> freezeAccounts(@Valid @RequestBody AccountFreezeDTO accountFreezeDTO) {
        return adminClient.freezeAccounts(accountFreezeDTO);
    }

    @PostMapping("/account/unfreeze")
    @ApiOperation(value = "解冻账号", notes = "解冻账号后状态码为0，账号可以正常登录系统")
    public Result<Boolean> unfreezeAccounts(@RequestBody AccountFreezeDTO accountFreezeDTO) {
        return adminClient.unfreezeAccounts(accountFreezeDTO);
    }
}
