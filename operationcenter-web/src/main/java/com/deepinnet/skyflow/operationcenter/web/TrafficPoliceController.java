package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.error.ErrorCode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.localdata.integration.model.outsidebean.CameraTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.client.IntercomPointRecordClient;
import com.deepinnet.skyflow.operationcenter.service.client.SurveillanceCameraClient;
import com.deepinnet.skyflow.operationcenter.util.GISUtil;
import com.deepinnet.skyflow.operationcenter.web.query.TrafficPoliceQuery;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCameraCondition;
import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomGeomWkt;
import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomPointRecord;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * Creator zengjuerui
 * Date 2025-05-27
 **/

@RestController
@Api(tags = "交警数据")
@Validated
@RequestMapping("/trafficPolice")
public class TrafficPoliceController {

    @Resource
    private SurveillanceCameraClient surveillanceCameraClient;
    @Resource
    private IntercomPointRecordClient intercomPointRecordClient;


    @ApiOperation("摄像头列表")
    @PostMapping("/listSurveillanceCamera")
    public Result<List<SurveillanceCamera>> listSurveillanceCamera(@RequestBody TrafficPoliceQuery query) {

        Point point = query.emitPoint();

        SurveillanceCameraCondition condition = new SurveillanceCameraCondition();
        condition.setDistance(0);
        condition.setWkt(query.emitGeometry().toText());

        List<SurveillanceCamera> cameras = surveillanceCameraClient.queryList(condition);


        return Result.success(GISUtil.sortToTargetPoint(cameras, point, c -> WktUtil.toPoint(c.getCoordinates())));
    }

    @ApiOperation("实时警力列表")
    @PostMapping("/listIntercomPointRecord")
    public Result<List<IntercomPointRecord>> listIntercomPointRecord(@RequestBody TrafficPoliceQuery query) {

        Point point = query.emitPoint();

        IntercomGeomWkt intercomGeomWkt = new IntercomGeomWkt();
        intercomGeomWkt.setWkt(query.emitGeometry().toText());

        List<IntercomPointRecord> list = intercomPointRecordClient.list(intercomGeomWkt);

        return Result.success(GISUtil.sortToTargetPoint(list, point, c -> WktUtil.toPoint(c.getCoordinate())));
    }

    @ApiOperation("摄像头视频流")
    @GetMapping("/videoStream")
    public void cameraVideoStream(@RequestParam("code") String code, @RequestParam("type") String type, HttpServletResponse response) {
        SurveillanceCamera camera = surveillanceCameraClient.getByCodeAndType(code, type);

        try {
            response.sendRedirect(camera.getVideoStreamUrl());
        } catch (Exception e) {
            throw new BizException(ErrorCode.UNKNOWN_EXCEPTION.getCode());
        }
    }
}
