package com.deepinnet.skyflow.operationcenter.web;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.ContentDetailDTO;
import com.deepinnet.skyflow.operationcenter.service.ContentDetailService;
import com.deepinnet.skyflow.operationcenter.service.convert.ContentDetailConvert;
import com.deepinnet.skyflow.operationcenter.vo.ContentDetailVO;
import com.deepinnet.spatiotemporalplatform.dto.IdDTO;
import com.deepinnet.tenant.TenantIdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 首页内容控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/content")
@Api(tags = "首页内容管理")
@Validated
public class ContentDetailController {

    @Resource
    private ContentDetailService contentDetailService;

    @Resource
    private ContentDetailConvert contentDetailConvert;

    @PostMapping("/create")
    @ApiOperation("创建首页内容")
    public Result<Integer> createContent(@RequestBody @Valid ContentDetailDTO contentDetailDTO) {
        // 设置租户ID
        contentDetailDTO.setTenantId(TenantIdUtil.getTenantId());
        Integer id = contentDetailService.saveContent(contentDetailDTO);
        return Result.success(id);
    }

    @PostMapping("/update")
    @ApiOperation("更新首页内容")
    public Result<Boolean> updateContent(@RequestBody @Valid ContentDetailDTO contentDetailDTO) {
        // 设置租户ID
        contentDetailDTO.setTenantId(TenantIdUtil.getTenantId());
        boolean success = contentDetailService.updateContent(contentDetailDTO);
        return Result.success(success);
    }

    @PostMapping("/get")
    @ApiOperation("根据ID获取首页内容")
    public Result<ContentDetailVO> getContentById(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                                @ApiParam(value = "内容ID DTO", required = true) IdDTO idDTO) {
        ContentDetailDTO contentDetailDTO = contentDetailService.getContentById(idDTO.getId());
        ContentDetailVO contentDetailVO = contentDetailConvert.convertToVO(contentDetailDTO);
        return Result.success(contentDetailVO);
    }

    @GetMapping("/getByType")
    @ApiOperation("根据内容类型获取首页内容")
    public Result<ContentDetailVO> getContentByType(@RequestParam @Valid @NotBlank(message = "内容类型不能为空")
                                                  @ApiParam(value = "内容类型", required = true) String contentType) {
        ContentDetailDTO contentDetailDTO = contentDetailService.getContentByType(contentType);
        ContentDetailVO contentDetailVO = contentDetailConvert.convertToVO(contentDetailDTO);
        return Result.success(contentDetailVO);
    }

    @GetMapping("/list")
    @ApiOperation("获取首页内容列表")
    public Result<List<ContentDetailVO>> listContent() {
        List<ContentDetailDTO> contentDetailDTOList = contentDetailService.listContent();
        List<ContentDetailVO> contentDetailVOList = contentDetailConvert.convertToVOList(contentDetailDTOList);
        return Result.success(contentDetailVOList);
    }

    @PostMapping("/delete")
    @ApiOperation("删除首页内容")
    public Result<Boolean> deleteContent(@RequestBody @Valid @NotNull(message = "ID不能为空")
                                       @ApiParam(value = "内容ID DTO", required = true) IdDTO idDTO) {
        boolean success = contentDetailService.deleteContent(idDTO.getId());
        return Result.success(success);
    }
    
    @PostMapping("/createOrUpdate")
    @ApiOperation("创建或更新首页内容（根据租户ID和内容类型）")
    public Result<Integer> createOrUpdateContent(@RequestBody @Valid ContentDetailDTO contentDetailDTO) {
        // 确保设置了租户ID
        if (contentDetailDTO.getTenantId() == null) {
            contentDetailDTO.setTenantId(TenantIdUtil.getTenantId());
        }
        
        Integer id = contentDetailService.createOrUpdateByTenantAndType(contentDetailDTO);
        return Result.success(id);
    }
} 