<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.deepinnet.skyflow</groupId>
    <artifactId>operationcenter</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>operationcenter</name>
    <description>Operation Center for SkyFlow</description>

    <properties>
        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.6.13</spring-boot.version>
        <operationcenter-api-model.version>20250728.0-RELEASE</operationcenter-api-model.version>
        <jasypt.version>3.0.5</jasypt.version>
        <pageHelper.version>1.4.7</pageHelper.version>
        <mapstruct.version>1.5.0.Final</mapstruct.version>
        <dynamic-datasource.version>3.5.1</dynamic-datasource.version>
        <mybatis-plus.version>3.5.1</mybatis-plus.version>
        <lombok.version>1.18.24</lombok.version>
        <liteflow.version>2.13.1</liteflow.version>
        <digital-boot-starter.version>1.0.0.2025.07.23-SNAPSHOT</digital-boot-starter.version>
        <infra-api-model.version>20250813.1-SNAPSHOT</infra-api-model.version>
        <feigin.version>3.1.9</feigin.version>
        <satoken.version>1.38.0</satoken.version>
        <logback.version>6.6</logback.version>
        <spatiotemporalplatform-api-model.version>20250813.0-SNAPSHOT</spatiotemporalplatform-api-model.version>
        <digitaltwincommon.version>1.3-SNAPSHOT</digitaltwincommon.version>
        <local-data-integration.version>1.1.20250715-SNAPSHOT</local-data-integration.version>
    </properties>

    <modules>
        <module>operationcenter-api-model</module>
        <module>operationcenter-util</module>
        <module>operationcenter-dal</module>
        <module>operationcenter-service</module>
        <module>operationcenter-biz</module>
        <module>operationcenter-web</module>
        <module>operationcenter-app-starter</module>
    </modules>
    <dependencies>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>digital-boot-starter</artifactId>
                <version>${digital-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>spatiotemporalplatform-infrastructure-api-model</artifactId>
                <version>${infra-api-model.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${feigin.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>2.6.13</version>
            </dependency>

            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>1.38.0</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>2.6.13</version>
            </dependency>

            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>1.38.0</version>
            </dependency>

            <!-- 连接池 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.12.1</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>2.6.13</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>0.2.0</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.deepinnet.skyflow</groupId>
                <artifactId>operationcenter-app-starter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.deepinnet.skyflow</groupId>
                <artifactId>operationcenter-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.deepinnet.skyflow</groupId>
                <artifactId>operationcenter-dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.deepinnet.skyflow</groupId>
                <artifactId>operationcenter-api-model</artifactId>
                <version>${operationcenter-api-model.version}</version>
            </dependency>
            <dependency>
                <groupId>com.deepinnet.skyflow</groupId>
                <artifactId>operationcenter-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.deepinnet.skyflow</groupId>
                <artifactId>operationcenter-util</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.deepinnet.skyflow</groupId>
                <artifactId>operationcenter-biz</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>2.0.25</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pageHelper.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.jsqlparser</groupId>
                        <artifactId>jsqlparser</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.9</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>digitaltwincommon</artifactId>
                <version>${digitaltwincommon.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.19</version>
            </dependency>

            <!-- liteflow 流程引擎 -->
            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-spring-boot-starter</artifactId>
                <version>${liteflow.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>spatiotemporalplatform-api-model</artifactId>
                <version>${spatiotemporalplatform-api-model.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.deepinnet</groupId>
                        <artifactId>local-data-integration</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.deepinnet</groupId>
                <artifactId>local-data-integration</artifactId>
                <version>${local-data-integration.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>skyflow-operation-center</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project> 