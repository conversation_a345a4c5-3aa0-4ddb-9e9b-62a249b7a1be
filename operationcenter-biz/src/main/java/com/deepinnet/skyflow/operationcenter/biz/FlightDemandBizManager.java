package com.deepinnet.skyflow.operationcenter.biz;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.DataAccessDTO;
import com.deepinnet.infra.api.dto.SimpleDepartmentDTO;
import com.deepinnet.infra.api.dto.UserDetailDTO;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveInstanceService;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalNode;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalRecord;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightDemandStatCondition;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandSceneEnum;
import com.deepinnet.skyflow.operationcenter.service.approval.instance.ApproveConvert;
import com.deepinnet.skyflow.operationcenter.service.client.FlightPlanQueryClient;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.skyflow.operationcenter.service.context.TenantContext;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightDemandConvert;
import com.deepinnet.skyflow.operationcenter.service.demand.FlightDemandService;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.order.FlightOrderHelper;
import com.deepinnet.skyflow.operationcenter.service.order.FlightPlanService;
import com.deepinnet.skyflow.operationcenter.service.product.FlightProductService;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavBmService;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavService;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavStationService;
import com.deepinnet.skyflow.operationcenter.service.user.UserService;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.util.enums.UserTypeEnum;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.deepinnet.spatiotemporalplatform.dto.BatchQueryPlanDTO;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import com.deepinnet.spatiotemporalplatform.vo.AerodromeVO;
import com.deepinnet.spatiotemporalplatform.vo.FlightCountVO;
import com.deepinnet.spatiotemporalplatform.vo.FlightPlanVO;
import com.deepinnet.tenant.TenantIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Point;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 飞行需求业务处理类，用于整合飞行需求领域服务和其他领域服务
 * Date: 2025/4/17
 * Author: lijunheng
 */
@Slf4j
@Service
public class FlightDemandBizManager {

    @Resource
    private FlightDemandService demandService;

    @Resource
    private FlightOrderHelper orderHelper;

    @Resource
    private FlightProductService productService;

    @Resource
    private FlightDemandConvert demandConvert;

    @Resource
    private UserRemoteClient userClient;

    @Resource
    private FlightPlanQueryClient flightPlanQueryClient;

    @Resource
    private FlightUavService flightUavService;

    @Resource
    private FlightUavStationService flightUavStationService;

    @Resource
    private FlightPlanService flightPlanService;

    @Resource
    private FlightUavBmService flightUavBmService;

    @Resource
    private ApproveInstanceService approveInstanceService;

    @Resource
    private ApproveConvert approveConvert;

    @Resource
    private UserService userService;

    @Resource
    private UserRemoteClient userRemoteClient;

    public String saveFlightDemand(FlightDemandDTO flightDemandDTO) {
        String userNo = UserUtil.getUserNo();
        flightDemandDTO.setPublisherNo(userNo);

        // 某些环境因为网络数据不通，需要一些特殊值作为查询手段
        flightDemandDTO.setBizData(JSONUtil.toJsonStr(Map.of("account", UserUtil.getAccount())));

        List<UserDetailDTO> userDetailList = userClient.batchQueryUser(ListUtil.toList(userNo));
        flightDemandDTO.setPublisherName(userDetailList.get(0).getUserName());

        FlightOrderVO flightOrder = orderHelper.getFlightOrder(flightDemandDTO);
        flightDemandDTO.setProductName(flightOrder.getProductName());

        SimpleDepartmentDTO simpleDepartmentDTO = userRemoteClient.getUserRelatedDepartment(userNo);

        flightDemandDTO.setOrganizationId(String.valueOf(simpleDepartmentDTO.getId()));
        flightDemandDTO.setOrganizationName(simpleDepartmentDTO.getName());
        return demandService.saveFlightDemand(flightDemandDTO);
    }

    public String editDemand(FlightDemandDTO flightDemandDTO) {
        String userNo = UserUtil.getUserNo();
        //判断需求是否有权限修改
        FlightDemandDTO existFlightDemand = demandService.getFlightDemandByNo(flightDemandDTO.getDemandNo());

        flightDemandDTO.setEditorNo(userNo);

        List<UserDetailDTO> userDetailList = userClient.batchQueryUser(ListUtil.toList(userNo));
        flightDemandDTO.setEditorName(userDetailList.get(0).getUserName());
        flightDemandDTO.setEditTime(LocalDateTime.now());

        FlightOrderVO flightOrder = orderHelper.getFlightOrder(flightDemandDTO);
        flightDemandDTO.setProductName(flightOrder.getProductName());


        //原来数据保持原样
        flightDemandDTO.setPublisherNo(existFlightDemand.getPublisherNo());

        // 某些环境因为网络数据不通，需要一些特殊值作为查询手段
        flightDemandDTO.setBizData(existFlightDemand.getBizData());

        flightDemandDTO.setPublisherName(existFlightDemand.getPublisherName());

        flightDemandDTO.setPublishTime(existFlightDemand.getPublishTime());

        flightDemandDTO.setOrganizationId(existFlightDemand.getOrganizationId());
        flightDemandDTO.setOrganizationName(existFlightDemand.getOrganizationName());

        flightDemandDTO.setIsMergeDemand(existFlightDemand.getIsMergeDemand());

        return demandService.editDemand(flightDemandDTO);
    }

    /**
     * 免登后台接口-获取需求详情
     *
     * @param demandNo
     * @return
     */
    public FlightDemandVO getPassFlightDemandByNo(String demandNo) {
        try {
            TenantContext.disableTenantLine();
            FlightDemandDTO flightDemand = demandService.getFlightDemandByNo(demandNo);
            if (flightDemand == null) {
                return null;
            }
            return setDemandVO(flightDemand, null);
        } finally {
            TenantContext.clear();
        }
    }

    /**
     * 客户角色获取需求详情
     *
     * @param demandNo
     * @return
     */
    public FlightDemandVO getCustomerFlightDemandByNo(String demandNo) {
        FlightDemandDTO flightDemand = demandService.getFlightDemandByNo(demandNo);
        if (flightDemand == null) {
            return null;
        }

        //如果发布人不是自己就不允许查看
        DataAccessDTO availableQueryData = userClient.getAvailableQueryData();
        List<String> supportQueryUserNos = availableQueryData.getSupportQueryUserNos();
        if (CollectionUtil.isEmpty(supportQueryUserNos)) {
            return null;
        }
        if (supportQueryUserNos.size() == 1 && Objects.equals(DataScopeConstants.SUPER_ADMIN, supportQueryUserNos.get(0))) {
            //管理员账号
            return setDemandVO(flightDemand, UserTypeEnum.CUSTOMER);
        }
        if (!supportQueryUserNos.contains(flightDemand.getPublisherNo())) {
            return null;
        }
        return setDemandVO(flightDemand, UserTypeEnum.CUSTOMER);
    }

    /**
     * 服务商、运营角色获取需求详情
     *
     * @param demandNo
     * @return
     */
    public FlightDemandVO getOpFlightDemandByNo(String demandNo) {
        FlightDemandDTO flightDemand = demandService.getFlightDemandByNo(demandNo);
        if (flightDemand == null) {
            return null;
        }

        //判断权限
        List<UserDetailDTO> userDetailList = userClient.batchQueryUser(ListUtil.toList(UserUtil.getUserNo()));
        if (CollectionUtil.isEmpty(userDetailList)) {
            LogUtil.error("查询到的用户信息为空，demandNo-{},userNo={},tenantId={}", demandNo, UserUtil.getUserNo(), TenantIdUtil.getTenantId());
        }
        UserDetailDTO loginUser = userDetailList.get(0);
        String userType = loginUser.getUserType();
        //服务商请求时需要判断是不是匹配到的服务商
        if (Objects.equals(userType, UserTypeEnum.SUPPLIER.getCode())) {
            List<FlightDemandMatchServiceProviderDTO> serviceProviderList = flightDemand.getServiceProviderList();
            if (CollectionUtil.isEmpty(serviceProviderList) ||
                    !serviceProviderList.stream()
                            .anyMatch(serviceProvider -> Objects.equals(serviceProvider.getServiceProviderNo(), loginUser.getUserNo()))) {
                return null;
            }
        }
        //运营后台的不需要校验
        if (Objects.equals(userType, UserTypeEnum.OPERATION.getCode())) {
        }

        return setDemandVO(flightDemand, UserTypeEnum.OPERATION);
    }

    /**
     * 客户角色获取需求列表
     *
     * @param queryDTO
     * @return
     */
    public CommonPage<FlightDemandDTO> pageQueryCustomerFlightDemand(FlightDemandQueryDTO queryDTO) {
        CommonPage<FlightDemandDTO> flightDemandDTOCommonPage = demandService.pageQueryFlightDemandCustomerManage(queryDTO);
        return setFlightCountAndApprove(flightDemandDTOCommonPage, queryDTO.getTenantId(), UserTypeEnum.CUSTOMER);
    }

    /**
     * 服务商、运营角色获取需求列表
     *
     * @param queryDTO
     * @return
     */
    public CommonPage<FlightDemandDTO> pageQueryOpFlightDemand(FlightDemandQueryDTO queryDTO) {
        CommonPage<FlightDemandDTO> flightDemandDTOCommonPage = demandService.pageQueryFlightDemandOpManage(queryDTO);
        return setFlightCountAndApprove(flightDemandDTOCommonPage, queryDTO.getTenantId(), UserTypeEnum.OPERATION);
    }

    /**
     * 根据计划ID获取需求信息
     *
     * @param
     * @return
     */

    public FlightDemandStatsDTO get90DayStatistics() {
        return demandService.get90DayStatistics();
    }

    public FlightDemandStatsDTO getStatistics(FlightDemandStatsQueryDTO queryDTO) {

        FlightDemandStatCondition condition = new FlightDemandStatCondition();
        // 查最近三个月的
        condition.setStartTime(LocalDateTime.of(LocalDate.now().plusMonths(-3), LocalTime.MIN));

        condition.setUserNoList(userService.getAvailableUserNoByDepartmentId(queryDTO.getDepartmentId()));

        condition.setDemandScene(FlightDemandSceneEnum.ONE_NET_UNIFIED_FLY.name());

        condition.setTenantId(TenantIdUtil.getTenantId());

        return demandService.getStatistics(condition);
    }

    public List<FlightDemandDayStatsDTO> get7DayStatistics(FlightDemandStatsQueryDTO queryDTO) {

        FlightDemandStatCondition condition = new FlightDemandStatCondition();
        if(queryDTO == null) {
            condition.setUserNo(UserUtil.getUserNo());
            condition.setDemandScene(FlightDemandSceneEnum.SKY_FLOW_ECONOMY_MATCHING_PLATFORM.name());
        } else {
            condition.setUserNoList(userService.getAvailableUserNoByDepartmentId(queryDTO.getDepartmentId()));
            condition.setDemandScene(FlightDemandSceneEnum.ONE_NET_UNIFIED_FLY.name());
        }

        // 查最近三个月的
        condition.setStartTime(LocalDateTime.of(LocalDate.now().plusMonths(-3), LocalTime.MIN));

        condition.setTenantId(TenantIdUtil.getTenantId());

        return demandService.get7DayStatistics(condition);
    }

    public FlightDemandPlanDTO getDemandPlanDetail(String planId) {

        FlightPlanVO flightPlanVO = flightPlanQueryClient.queryFlightPlan(planId);
        if (flightPlanVO == null) {
            return null;
        }

        FlightDemandPlanDTO res = demandConvert.toFlightDemandPlanDTO(flightPlanVO);

        FlightDemandDTO flightDemandDTO = demandService.getFlightDemandByNo(res.getBizNo());
        if(BooleanUtils.isTrue(flightDemandDTO.getIsMergeDemand())) {
            if(CollectionUtils.isNotEmpty(flightDemandDTO.getOriginDemandList())) {

                try {
                    flightDemandDTO.setOriginDemandList(flightDemandDTO.getOriginDemandList()
                            .stream()
                            .filter(d -> d.getPublisherNo().equals(UserUtil.getUserNo()) || d.getBizData().contains(UserUtil.getAccount()))
                            .collect(Collectors.toList()));

                    flightDemandDTO.setAreaList(flightDemandDTO.getOriginDemandList()
                            .stream()
                            .flatMap(d -> d.getAreaList().stream())
                            .collect(Collectors.toList()));
                } catch (Exception e) {
                    log.error("handle origin demand error", e);
                }
            }
        }

        res.setFlightDemand(flightDemandDTO);

        res.setFlightUav(flightUavService.getFlightUavBySn(res.getUavId()));

        if (res.getFlightUav() != null && StringUtils.isNotBlank(res.getFlightUav().getFlightUavNo())) {
            res.setFlightUavStation(flightUavStationService.getFlightUavNo(res.getFlightUav().getFlightUavNo()));
        }

        if (res.getFlightUavStation() == null) {
            List<AerodromeVO> aerodromeLists = flightPlanVO.getLandingAerodrome();
            if (CollectionUtil.isNotEmpty(aerodromeLists)) {
                Point demandPoint = WktUtil.toPoint(res.getFlightDemand().getCenterPointLongitude(),
                        res.getFlightDemand().getCenterPointLatitude());

                double maxDistance = Double.MIN_VALUE;
                AerodromeVO farthest = null;
                for (AerodromeVO aerodromeList : aerodromeLists) {
                    Point tmp = WktUtil.toPoint(aerodromeList.getPosition());
                    double distance = Math.abs(tmp.distance(demandPoint));
                    if (distance > maxDistance) {
                        maxDistance = distance;
                        farthest = aerodromeList;
                    }
                }

                res.setAerodrome(demandConvert.toAerodromeDTO(farthest));
            }

        }

        return res;
    }

    public FlightDemandDTO getFlightDemandByPlanId(String planId) {
        return demandService.getFlightDemandByPlanId(planId);
    }

    /**
     * 需求分配服务商
     *
     * @param dto
     * @return
     */
    public Boolean assignServiceProvider(FlightDemandAssignDTO dto) {
        //这里不需要加事务，即使同步需求失败了，分配成功了也需要记录
        demandService.assignServiceProvider(dto);
        demandService.syncDemandToProvider(dto.getDemandCode(), dto.getServiceProviderNo());
        return Boolean.TRUE;
    }


    private CommonPage<FlightDemandDTO> setFlightCountAndApprove(CommonPage<FlightDemandDTO> flightDemandDTOCommonPage, String tenantId, UserTypeEnum userType) {
        List<String> demandNoList = flightDemandDTOCommonPage.getList().stream().map(FlightDemandDTO::getDemandNo).collect(Collectors.toList());
        List<FlightCountVO> flightCountList;
        if (userType == UserTypeEnum.CUSTOMER) {
            flightCountList = batchQueryCustomerFlightCount(demandNoList, tenantId);
        } else {
            flightCountList = batchQueryOpFlightCount(demandNoList, tenantId);
        }
        Map<String, Integer> demandFlightCountMap = flightCountList.stream().collect(Collectors.toMap(FlightCountVO::getBizNo, FlightCountVO::getCount));
        List<String> approveIdList = flightDemandDTOCommonPage.getList().stream().map(FlightDemandDTO::getApprovalId).collect(Collectors.toList());
        Map<String, List<ApprovalNode>> approvalNodeMap = approveInstanceService.getCurrentNeedToApproveNodeMap(approveIdList);
        String userNo = UserUtil.getUserNo();
        flightDemandDTOCommonPage.getList().forEach(flightDemandDTO -> {
            flightDemandDTO.setRealtimeFlightCount(demandFlightCountMap.getOrDefault(flightDemandDTO.getDemandNo(), 0));
            List<ApprovalNode> approvalNodeList = approvalNodeMap.get(flightDemandDTO.getApprovalId());
            if (CollectionUtil.isEmpty(approvalNodeList)) {
                flightDemandDTO.setAllowApprove(false);
            } else {
                flightDemandDTO.setAllowApprove(approvalNodeList.stream().anyMatch(approvalNode -> Objects.equals(approvalNode.getApproveUserId(), userNo)));
            }
            flightDemandDTO.setAllowEdit(Objects.equals(flightDemandDTO.getPublisherNo(), userNo));
        });

        return flightDemandDTOCommonPage;
    }

    private FlightDemandVO setDemandVO(FlightDemandDTO flightDemand, UserTypeEnum userType) {
        FlightDemandVO flightDemandVO = demandConvert.convertToVO(flightDemand);
        //获取增值服务
        List<String> incrementServiceProductNoList = flightDemand.getIncrementService();
        if (CollectionUtil.isNotEmpty(incrementServiceProductNoList)) {
            List<FlightProductDTO> incrementServiceList = productService.listByProductNoList(incrementServiceProductNoList);
            flightDemandVO.setIncrementServiceList(incrementServiceList);
        }

        //获取需求目前已经飞了多少次
        List<FlightCountVO> flightCountList = new ArrayList<>();
        if (userType == UserTypeEnum.CUSTOMER) {
            flightCountList = batchQueryCustomerFlightCount(ListUtil.toList(flightDemand.getDemandNo()), flightDemand.getTenantId());
        } else if (userType == UserTypeEnum.OPERATION || userType == UserTypeEnum.SUPPLIER) {
            flightCountList = batchQueryOpFlightCount(ListUtil.toList(flightDemand.getDemandNo()), flightDemand.getTenantId());
        }
        flightDemandVO.setRealtimeFlightCount(CollectionUtil.isEmpty(flightCountList) ? 0 : flightCountList.get(0).getCount());

        // 设置无人机机型
        List<String> modelCodeList = flightDemand.getModelCodeList();
        if (CollectionUtil.isNotEmpty(modelCodeList)) {
            List<FlightUavBmDTO> flightUavBmDTOList = modelCodeList.stream().map(modelCode -> flightUavBmService.getFlightUavBmByNo(modelCode)).collect(Collectors.toList());
            flightDemandVO.setFlightUavBmList(flightUavBmDTOList);
        }

        if (userType != null) {
            //设置审批结果
            List<String> allHistoryDemandCodeList = demandService.findAllHistoryDemandCode(flightDemand.getDemandNo());
            Map<String, List<ApprovalRecord>> approvalRecordMap = approveInstanceService.getApprovalRecordMap(allHistoryDemandCodeList);
            List<ApprovalRecord> recordList = approvalRecordMap
                    .values()
                    .stream()
                    .flatMap(List::stream)
                    .sorted(Comparator.comparing(ApprovalRecord::getApplyTime))
                    .collect(Collectors.toList());
            //设置审批人名字、组织
            flightDemandVO.setApprovalRecordList(approveConvert.toApprovalRecordVO(recordList));

            //设置是否允许审核
            List<ApprovalNode> approvalNodeList = approveInstanceService.getCurrentNeedToApproveNodeList(flightDemand.getApprovalId());
            String userNo = UserUtil.getUserNo();
            flightDemandVO.setAllowApprove(approvalNodeList.stream().anyMatch(approvalNode -> Objects.equals(approvalNode.getApproveUserId(), userNo)));

            flightDemandVO.setAllowEdit(Objects.equals(flightDemand.getPublisherNo(), userNo));
        }

        return flightDemandVO;
    }

    private List<FlightCountVO> batchQueryCustomerFlightCount(List<String> originalDemandCodeList, String tenantId) {
        BatchQueryPlanDTO batchQueryPlanDTO = new BatchQueryPlanDTO();
        batchQueryPlanDTO.setBizNo(originalDemandCodeList);
        batchQueryPlanDTO.setTenantId(tenantId);
        return flightPlanService.batchQueryFlightCountByCustomer(batchQueryPlanDTO);
    }

    private List<FlightCountVO> batchQueryOpFlightCount(List<String> demandCodeList, String tenantId) {
        BatchQueryPlanDTO batchQueryPlanDTO = new BatchQueryPlanDTO();
        batchQueryPlanDTO.setBizNo(demandCodeList);
        batchQueryPlanDTO.setTenantId(tenantId);
        return flightPlanQueryClient.planCountByBizNo(batchQueryPlanDTO);
    }

    public List<FlightDemandVO> getPassFlightDemandByNoList(List<String> demandNoList) {
        if (CollectionUtil.isEmpty(demandNoList)) {
            log.error("需求编号不能为空");
            throw new BizException("需求编号不能为空");
        }
        return demandService.getFlightDemandByNoList(demandNoList);

    }
}
