package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Getter
@Setter
@TableName("flight_plan_cycle")
public class FlightPlanCycleDO extends Model<FlightPlanCycleDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 周期编号
     */
    @TableField("cycle_no")
    private String cycleNo;

    /**
     * 周期名称
     */
    @TableField("cycle_name")
    private String cycleName;

    /**
     * 周期类型(YEAR; QUARTER; MONTH)
     */
    @TableField("cycle_type")
    private String cycleType;

    /**
     * 周期开始时间
     */
    @TableField("cycle_start")
    private LocalDate cycleStart;

    /**
     * 周期结束时间
     */
    @TableField("cycle_end")
    private LocalDate cycleEnd;

    /**
     * 创建人
     */
    @TableField("creator_name")
    private String creatorName;

    /**
     * 是否启用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
