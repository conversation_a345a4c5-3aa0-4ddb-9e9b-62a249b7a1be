package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Getter
@Setter
@TableName("flight_order_approval")
public class FlightOrderApprovalDO extends Model<FlightOrderApprovalDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 审核状态
     */
    @TableField("approval_status")
    private String approvalStatus;

    /**
     * 审核人编码
     */
    @TableField("approval_user_no")
    private String approvalUserNo;

    /**
     * 审核人名称
     */
    @TableField("approval_user_name")
    private String approvalUserName;

    /**
     * 审核时间
     */
    @TableField("approval_time")
    private Long approvalTime;

    /**
     * 审核备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 角色
     */
    @TableField("role")
    private String role;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 组织ID
     */
    @TableField("organization_id")
    private String organizationId;

    /**
     * 组织名称
     */
    @TableField("organization_name")
    private String organizationName;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
