package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandMergeHandleDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDemandMergeHandleMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandMergeHandleRepository;
import org.springframework.stereotype.Repository;

/**
 * 合并需求处理记录仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightDemandMergeHandleRepositoryImpl extends ServiceImpl<FlightDemandMergeHandleMapper, FlightDemandMergeHandleDO>
        implements FlightDemandMergeHandleRepository {
} 