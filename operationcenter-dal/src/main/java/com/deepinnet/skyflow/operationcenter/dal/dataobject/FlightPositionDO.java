package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 飞行区域/点位表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Getter
@Setter
@TableName("flight_position")
public class FlightPositionDO extends Model<FlightPositionDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("position_no")
    private String positionNo;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 类型(POINT; AREA)
     */
    @TableField("type")
    private String type;

    /**
     * 名称(点位/区域)
     */
    @TableField("name")
    private String name;

    /**
     * 飞行点位/区域
     */
    @TableField("flight_position")
    private String flightPosition;

    /**
     * 中心点
     */
    @TableField("center_point")
    private String centerPoint;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
