package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalStepDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.ApprovalStepDOMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.ApprovalStepRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 审批步骤Repository实现类
 * 
 * <AUTHOR>
 * @date 2025/1/17
 */
@Slf4j
@Repository
public class ApprovalStepRepositoryImpl extends ServiceImpl<ApprovalStepDOMapper, ApprovalStepDO> implements ApprovalStepRepository {
} 