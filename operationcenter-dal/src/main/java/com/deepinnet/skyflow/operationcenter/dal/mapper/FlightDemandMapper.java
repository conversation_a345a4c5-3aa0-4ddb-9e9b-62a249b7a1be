package com.deepinnet.skyflow.operationcenter.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandStatsDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDayStatsDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightDemandStatCondition;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 飞行需求 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FlightDemandMapper extends BaseMapper<FlightDemandDO> {

    FlightDemandStatsDO select90DayStats(@Param("condition") FlightDemandStatCondition condition);

    FlightDemandStatsDO selectDayStats(@Param("condition") FlightDemandStatCondition condition);

    FlightDemandStatsDO select90DayEmergencyResponseTimeStats(@Param("condition") FlightDemandStatCondition condition);

    FlightDemandStatsDO selectEmergencyResponseTimeStats(@Param("condition") FlightDemandStatCondition condition);

    List<FlightDemandDayStatsDO> select7DayStats(@Param("condition") FlightDemandStatCondition condition);

    /**
     * 分页查询待匹配的需求（距离起飞时间小于72小时）
     *
     * @param lastId 上次查询的最后一个ID，用于游标分页
     * @param limit 页大小
     * @return 需求列表
     */
    List<FlightDemandDO> selectPendingMatchDemands(@Param("lastId") Long lastId, @Param("limit") int limit);

    /**
     * 分页查询待合并的需求（距离起飞时间大于72小时）
     *
     * @param lastId 上次查询的最后一个ID，用于游标分页
     * @param limit 页大小
     * @return 需求列表
     */
    List<FlightDemandDO> selectPendingMergeDemands(@Param("lastId") Long lastId, @Param("limit") int limit);

    /**
     * 客户端分页查询飞行需求（只查询原始需求）
     *
     * @param queryDTO 查询条件
     * @return 需求列表
     */
    List<FlightDemandDO> pageQueryFlightDemandCustomerManage(@Param("queryDTO") FlightDemandQueryDTO queryDTO);

    /**
     * 运营端分页查询飞行需求（查询合并需求和未参与合并的原始需求）
     *
     * @param queryDTO 查询条件
     * @return 需求列表
     */
    List<FlightDemandDO> pageQueryFlightDemandOpManage(@Param("queryDTO") FlightDemandQueryDTO queryDTO);
}