package com.deepinnet.skyflow.operationcenter.dal.dataobject;

public class ApprovalTimeoutEventDO {

    //    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    //    instance_id VARCHAR(64) NOT NULL,
    //    node_id VARCHAR(64) NULL,
    //    timeout_type VARCHAR(16) NOT NULL, -- GLOBAL / NODE
    //    trigger_time DATETIME NOT NULL,
    //    action_type  VARCHAR(64) NOT NULL,
    //    action_params
    //    status VARCHAR(16) NOT NULL, -- WAITING / EXECUTED / CANCELED
    //    create_time DATETIME NOT NULL,
    //    update_time DATETIME NOT NULL
    //
}
