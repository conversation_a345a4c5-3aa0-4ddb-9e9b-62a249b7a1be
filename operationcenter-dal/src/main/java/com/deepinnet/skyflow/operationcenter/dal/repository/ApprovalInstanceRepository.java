package com.deepinnet.skyflow.operationcenter.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalInstanceDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalNodeDO;

import java.util.List;


/**
 * 审批服务接口 - 基于MyBatis Plus IService
 * 
 * <AUTHOR>
 * @date 2025/1/17
 */
public interface ApprovalInstanceRepository extends IService<ApprovalInstanceDO> {
    List<ApprovalNodeDO> queryPendingApprovalCountByUserNo(String type, String userNo);
}