package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandMatchServiceProviderDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDemandMatchServiceProviderMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandMatchServiceProviderRepository;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 需求匹配到的服务商 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Service
public class FlightDemandMatchServiceProviderRepositoryImpl extends ServiceImpl<FlightDemandMatchServiceProviderMapper, FlightDemandMatchServiceProviderDO> implements FlightDemandMatchServiceProviderRepository {

}
