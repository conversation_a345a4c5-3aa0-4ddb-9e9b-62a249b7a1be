package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 审批配置规则实体
 * 
 * <AUTHOR>
 * @date 2025/1/17
 */
@Data
@TableName("approval_config_rule")
public class ApprovalConfigRuleDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    @TableField("rule_name")
    private String ruleName;

    @TableField("rule_type")
    private String ruleType;

    @TableField("org_code")
    private String orgCode;

    @TableField("creator")
    private String creator;

    @TableField("editor")
    private String editor;

    @TableField("approval_choose_config_rule_list")
    private String approvalChooseConfigRuleList;

    @TableField("approve_timeout_config")
    private String approveTimeoutConfig;

    @TableField("approve_org_user_config")
    private String approveOrgUserConfig;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
} 