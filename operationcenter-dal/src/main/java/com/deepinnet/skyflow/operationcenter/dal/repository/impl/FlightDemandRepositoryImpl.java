package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.anno.AccountQuery;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandStatsDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDayStatsDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDemandMapper;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightDemandStatCondition;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandRepository;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandQueryDTO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 飞行需求仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightDemandRepositoryImpl extends ServiceImpl<FlightDemandMapper, FlightDemandDO>
        implements FlightDemandRepository {

    @AccountQuery
    @Override
    public FlightDemandStatsDO select90DayStats(FlightDemandStatCondition condition) {
        return getBaseMapper().select90DayStats(condition);
    }

    @Override
    public FlightDemandStatsDO selectStats(FlightDemandStatCondition condition) {
        return getBaseMapper().selectDayStats(condition);
    }

    @AccountQuery
    @Override
    public FlightDemandStatsDO select90DayEmergencyResponseTimeStats(FlightDemandStatCondition condition) {
        return getBaseMapper().select90DayEmergencyResponseTimeStats(condition);
    }

    @Override
    public FlightDemandStatsDO selectEmergencyResponseTimeStats(FlightDemandStatCondition condition) {
        return getBaseMapper().selectEmergencyResponseTimeStats(condition);
    }

    @AccountQuery
    @Override
    public List<FlightDemandDayStatsDO> select7DayStats(FlightDemandStatCondition condition) {
        return getBaseMapper().select7DayStats(condition);
    }

    @Override
    public List<FlightDemandDO> selectPendingMatchDemands(Long lastId, int limit) {
        return getBaseMapper().selectPendingMatchDemands(lastId, limit);
    }

    @Override
    public List<FlightDemandDO> selectPendingMergeDemands(Long lastId, int limit) {
        return getBaseMapper().selectPendingMergeDemands(lastId, limit);
    }

    @Override
    public List<FlightDemandDO> pageQueryFlightDemandCustomerManage(FlightDemandQueryDTO queryDTO) {
        return getBaseMapper().pageQueryFlightDemandCustomerManage(queryDTO);
    }

    @Override
    public List<FlightDemandDO> pageQueryFlightDemandOpManage(FlightDemandQueryDTO queryDTO) {
        return getBaseMapper().pageQueryFlightDemandOpManage(queryDTO);
    }


}