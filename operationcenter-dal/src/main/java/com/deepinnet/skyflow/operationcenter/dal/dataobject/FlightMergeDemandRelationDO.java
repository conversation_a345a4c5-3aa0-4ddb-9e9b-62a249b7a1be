package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Getter
@Setter
@TableName("flight_merge_demand_relation")
public class FlightMergeDemandRelationDO extends Model<FlightMergeDemandRelationDO> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 原需求id
     */
    @TableField("original_demand_code")
    private String originalDemandCode;

    /**
     * 目标需求id，可能是自己，需要根据target_type区分
     */
    @TableField("target_demand_code")
    private String targetDemandCode;

    /**
     * 合并处理编号
     */
    @TableField("merge_handle_code")
    private String mergeHandleCode;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 合并状态
     */
    @TableField("merge_status")
    private String mergeStatus;

    @TableField("type")
    private String type;

    @TableField("publisher_no")
    private String publisherNo;

    @TableField("flight_order_no")
    private String flightOrderNo;

    @TableField("merge_schedule_time")
    private String mergeScheduleTime;

    /**
     * 目标需求的类型，self、merge
     */
    @TableField("target_type")
    private String targetType;

    /**
     * 原始需求名称
     */
    @TableField("original_demand_name")
    private String originalDemandName;

    /**
     * 目标需求名称
     */
    @TableField("target_demand_name")
    private String targetDemandName;

    /**
     * 原始需求发布时间
     */
    @TableField("publish_time")
    private LocalDateTime publishTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
