package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandAreaDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDemandAreaMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandAreaRepository;
import org.springframework.stereotype.Service;

/**
 * 飞行需求区域仓储实现类
 *
 * <AUTHOR>
 */
@Service
public class FlightDemandAreaRepositoryImpl extends ServiceImpl<FlightDemandAreaMapper, FlightDemandAreaDO> implements FlightDemandAreaRepository {
}
