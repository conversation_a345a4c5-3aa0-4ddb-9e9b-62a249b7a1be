package com.deepinnet.skyflow.operationcenter.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDraftDO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDraftQueryDTO;

import java.util.List;

/**
 * 飞行草稿 Repository 接口
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface FlightDraftRepository extends IService<FlightDraftDO> {

    /**
     * 保存草稿
     *
     * @param flightDraftDO 草稿对象
     * @return 是否成功
     */
    boolean saveDraft(FlightDraftDO flightDraftDO);

    /**
     * 更新草稿
     *
     * @param flightDraftDO 草稿对象
     * @return 是否成功
     */
    boolean updateDraft(FlightDraftDO flightDraftDO);

    /**
     * 删除草稿
     *
     * @param code 草稿ID
     * @return 是否成功
     */
    boolean removeDraft(String code);

    /**
     * 根据ID查询草稿
     *
     * @param code 草稿ID
     * @return 草稿对象
     */
    FlightDraftDO getDraft(String code);

    /**
     * 根据条件查询草稿列表
     *
     * @return 草稿列表
     */
    List<FlightDraftDO> listDrafts(FlightDraftQueryDTO queryDTO);
}