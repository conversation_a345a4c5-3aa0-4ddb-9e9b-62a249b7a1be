package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPositionDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightPositionDao;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightPositionRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 飞行区域/点位表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
public class FlightPositionRepositoryImpl extends ServiceImpl<FlightPositionDao, FlightPositionDO> implements FlightPositionRepository {

}
