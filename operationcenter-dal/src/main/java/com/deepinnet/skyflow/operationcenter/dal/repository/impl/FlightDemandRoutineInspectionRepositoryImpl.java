package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandRoutineInspectionDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDemandRoutineInspectionMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandRoutineInspectionRepository;
import org.springframework.stereotype.Repository;

/**
 * 日常巡检飞行需求仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightDemandRoutineInspectionRepositoryImpl 
        extends ServiceImpl<FlightDemandRoutineInspectionMapper, FlightDemandRoutineInspectionDO>
        implements FlightDemandRoutineInspectionRepository {
} 