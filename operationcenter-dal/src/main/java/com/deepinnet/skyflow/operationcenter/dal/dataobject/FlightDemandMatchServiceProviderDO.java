package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 需求匹配到的服务商
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Getter
@Setter
@TableName("flight_demand_match_service_provider")
public class FlightDemandMatchServiceProviderDO extends Model<FlightDemandMatchServiceProviderDO> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 需求编号（原始或者合并需求）
     */
    @TableField("demand_code")
    private String demandCode;

    /**
     * 服务提供商编号
     */
    @TableField("service_provider_no")
    private String serviceProviderNo;

    /**
     * 服务商名称
     */
    @TableField("service_provider_name")
    private String serviceProviderName;

    /**
     * 服务提供商公司名称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 服务提供者组织id
     */
    @TableField("organization_id")
    private String organizationId;

    /**
     * 服务商分配方式
     */
    @TableField("assign_way")
    private String assignWay;

    /**
     * 分配服务商的用户
     */
    @TableField("assign_operator_user_no")
    private String assignOperatorUserNo;

    /**
     * 分配服务商的时间
     */
    @TableField("assign_time")
    private LocalDateTime assignTime;

    /**
     * 同步状态
     */
    @TableField("sync_status")
    private String syncStatus;

    /**
     * 同步订单号
     */
    @TableField("sync_order_no")
    private String syncOrderNo;

    /**
     * 同步失败原因
     */
    @TableField("sync_fail_reason")
    private String syncFailReason;

    /**
     * 提供商匹配到的服务周期
     */
    @TableField("merge_schedule_time")
    private String mergeScheduleTime;

    /**
     * 是否是合并需求
     */
    @TableField("is_merge_demand")
    private Boolean isMergeDemand;

    @TableField("tenant_id")
    private String tenantId;

    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;

    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
