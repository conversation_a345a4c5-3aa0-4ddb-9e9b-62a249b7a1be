package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.anno.AccountQuery;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandPlanStatDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDemandPlanStatMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandPlanStatRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 飞行需求计划统计仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightDemandPlanStatRepositoryImpl extends ServiceImpl<FlightDemandPlanStatMapper, FlightDemandPlanStatDO>
        implements FlightDemandPlanStatRepository {

    @AccountQuery
    @Override
    public FlightDemandPlanStatDO selectByStatDateAndUserCode(LocalDate statDate, String userCode) {
        return getBaseMapper().selectByStatDateAndUserCode(statDate, userCode);
    }

    @AccountQuery
    @Override
    public List<FlightDemandPlanStatDO> selectByStatDateRange(LocalDate startDate, LocalDate endDate, String userCode) {
        return getBaseMapper().selectByStatDateRange(startDate, endDate, userCode);
    }

    @AccountQuery
    @Override
    public List<FlightDemandPlanStatDO> selectByUserCode(String userCode) {
        return getBaseMapper().selectByUserCode(userCode);
    }

    @AccountQuery
    @Override
    public List<FlightDemandPlanStatDO> selectByStatDate(LocalDate statDate) {
        return getBaseMapper().selectByStatDate(statDate);
    }

    @Override
    public boolean saveOrUpdateStat(FlightDemandPlanStatDO statDO) {
        // 先查询是否已存在相同日期和用户的统计数据
        LambdaQueryWrapper<FlightDemandPlanStatDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlightDemandPlanStatDO::getStatDate, statDO.getStatDate())
                   .eq(FlightDemandPlanStatDO::getUserCode, statDO.getUserCode());
        
        FlightDemandPlanStatDO existingStat = getOne(queryWrapper);
        
        if (existingStat != null) {
            // 如果存在，则更新
            statDO.setId(existingStat.getId());
            return updateById(statDO);
        } else {
            // 如果不存在，则新增
            return save(statDO);
        }
    }

    @Override
    public List<FlightDemandPlanStatDO> selectUserCodesByStatDateRange(LocalDate startDate, LocalDate endDate, List<String> userCodes) {
        return baseMapper.selectUserCodesByStatDateRange(startDate, endDate, userCodes);
    }
} 