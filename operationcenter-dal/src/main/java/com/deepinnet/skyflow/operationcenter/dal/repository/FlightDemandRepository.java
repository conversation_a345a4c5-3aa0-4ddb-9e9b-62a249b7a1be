package com.deepinnet.skyflow.operationcenter.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandStatsDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDayStatsDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightDemandStatCondition;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandQueryDTO;

import java.util.List;

/**
 * 飞行需求仓储接口
 *
 * <AUTHOR>
 */
public interface FlightDemandRepository extends IService<FlightDemandDO> {

    FlightDemandStatsDO select90DayStats(FlightDemandStatCondition condition);

    FlightDemandStatsDO selectStats(FlightDemandStatCondition condition);

    FlightDemandStatsDO select90DayEmergencyResponseTimeStats(FlightDemandStatCondition condition);

    FlightDemandStatsDO selectEmergencyResponseTimeStats(FlightDemandStatCondition condition);

    List<FlightDemandDayStatsDO> select7DayStats(FlightDemandStatCondition condition);

    /**
     * 分页查询待匹配的需求（距离起飞时间小于72小时）
     *
     * @param lastId 上次查询的最后一个ID，用于游标分页
     * @param limit 页大小
     * @return 需求列表
     */
    List<FlightDemandDO> selectPendingMatchDemands(Long lastId, int limit);

    /**
     * 分页查询待合并的需求（距离起飞时间大于72小时）
     *
     * @param lastId 上次查询的最后一个ID，用于游标分页
     * @param limit 页大小
     * @return 需求列表
     */
    List<FlightDemandDO> selectPendingMergeDemands(Long lastId, int limit);

    /**
     * 客户端分页查询飞行需求（只查询原始需求）
     *
     * @param queryDTO 查询条件
     * @return 需求列表
     */
    List<FlightDemandDO> pageQueryFlightDemandCustomerManage(FlightDemandQueryDTO queryDTO);

    /**
     * 运营端分页查询飞行需求（查询合并需求和未参与合并的原始需求）
     *
     * @param queryDTO 查询条件
     * @return 需求列表
     */
    List<FlightDemandDO> pageQueryFlightDemandOpManage(FlightDemandQueryDTO queryDTO);
}