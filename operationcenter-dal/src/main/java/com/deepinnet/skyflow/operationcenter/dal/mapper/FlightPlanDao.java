package com.deepinnet.skyflow.operationcenter.dal.mapper;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightPlanPageQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 飞行计划表，每个飞行任务包含多个飞行计划 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface FlightPlanDao extends BaseMapper<FlightPlanDO> {

    /**
     * 根据查询条件获取飞行计划列表
     * @param flightPlanPageQuery 查询条件
     * @return 飞行计划列表
     */
    List<FlightPlanDO> getFlightPlanListByPage(@Param("planQuery") FlightPlanPageQuery flightPlanPageQuery);

    /**
     * 根据查询条件获取飞行计划列表 统计使用
     * @param flightPlanPageQuery 查询条件
     * @return 飞行计划列表
     */
    List<FlightPlanDO> getFlightPlanList(@Param("planQuery") FlightPlanPageQuery flightPlanPageQuery);

    /**
     * 根据查询条件获取截止至昨天的所有飞行计划数据 统计使用
     * @param flightPlanPageQuery 查询条件
     * @return 飞行计划列表
     */
    List<FlightPlanDO> getYesterdayFlightPlanList(@Param("planQuery") FlightPlanPageQuery flightPlanPageQuery);

    /**
     * 根据订单编号查询飞行计划列表
     * @param orderNo 订单编号
     * @return 计划列表
     */
    List<FlightPlanDO> getFlightPlanListByOrderNo(@Param("orderNo") String orderNo, @Param("demandNo") String demandNo);
}
