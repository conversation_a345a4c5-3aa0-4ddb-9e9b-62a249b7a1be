package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 日常巡检飞行需求表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_demand_routine_inspection")
public class FlightDemandRoutineInspectionDO extends Model<FlightDemandRoutineInspectionDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 需求编号
     */
    @TableField("demand_no")
    private String demandNo;

    /**
     * 周期类型
     */
    @TableField("cycle_type")
    private String cycleType;

    /**
     * 需求开始时间
     */
    @TableField("demand_start_time")
    private LocalDate demandStartTime;

    /**
     * 需求结束时间
     */
    @TableField("demand_end_time")
    private LocalDate demandEndTime;

    /**
     * 飞行开始时间
     */
    @TableField("flying_start_time")
    private LocalTime flyingStartTime;

    /**
     * 飞行结束时间
     */
    @TableField("flying_end_time")
    private LocalTime flyingEndTime;

    /**
     * 飞行频率
     */
    @TableField("flying_frequency")
    private String flyingFrequency;

    /**
     * 飞行次数
     */
    @TableField("flying_num")
    private Integer flyingNum;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;
} 