package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 飞行需求计划统计表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_demand_plan_stat")
@AllArgsConstructor
@NoArgsConstructor
public class FlightDemandPlanStatDO extends Model<FlightDemandPlanStatDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计日期
     */
    @TableField("stat_date")
    private LocalDate statDate;

    /**
     * 用户编号
     */
    @TableField("user_code")
    private String userCode;

    /**
     * 已审核的规划数
     */
    @TableField("approved_order_num")
    private Long approvedOrderNum;

    /**
     * 有效飞行需求数
     */
    @TableField("valid_demand_num")
    private Long validDemandNum;

    /**
     * 飞行计划总数
     */
    @TableField("total_plan_num")
    private Long totalPlanNum;

    /**
     * 待飞行计划数
     */
    @TableField("ready_plan_num")
    private Long readyPlanNum;

    /**
     * 已飞行计划数
     */
    @TableField("complete_plan_num")
    private Long completePlanNum;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
}