package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalNodeDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.ApprovalNodeDOMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.ApprovalNodeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 审批节点Repository实现类
 * 
 * <AUTHOR>
 * @date 2025/1/17
 */
@Slf4j
@Repository
public class ApprovalNodeRepositoryImpl extends ServiceImpl<ApprovalNodeDOMapper, ApprovalNodeDO> implements ApprovalNodeRepository {
} 