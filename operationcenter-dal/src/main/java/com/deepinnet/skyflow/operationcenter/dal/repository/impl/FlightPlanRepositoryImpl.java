package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.deepinnet.skyflow.operationcenter.dal.anno.AccountQuery;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightPlanDao;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightPlanPageQuery;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightPlanRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 飞行计划表，每个飞行任务包含多个飞行计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
public class FlightPlanRepositoryImpl extends ServiceImpl<FlightPlanDao, FlightPlanDO> implements FlightPlanRepository {

    @AccountQuery
    @Override
    public List<FlightPlanDO> getFlightPlanListByPage(FlightPlanPageQuery flightPlanPageQuery) {
        return baseMapper.getFlightPlanListByPage(flightPlanPageQuery);
    }

    @Override
    public List<FlightPlanDO> getFlightPlanList(FlightPlanPageQuery flightPlanPageQuery) {
        return baseMapper.getFlightPlanList(flightPlanPageQuery);
    }

    @Override
    public List<FlightPlanDO> getYesterdayFlightPlanList(FlightPlanPageQuery flightPlanPageQuery) {
        return baseMapper.getYesterdayFlightPlanList(flightPlanPageQuery);
    }

    @Override
    public List<FlightPlanDO> getFlightPlanListByOrderNo(String orderNo, String demandNo) {
        return baseMapper.getFlightPlanListByOrderNo(orderNo, demandNo);
    }

}
