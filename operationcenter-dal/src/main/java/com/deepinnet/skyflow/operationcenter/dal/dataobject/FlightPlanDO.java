package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 飞行计划表，每个飞行任务包含多个飞行计划
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Getter
@Setter
@TableName("flight_plan")
public class FlightPlanDO extends Model<FlightPlanDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField("mission_id")
    private String missionId;

    /**
     * 飞行计划唯一标识
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 无人机ID
     */
    @TableField("uav_id")
    private String uavId;

    /**
     * 飞行计划状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 计划起飞时间
     */
    @TableField("planed_takeoff_time")
    private Long planedTakeoffTime;

    /**
     * 计划降落时间
     */
    @TableField("planed_landing_time")
    private Long planedLandingTime;

    /**
     * 飞手（操作员）
     */
    @TableField("operator_user")
    private String operatorUser;

    /**
     * 飞手联系电话
     */
    @TableField("operator_phone")
    private String operatorPhone;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 起降场
     */
    @TableField("landing_aerodrome_id")
    private String landingAerodromeId;

    /**
     * 降落机场（弃用）
     */
    @TableField("arrival_aerodrome_id")
    private String arrivalAerodromeId;

    /**
     * 备降机场（弃用）
     */
    @TableField("alternate_aerodrome_id")
    private String alternateAerodromeId;

    /**
     * 计划飞行高度
     */
    @TableField("planed_altitude")
    private String planedAltitude;

    /**
     * 是否实名
     */
    @TableField("real_ame_verification")
    private Integer realAmeVerification;

    /**
     * 飞行单位
     */
    @TableField("flight_unit")
    private String flightUnit;

    /**
     * 飞行单位ID
     */
    @TableField("flight_unit_id")
    private String flightUnitId;

    /**
     * 申请用户名称
     */
    @TableField("apply_user_name")
    private String applyUserName;

    /**
     * 空域id
     */
    @TableField("airspace_id")
    private String airspaceId;

    /**
     * 场景code
     */
    @TableField("scence_code")
    private String scenceCode;

    /**
     * 业务单据号(需求编号)
     */
    @TableField("biz_no")
    private String bizNo;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 关联长期计划的ID
     */
    @TableField("related_id")
    private String relatedId;

    /**
     * 申请计划类型: 1-长期;2-次日;3-当日
     */
    @TableField("apply_type")
    private Integer applyType;

    /**
     * 服务商计划编号
     */
    @TableField("out_plan_no")
    private String outPlanNo;

    /**
     * 计划名称
     */
    @TableField("plan_name")
    private String planName;

    /**
     * 需求名称
     */
    @TableField("requirement_name")
    private String requirementName;

    /**
     * 无人机机型
     */
    @TableField("uav_model")
    private String uavModel;

    /**
     * 飞行报告
     */
    @TableField("flight_report")
    private String flightReport;

    /**
     * 监控地址
     */
    @TableField("monitor_url")
    private String monitorUrl;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
