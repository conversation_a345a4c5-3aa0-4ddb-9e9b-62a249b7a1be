package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightMergeDemandRelationDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightMergeDemandRelationDao;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightMergeDemandRelationRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Service
public class FlightMergeDemandRelationRepositoryImpl extends ServiceImpl<FlightMergeDemandRelationDao, FlightMergeDemandRelationDO> implements FlightMergeDemandRelationRepository {

}
