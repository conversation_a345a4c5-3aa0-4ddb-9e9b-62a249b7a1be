package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDraftDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDraftMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDraftRepository;
import com.deepinnet.skyflow.operationcenter.dto.FlightDraftQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 飞行草稿 Repository 实现类
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Repository
public class FlightDraftRepositoryImpl extends ServiceImpl<FlightDraftMapper, FlightDraftDO> implements FlightDraftRepository {

    @Override
    public boolean saveDraft(FlightDraftDO flightDraftDO) {
        return save(flightDraftDO);
    }

    @Override
    public boolean updateDraft(FlightDraftDO flightDraftDO) {
        flightDraftDO.setGmtModified(LocalDateTime.now());
        return update(flightDraftDO, Wrappers.lambdaUpdate(FlightDraftDO.class)
                .eq(FlightDraftDO::getCode, flightDraftDO.getCode())
        );
    }

    @Override
    public boolean removeDraft(String code) {
        return update(Wrappers.lambdaUpdate(FlightDraftDO.class)
                .set(FlightDraftDO::getIsDeleted, true)
                .eq(FlightDraftDO::getCode, code));
    }

    @Override
    public FlightDraftDO getDraft(String code) {
        return getOne(Wrappers.lambdaQuery(FlightDraftDO.class)
                .eq(FlightDraftDO::getCode, code));
    }

    @Override
    public List<FlightDraftDO> listDrafts(FlightDraftQueryDTO queryDTO) {
        LambdaQueryWrapper<FlightDraftDO> queryWrapper = buildQueryWrapper(queryDTO);
        return list(queryWrapper);
    }

    /**
     * 构建查询条件
     *
     * @param queryDTO      草稿
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<FlightDraftDO> buildQueryWrapper(FlightDraftQueryDTO queryDTO) {
        LambdaQueryWrapper<FlightDraftDO> queryWrapper = Wrappers.lambdaQuery(FlightDraftDO.class);

        // 按名称模糊查询
        queryWrapper.like(StringUtils.isNotBlank(queryDTO.getName()), FlightDraftDO::getName, queryDTO.getName());

        queryWrapper.eq(StringUtils.isNotBlank(queryDTO.getDraftCode()), FlightDraftDO::getCode, queryDTO.getDraftCode());

        // 按创建人精确查询
        queryWrapper.in(CollectionUtils.isNotEmpty(queryDTO.getCreatorList()), FlightDraftDO::getCreator, queryDTO.getCreatorList());

        // 按创建时间范围查询
        queryWrapper.ge(queryDTO.getStartTime() != null, FlightDraftDO::getCreateTime, queryDTO.getStartTime());

        queryWrapper.le(queryDTO.getEndTime() != null, FlightDraftDO::getCreateTime, queryDTO.getEndTime());

        queryWrapper.in(CollectionUtils.isNotEmpty(queryDTO.getTypeList()), FlightDraftDO::getBizType, queryDTO.getTypeList());

        queryWrapper.eq(StringUtils.isNotBlank(queryDTO.getBizCode()), FlightDraftDO::getBizCode, queryDTO.getBizCode());

        // 按创建时间降序排序
        queryWrapper.orderByDesc(FlightDraftDO::getGmtModified);

        return queryWrapper;
    }
}