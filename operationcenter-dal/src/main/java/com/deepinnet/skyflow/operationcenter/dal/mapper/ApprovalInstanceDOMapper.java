package com.deepinnet.skyflow.operationcenter.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalInstanceDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalNodeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 审批实例Mapper
 * 
 * <AUTHOR>
 * @date 2025/1/17
 */
@Mapper
public interface ApprovalInstanceDOMapper extends BaseMapper<ApprovalInstanceDO> {

    List<ApprovalNodeDO> queryPendingApprovalCountByUserNo(@Param("type") String type, @Param("userNo") String userNo);
}