package com.deepinnet.skyflow.operationcenter.dal.model;


import com.deepinnet.skyflow.operationcenter.util.enums.UserTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/21
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightPlanPageQuery {

    private String planName;

    private String requirementId;

    private String requirementName;

    private String requirementType;

    private String uavModel;

    private List<String> userNo;

    private String orderNo;

    private String flightUnitId;

    private String flightUnitName;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String orderBy;

    private String account;

    /**
     * 默认为客户权限
     */
    private String userType = UserTypeEnum.CUSTOMER.getCode();
}
