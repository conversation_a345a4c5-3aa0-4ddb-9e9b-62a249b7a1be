package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandEmergencyResponseDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDemandEmergencyResponseMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandEmergencyResponseRepository;
import org.springframework.stereotype.Repository;

/**
 * 应急处置飞行需求仓储实现类
 *
 * <AUTHOR>
 */
@Repository
public class FlightDemandEmergencyResponseRepositoryImpl 
        extends ServiceImpl<FlightDemandEmergencyResponseMapper, FlightDemandEmergencyResponseDO>
        implements FlightDemandEmergencyResponseRepository {
} 