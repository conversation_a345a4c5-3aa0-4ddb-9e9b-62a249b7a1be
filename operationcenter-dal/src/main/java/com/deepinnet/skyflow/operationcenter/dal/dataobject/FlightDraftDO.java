package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 飞行草稿表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Getter
@Setter
@TableName("flight_draft")
public class FlightDraftDO extends Model<FlightDraftDO> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 草稿编码
     */
    @TableField("code")
    private String code;
    
    /**
     * 草稿名称
     */
    @TableField("name")
    private String name;

    /**
     * 草稿创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 草稿创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 草稿业务类型
     */
    @TableField("biz_type")
    private String bizType;

    /**
     * 业务编码
     */
    @TableField("biz_code")
    private String bizCode;

    /**
     * 草稿数据
     */
    @TableField("json_data")
    private String jsonData;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}