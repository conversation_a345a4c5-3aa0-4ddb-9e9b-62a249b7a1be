package com.deepinnet.skyflow.operationcenter.dal.config;

import com.deepinnet.infra.api.enums.SceneEnum;
import com.deepinnet.skyflow.operationcenter.dal.anno.AccountQuery;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

/**
 * C<PERSON> zengjuerui
 * Date 2025-06-16
 **/

@Aspect
@Component
public class AccountQueryAspect {


    @Before("@annotation(accountQuery) && args(query,..)")
    public void modifyQuery(Object query, AccountQuery accountQuery) {
        if (shouldModify()) {
            setFieldSafely(query, accountQuery.fieldName(), UserUtil.getAccount());
            for (String s : accountQuery.clearFieldNames()) {
                clearFieldSafely(query, s);
            }

            if(accountQuery.needSetUserType()) {
                setFieldSafely(query, "userType", "supplier");
            }
         }
    }

    private void setFieldSafely(Object target, String fieldName, Object value) {
        ReflectionUtils.doWithFields(target.getClass(), field -> {
            field.setAccessible(true);
            field.set(target, value);
        }, field -> field.getName().equals(fieldName));
    }

    private void clearFieldSafely(Object target, String fieldName) {
        ReflectionUtils.doWithFields(target.getClass(), field -> {
            field.setAccessible(true);
            field.set(target, null);
        }, field -> field.getName().equals(fieldName));
    }

    private boolean shouldModify() {
        // 交警传过来的 token 才可以用
        return SceneEnum.TRAFFIC_POLICE.getCode().equals(UserUtil.getSceneEnum());
    }
}
