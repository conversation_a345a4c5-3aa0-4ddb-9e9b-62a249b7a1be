package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.deepinnet.skyflow.operationcenter.dal.config.DataPermissionColumn;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 飞行需求表实体类
 *
 * <AUTHOR>
 */
@DataPermissionColumn("publisher_no")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_demand")
public class FlightDemandDO extends Model<FlightDemandDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 需求编号
     */
    @TableField("demand_no")
    private String demandNo;

    /**
     * 需求名称
     */
    @TableField("name")
    private String name;

    /**
     * 需求描述
     */
    @TableField("demand_desc")
    private String demandDesc;

    /**
     * 需求类型
     */
    @TableField("type")
    private String type;

    /**
     * 需求来源场景
     */
    @TableField("scene")
    private String scene;

    /**
     * 发布者编号
     */
    @TableField("publisher_no")
    private String publisherNo;

    /**
     * 发布者名称
     */
    @TableField(value = "publisher_name")
    private String publisherName;

    /**
     * 发布时间
     */
    @TableField("publish_time")
    private LocalDateTime publishTime;

    /**
     * 编辑者编号
     */
    @TableField("editor_no")
    private String editorNo;

    /**
     * 编辑者名称
     */
    @TableField("editor_name")
    private String editorName;

    /**
     * 编辑时间
     */
    @TableField("edit_time")
    private LocalDateTime editTime;

    /**
     * 飞行订单编号
     */
    @TableField("flight_order_no")
    private String flightOrderNo;

    @TableField("product_name")
    private String productName;

    /**
     * 飞行无人机型号
     */
    @TableField("flight_uav_bm")
    private String flightUavBm;

    /**
     * 巡检区域名称
     */
    @TableField("inspection_area_name")
    private String inspectionAreaName;

    /**
     * 巡检区域编码, 格式: provinceCode/cityCode/countryCode/streetCode
     */
    @TableField("inspection_area_code")
    private String inspectionAreaCode;

    /**
     * 增值服务
     */
    @TableField("increment_service")
    private String incrementService;

    /**
     * 机型编号列表
     */
    @TableField("model_code_list")
    private String modelCodeList;

    /**
     * 附加文件请求
     */
    @TableField("request_additional_files")
    private String requestAdditionalFiles;

    /**
     * 详细类目编号
     */
    @TableField("category_no")
    private String categoryNo;

    @TableField("category_full_name")
    private String categoryFullName;

    /**
     * 匹配状态
     */
    @TableField("match_status")
    private String matchStatus;

    /**
     * 区域面积，平方公里
     */
    @TableField("area")
    private Double area;

    /**
     * 中心点坐标-经度
     */
    @TableField("center_point_longitude")
    private String centerPointLongitude;

    /**
     * 中心点坐标-纬度
     */
    @TableField("center_point_latitude")
    private String centerPointLatitude;

    /**
     * 区域坐标，WKT字符串
     */
    @TableField("area_coordinate")
    private String areaCoordinate;

    /**
     * 客户组织ID
     */
    @TableField(value = "organization_id")
    private String organizationId;

    /**
     * 客户组织名称
     */
    @TableField(value = "organization_name")
    private String organizationName;

    /**
     * 合并状态，待合并、未参与合并、不能合并、已合并、取消合并
     */
    @TableField("merge_status")
    private String mergeStatus;

    /**
     * 合并处理编号
     */
    @TableField("merge_handle_code")
    private String mergeHandleCode;

    /**
     * 是否是合并需求
     */
    @TableField("is_merge_demand")
    private Boolean isMergeDemand;

    /**
     * 周期类型
     */
    @TableField("cycle_type")
    private String cycleType;

    /**
     * 需求开始时间
     */
    @TableField("demand_start_time")
    private LocalDate demandStartTime;

    /**
     * 需求结束时间
     */
    @TableField("demand_end_time")
    private LocalDate demandEndTime;

    /**
     * 飞行开始时间
     */
    @TableField("flying_start_time")
    private LocalTime flyingStartTime;

    /**
     * 飞行结束时间
     */
    @TableField("flying_end_time")
    private LocalTime flyingEndTime;

    /**
     * 总飞行次数
     */
    @TableField("total_flying_num")
    private Integer totalFlyingNum;

    /**
     * 区域数量
     */
    @TableField("area_num")
    private Integer areaNum;

    /**
     * 审核状态
     */
    @TableField("approve_status")
    private String approveStatus;

    /**
     * 审核结束时间
     */
    @TableField("approve_end_time")
    private LocalDateTime approveEndTime;

    /**
     * 审批编号
     */
    @TableField("approval_id")
    private String approvalId;

    /**
     * 飞行频率
     */
    @TableField("flying_frequency")
    private String flyingFrequency;

    /**
     * 飞行次数
     */
    @TableField("flying_num")
    private Integer flyingNum;

    /**
     * 上个版本的需求编号
     */
    @TableField("parent_code")
    private String parentCode;

    /**
     * 是否最新数据
     */
    @TableField("is_latest")
    private Boolean isLatest;

    /**
     * 版本组编号
     */
    @TableField("group_code")
    private String groupCode;

    /**
     * 版本
     */
    @TableField("version")
    private Integer version;

    /**
     * 是否补录
     */
    @TableField("is_supplement")
    private Boolean isSupplement;

    /**
     * 业务数据
     */
    @TableField("biz_data")
    private String bizData;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
} 