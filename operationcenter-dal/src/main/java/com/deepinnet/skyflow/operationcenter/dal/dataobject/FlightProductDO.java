package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.deepinnet.skyflow.operationcenter.dal.typehandler.ProductShelfStatusEnumTypeHandler;
import com.deepinnet.skyflow.operationcenter.enums.ProductShelfStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 飞行产品表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_product")
public class FlightProductDO extends Model<FlightProductDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 产品编码
     */
    @TableField("product_no")
    private String productNo;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 产品描述
     */
    @TableField("product_description")
    private String productDescription;

    /**
     * 产品详情
     */
    @TableField("product_detail")
    private String productDetail;

    /**
     * 产品内容
     */
    @TableField("product_content")
    private String productContent;

    /**
     * 产品类型
     */
    @TableField("product_type")
    private String productType;

    /**
     * 产品服务类型
     */
    @TableField("product_service_type")
    private String productServiceType;

    /**
     * 关联类目编号
     */
    @TableField("category_no")
    private String categoryNo;

    /**
     * 关联类目名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 服务提供商编号
     */
    @TableField("service_provider_no")
    private String serviceProviderNo;

    /**
     * 产品状态
     */
    @TableField("product_status")
    private String productStatus;

    /**
     * 产品上下架状态
     */
    @TableField(value = "product_shelf_status", typeHandler = ProductShelfStatusEnumTypeHandler.class)
    private ProductShelfStatusEnum productShelfStatus;

    /**
     * 最低价
     */
    @TableField("product_min_price")
    private BigDecimal productMinPrice;

    /**
     * 图片列表
     */
    @TableField(value = "product_pictures", typeHandler = com.deepinnet.skyflow.operationcenter.dal.typehandler.PostgresTextArrayTypeHandler.class)
    private String[] productPictures;

    /**
     * 机型列表
     */
    @TableField(value = "flight_uav_bm_list", typeHandler = com.deepinnet.skyflow.operationcenter.dal.typehandler.PostgresTextArrayTypeHandler.class)
    private String[] flightUavBmList;

    /**
     * 关联类目编号列表
     */
    @TableField(value = "category_no_list", typeHandler = com.deepinnet.skyflow.operationcenter.dal.typehandler.PostgresTextArrayTypeHandler.class)
    private String[] categoryNoList;

    /**
     * 是否提供飞手服务
     */
    @TableField("support_uav_pilot")
    private Boolean supportUavPilot;

    /**
     * 租户
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
} 