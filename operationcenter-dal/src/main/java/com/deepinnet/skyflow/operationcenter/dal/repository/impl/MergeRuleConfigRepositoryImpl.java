package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.MergeRuleConfigDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.MergeRuleConfigDao;
import com.deepinnet.skyflow.operationcenter.dal.repository.MergeRuleConfigRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Service
public class MergeRuleConfigRepositoryImpl extends ServiceImpl<MergeRuleConfigDao, MergeRuleConfigDO> implements MergeRuleConfigRepository {

}
