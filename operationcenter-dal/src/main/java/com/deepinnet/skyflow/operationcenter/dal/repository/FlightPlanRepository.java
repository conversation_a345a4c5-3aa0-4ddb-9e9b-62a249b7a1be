package com.deepinnet.skyflow.operationcenter.dal.repository;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightPlanPageQuery;

import java.util.List;

/**
 * <p>
 * 飞行计划表，每个飞行任务包含多个飞行计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface FlightPlanRepository extends IService<FlightPlanDO> {

    /**
     * 分页查询飞行计划(分为用户查询与服务商查询)
     * @param flightPlanPageQuery 查询条件
     * @return 分页结果
     */
    List<FlightPlanDO> getFlightPlanListByPage(FlightPlanPageQuery flightPlanPageQuery);

    /**
     * 一网统飞 工作台统计当日飞行计划
     * @param flightPlanPageQuery 查询条件
     * @return 查询结果
     */
    List<FlightPlanDO> getFlightPlanList(FlightPlanPageQuery flightPlanPageQuery);

    /**
     * 一网统飞 工作台统计截止到昨天的飞行计划
     * @param flightPlanPageQuery 查询条件
     * @return 查询结果
     */
    List<FlightPlanDO> getYesterdayFlightPlanList(FlightPlanPageQuery flightPlanPageQuery);

    /**
     * 根据订单编号查询计划列表
     * @param orderNo  订单编号
     * @param demandNo 需求编号
     * @return 计划列表
     */
    List<FlightPlanDO> getFlightPlanListByOrderNo(String orderNo, String demandNo);
}
