package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandStatsDTO;
import lombok.Data;

import java.util.Objects;

/**
 * C<PERSON> zengjuerui
 * Date 2025-05-29
 **/

@Data
public class FlightDemandStatsDO {

    private Integer demandNum;
    private Integer planNum;
    private Integer planFinishedNum;
    private Double flightDuration;
    private Double flightMiles;
    private Double averageResponseTime;
    private Double aerialPatrolArea;
    private Double aerialPatrolEventDetected;

    public FlightDemandStatsDTO view() {
        FlightDemandStatsDTO res = new FlightDemandStatsDTO();
        res.setPlanNum(getPlanNum());
        res.setDemandNum(getDemandNum());
        res.setPlanFinishedNum(getPlanFinishedNum());

        res.setFlightHours(String.format("%.1f", Objects.requireNonNullElse(getFlightDuration(), 0D) / 3600));
        res.setFlightDistance(String.format("%.1f", Objects.requireNonNullElse(getFlightMiles(), 0D) / 1000));
        res.setAerialPatrolArea(String.format("%.1f", Objects.requireNonNullElse(getAerialPatrolArea(), 0D)));

        return res;
    }
}
