package com.deepinnet.skyflow.operationcenter.dal.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * C<PERSON> zengjuerui
 * Date 2025-06-16
 * 龙岗交警内网数据是摆渡进去的，没有对应的 userNo，只能通过 account 去查询
 **/

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AccountQuery {

    String fieldName() default "account";

    String[] clearFieldNames() default {"userNo", "userNos"};

    boolean needSetUserType() default false;
}
