package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 飞行需求区域表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_demand_area")
public class FlightDemandAreaDO extends Model<FlightDemandAreaDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 需求编号
     */
    @TableField("demand_code")
    private String demandCode;

    /**
     * 需求名称
     */
    @TableField("demand_name")
    private String demandName;

    @TableField("area_code")
    private String areaCode;

    @TableField("area_name")
    private String areaName;

    /**
     * 区域的序号，从1开始
     */
    @TableField("sequence")
    private Integer sequence;

    /**
     * 类型(POINT; AREA)
     */
    @TableField("type")
    private String type;

    /**
     * 中心点坐标-经度
     */
    @TableField("center_point_longitude")
    private String centerPointLongitude;

    /**
     * 中心点坐标-纬度
     */
    @TableField("center_point_latitude")
    private String centerPointLatitude;

    /**
     * 区域坐标，WKT字符串
     */
    @TableField("area_coordinate")
    private String areaCoordinate;

    /**
     * 区域面积，平方公里
     */
    @TableField("area")
    private Double area;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
