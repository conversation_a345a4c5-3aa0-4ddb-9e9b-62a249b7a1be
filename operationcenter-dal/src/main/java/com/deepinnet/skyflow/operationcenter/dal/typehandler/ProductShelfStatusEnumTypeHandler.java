package com.deepinnet.skyflow.operationcenter.dal.typehandler;

import com.deepinnet.skyflow.operationcenter.enums.ProductShelfStatusEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;

/**
 * 产品上下架状态枚举类型处理器
 *
 * <AUTHOR>
 */
public class ProductShelfStatusEnumTypeHandler extends BaseTypeHandler<ProductShelfStatusEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, ProductShelfStatusEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getCode());
    }

    @Override
    public ProductShelfStatusEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String code = rs.getString(columnName);
        return getProductShelfStatusEnum(code);
    }

    @Override
    public ProductShelfStatusEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String code = rs.getString(columnIndex);
        return getProductShelfStatusEnum(code);
    }

    @Override
    public ProductShelfStatusEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String code = cs.getString(columnIndex);
        return getProductShelfStatusEnum(code);
    }

    private ProductShelfStatusEnum getProductShelfStatusEnum(String code) {
        if (code == null) {
            return null;
        }
        
        for (ProductShelfStatusEnum statusEnum : ProductShelfStatusEnum.values()) {
            if (Objects.equals(statusEnum.getCode(), code)) {
                return statusEnum;
            }
        }
        
        return null;
    }
} 