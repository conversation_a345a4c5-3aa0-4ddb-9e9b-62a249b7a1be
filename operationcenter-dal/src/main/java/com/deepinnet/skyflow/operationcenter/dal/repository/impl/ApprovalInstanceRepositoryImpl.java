package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalInstanceDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalNodeDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.ApprovalInstanceDOMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.ApprovalInstanceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 审批服务实现类 - 基于MyBatis Plus ServiceImpl
 * 
 * <AUTHOR>
 * @date 2025/1/17
 */
@Slf4j
@Repository
public class ApprovalInstanceRepositoryImpl extends ServiceImpl<ApprovalInstanceDOMapper, ApprovalInstanceDO> implements ApprovalInstanceRepository {
    @Override
    public List<ApprovalNodeDO> queryPendingApprovalCountByUserNo(String type, String userNo) {
        return baseMapper.queryPendingApprovalCountByUserNo(type, userNo);
    }
}