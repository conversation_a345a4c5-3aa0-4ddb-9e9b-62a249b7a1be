package com.deepinnet.skyflow.operationcenter.dal.repository.impl;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalConfigRuleDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.ApprovalConfigRuleMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.ApprovalConfigRuleRepository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 审批配置规则 Repository 实现类
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Service
public class ApprovalConfigRuleRepositoryImpl extends ServiceImpl<ApprovalConfigRuleMapper, ApprovalConfigRuleDO> implements ApprovalConfigRuleRepository {

} 