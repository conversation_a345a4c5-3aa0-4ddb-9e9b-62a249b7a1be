package com.deepinnet.skyflow.operationcenter.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandPlanStatDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 飞行需求计划统计 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FlightDemandPlanStatMapper extends BaseMapper<FlightDemandPlanStatDO> {

    /**
     * 根据统计日期和用户编号查询统计数据
     *
     * @param statDate 统计日期
     * @param userCode 用户编号
     * @return 统计数据
     */
    FlightDemandPlanStatDO selectByStatDateAndUserCode(@Param("statDate") LocalDate statDate, @Param("userCode") String userCode);

    /**
     * 根据统计日期范围查询统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param userCode 用户编号
     * @return 统计数据列表
     */
    List<FlightDemandPlanStatDO> selectByStatDateRange(@Param("startDate") LocalDate startDate, 
                                                       @Param("endDate") LocalDate endDate, 
                                                       @Param("userCode") String userCode);

    /**
     * 根据用户编号查询统计数据
     *
     * @param userCode 用户编号
     * @return 统计数据列表
     */
    List<FlightDemandPlanStatDO> selectByUserCode(@Param("userCode") String userCode);

    /**
     * 根据统计日期查询所有用户的统计数据
     *
     * @param statDate 统计日期
     * @return 统计数据列表
     */
    List<FlightDemandPlanStatDO> selectByStatDate(@Param("statDate") LocalDate statDate);

    /**
     * 批量查询用户对应时间的统计值
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param userCodes 用户编号列表
     * @return 统计结果值
     */
    List<FlightDemandPlanStatDO> selectUserCodesByStatDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("userCodes") List<String> userCodes);
}