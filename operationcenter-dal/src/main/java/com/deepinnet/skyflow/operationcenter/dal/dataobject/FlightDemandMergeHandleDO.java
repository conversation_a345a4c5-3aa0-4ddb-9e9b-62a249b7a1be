package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 合并需求处理记录表实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("flight_demand_merge_handle")
public class FlightDemandMergeHandleDO extends Model<FlightDemandMergeHandleDO> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 合并处理编号
     */
    @TableField("merge_handle_code")
    private String mergeHandleCode;

    /**
     * 合并状态
     */
    @TableField("merge_status")
    private String mergeStatus;

    /**
     * 处理时间
     */
    @TableField("handle_time")
    private LocalDateTime handleTime;

    /**
     * 处理人编号
     */
    @TableField("operator_id")
    private String operatorId;

    /**
     * 处理人名称
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 减少的飞行次数
     */
    @TableField("reduce_flight_count")
    private Integer reduceFlightCount;

    @TableField("merge_effect_json")
    private String mergeEffectJson;

    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    @TableField("tenant_id")
    private String tenantId;

    @TableLogic
    @TableField("is_deleted")
    private Boolean isDeleted;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
} 