package com.deepinnet.skyflow.operationcenter.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandPlanStatDO;

import java.time.LocalDate;
import java.util.List;

/**
 * 飞行需求计划统计仓储接口
 *
 * <AUTHOR>
 */
public interface FlightDemandPlanStatRepository extends IService<FlightDemandPlanStatDO> {

    /**
     * 根据统计日期和用户编号查询统计数据
     *
     * @param statDate 统计日期
     * @param userCode 用户编号
     * @return 统计数据
     */
    FlightDemandPlanStatDO selectByStatDateAndUserCode(LocalDate statDate, String userCode);

    /**
     * 根据统计日期范围查询统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param userCode 用户编号
     * @return 统计数据列表
     */
    List<FlightDemandPlanStatDO> selectByStatDateRange(LocalDate startDate, LocalDate endDate, String userCode);

    /**
     * 根据用户编号查询统计数据
     *
     * @param userCode 用户编号
     * @return 统计数据列表
     */
    List<FlightDemandPlanStatDO> selectByUserCode(String userCode);

    /**
     * 根据统计日期查询所有用户的统计数据
     *
     * @param statDate 统计日期
     * @return 统计数据列表
     */
    List<FlightDemandPlanStatDO> selectByStatDate(LocalDate statDate);

    /**
     * 保存或更新统计数据
     *
     * @param statDO 统计数据
     * @return 是否成功
     */
    boolean saveOrUpdateStat(FlightDemandPlanStatDO statDO);

    /**
     * 批量查询用户对应时间的统计值
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param userCodes 用户编号列表
     * @return 统计值
     */
    List<FlightDemandPlanStatDO> selectUserCodesByStatDateRange(LocalDate startDate, LocalDate endDate, List<String> userCodes);
} 