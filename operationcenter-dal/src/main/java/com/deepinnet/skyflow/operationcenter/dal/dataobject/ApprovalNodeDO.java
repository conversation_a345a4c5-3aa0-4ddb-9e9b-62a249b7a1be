package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 审批节点实体
 * 
 * <AUTHOR>
 * @date 2025/1/17
 */
@Data
@TableName("approval_node")
public class ApprovalNodeDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("approval_id")
    private String approvalId;

    @TableField("step_order")
    private Integer stepOrder;

    @TableField("approve_user_id")
    private String approveUserId;

    @TableField("approve_user_name")
    private String approveUserName;

    @TableField("approve_department_id")
    private String approveDepartmentId;

    @TableField("approve_department_name")
    private String approveDepartmentName;

    @TableField("approve_user_phone")
    private String approveUserPhone;

    @TableField("status")
    private String status;

    @TableField("remark")
    private String remark;

    @TableField("approval_time")
    private LocalDateTime approvalTime;


    /**
     * 是否自动通过
     */
    @TableField("auto_pass")
    private Boolean autoPass = false;

    /**
     * 是否自动拒绝
     */
    @TableField("auto_reject")
    private Boolean autoReject = false;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
} 