package com.deepinnet.skyflow.operationcenter.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 审批实例实体
 * 
 * <AUTHOR>
 * @date 2025/1/17
 */
@Data
@TableName("approval_instance")
public class ApprovalInstanceDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("approval_id")
    private String approvalId;

    @TableField("biz_type")
    private String bizType;

    @TableField("biz_id")
    private String bizId;

    @TableField("status")
    private String status;

    /**
     * 审批发起人ID
     */
    @TableField("submit_user_id")
    private String submitUserId;

    @TableField("submit_user_name")
    private String submitUserName;

    @TableField("submit_department_id")
    private String submitDepartmentId;

    @TableField("submit_department_name")
    private String submitDepartmentName;

    @TableField("submit_user_phone")
    private String submitUserPhone;

    @TableField("apply_time")
    private LocalDateTime applyTime;

    @TableField("apply_reason")
    private String applyReason;

    /**
     * 审批结束时间
     */
    @TableField("approve_end_time")
    private LocalDateTime approveEndTime;

    @TableField("current_step")
    private Integer currentStep;

    @TableField("callback_params")
    private String callbackParams;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 创建时间
     */
    @TableField("gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
} 