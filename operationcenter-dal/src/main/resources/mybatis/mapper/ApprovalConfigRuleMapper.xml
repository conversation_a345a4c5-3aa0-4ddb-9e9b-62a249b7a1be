<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.ApprovalConfigRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalConfigRuleDO">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="rule_name" property="ruleName" />
        <result column="rule_type" property="ruleType" />
        <result column="org_code" property="orgCode" />
        <result column="creator" property="creator" />
        <result column="editor" property="editor" />
        <result column="approval_choose_config_rule_list" property="approvalChooseConfigRuleList" />
        <result column="tenant_id" property="tenantId" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, rule_name, rule_type, org_code, creator, editor, approval_choose_config_rule_list, tenant_id, gmt_created, gmt_modified, is_deleted
    </sql>

</mapper> 