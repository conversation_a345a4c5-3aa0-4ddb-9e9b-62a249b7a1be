<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.ApprovalInstanceDOMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalInstanceDO">
        <id column="id" property="id" />
        <result column="approval_id" property="approvalId" />
        <result column="biz_type" property="bizType" />
        <result column="biz_id" property="bizId" />
        <result column="status" property="status" />
        <result column="apply_time" property="applyTime" />
        <result column="apply_reason" property="applyReason" />
        <result column="current_step" property="currentStep" />
        <result column="callback_params" property="callbackParams" />
        <result column="submit_user_id" property="submitUserId" />
        <result column="submit_user_name" property="submitUserName" />
        <result column="submit_department_id" property="submitDepartmentId" />
        <result column="submit_department_name" property="submitDepartmentName" />
        <result column="approve_end_time" property="approveEndTime" />
        <result column="submit_user_phone" property="submitUserPhone" />
        <result column="tenant_id" property="tenantId" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <select id="queryPendingApprovalCountByUserNo"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalNodeDO">
        SELECT
            an.*
        FROM
            approval_instance ai
                INNER JOIN approval_node an ON ai.approval_id = an.approval_id
        WHERE
            ai.status in ('PENDING', 'APPROVING')
          AND ai.biz_type = #{type}
          AND an.status in ('PENDING', 'APPROVING')
          AND an.step_order = (
            SELECT MIN(step_order)
            FROM approval_node an2
            WHERE an2.approval_id = ai.approval_id
              AND an2.status IN ('PENDING', 'APPROVING')
          )
          AND an.approve_user_id = #{userNo}
    </select>

</mapper>
