<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.MergeRuleConfigDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.skyflow.operationcenter.dal.dataobject.MergeRuleConfigDO">
        <id column="id" property="id" />
        <result column="rule_type" property="ruleType" />
        <result column="rule_value" property="ruleValue" />
        <result column="is_active" property="isActive" />
        <result column="tenant_id" property="tenantId" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rule_type, rule_value, is_active, tenant_id, is_deleted, gmt_created, gmt_modified
    </sql>

</mapper>
