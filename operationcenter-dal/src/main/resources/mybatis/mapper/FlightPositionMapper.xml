<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.FlightPositionDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPositionDO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="position_no" property="positionNo" />
        <result column="type" property="type" />
        <result column="name" property="name" />
        <result column="center_point" property="centerPoint" />
        <result column="flight_position" property="flightPosition" />
        <result column="is_deleted" property="isDeleted" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, position_no, type, name, center_point, flight_position, is_deleted, gmt_created, gmt_modified, tenant_id
    </sql>

</mapper>
