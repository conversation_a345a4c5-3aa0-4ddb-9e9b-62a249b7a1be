<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDemandPlanStatMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandPlanStatDO">
        <id column="id" property="id"/>
        <result column="stat_date" property="statDate"/>
        <result column="user_code" property="userCode"/>
        <result column="approved_order_num" property="approvedOrderNum"/>
        <result column="valid_demand_num" property="validDemandNum"/>
        <result column="total_plan_num" property="totalPlanNum"/>
        <result column="ready_plan_num" property="readyPlanNum"/>
        <result column="complete_plan_num" property="completePlanNum"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, stat_date, user_code, approved_order_num, valid_demand_num, total_plan_num, ready_plan_num, complete_plan_num,
        tenant_id, gmt_created, gmt_modified, is_deleted
    </sql>

    <!-- 根据统计日期和用户编号查询统计数据 -->
    <select id="selectByStatDateAndUserCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flight_demand_plan_stat
        WHERE stat_date = #{statDate}
        AND user_code = #{userCode}
        AND is_deleted = false
    </select>

    <!-- 根据统计日期范围查询统计数据 -->
    <select id="selectByStatDateRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flight_demand_plan_stat
        WHERE stat_date BETWEEN #{startDate} AND #{endDate}
        <if test="userCode != null and userCode != ''">
            AND user_code = #{userCode}
        </if>
        AND is_deleted = false
        ORDER BY stat_date ASC, user_code ASC
    </select>

    <!-- 根据用户编号查询统计数据 -->
    <select id="selectByUserCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flight_demand_plan_stat
        WHERE user_code = #{userCode}
        AND is_deleted = false
        ORDER BY stat_date DESC
    </select>

    <!-- 根据统计日期查询所有用户的统计数据 -->
    <select id="selectByStatDate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flight_demand_plan_stat
        WHERE stat_date = #{statDate}
        AND is_deleted = false
        ORDER BY user_code ASC
    </select>

    <select id="selectUserCodesByStatDateRange"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandPlanStatDO">
        SELECT
            stat_date AS statDate,
            SUM(valid_demand_num)::BIGINT AS validDemandNum,
            SUM(total_plan_num)::BIGINT AS totalPlanNum,
            SUM(ready_plan_num)::BIGINT AS readyPlanNum,
            SUM(complete_plan_num)::BIGINT AS completePlanNum,
            SUM(approved_order_num)::BIGINT AS approvedOrderNum
        FROM
            flight_demand_plan_stat
        WHERE
            stat_date BETWEEN #{startDate} AND #{endDate}
        <if test="userCodes != null and userCodes.size > 0">
            AND user_code in
            <foreach collection="userCodes" item="userCode" open="(" separator="," close=")">
                #{userCode}
            </foreach>
        </if>
        GROUP BY
            stat_date
        ORDER BY
            stat_date ASC
    </select>

</mapper> 