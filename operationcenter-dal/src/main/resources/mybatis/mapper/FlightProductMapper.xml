<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.FlightProductMapper">

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, product_no, product_name, product_description, product_detail, product_content,
        product_type, product_service_type, category_no, category_name, service_provider_no,
        product_status, product_shelf_status, product_min_price, product_pictures, flight_uav_bm_list, category_no_list, support_uav_pilot,
        tenant_id, gmt_created, gmt_modified, is_deleted
    </sql>

    <!-- 分页查询飞行产品（带关联查询） -->
    <select id="pageQueryFlightProductWithJoin" resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightProductDO">
        SELECT DISTINCT fp.*
        FROM flight_product fp
        <!-- 只有当产品类型为FLIGHT_UAV时才做关联查询 -->
        <if test="queryDTO.flightUavFlyType != null or queryDTO.flightUavBmName != null and queryDTO.flightUavBmName != '' or queryDTO.flightUavBmModelNo != null and queryDTO.flightUavBmModelNo != ''">
            LEFT JOIN flight_uav_bm fub ON 
            fp.product_type = 'FLIGHT_UAV' AND 
            <!-- 使用flightUavBmList字段包含flight_uav_bm_no做连接条件 -->
            fub.flight_uav_bm_no = ANY(fp.flight_uav_bm_list)
        </if>
        <where>
            fp.is_deleted = false
            <if test="queryDTO.tenantId != null and queryDTO.tenantId != ''">
                AND fp.tenant_id = #{queryDTO.tenantId}
            </if>
            <if test="queryDTO.productNo != null and queryDTO.productNo != ''">
                AND fp.product_no = #{queryDTO.productNo}
            </if>
            <if test="queryDTO.productNoList != null and queryDTO.productNoList.size() > 0">
                AND fp.product_no IN
                <foreach collection="queryDTO.productNoList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="queryDTO.productName != null and queryDTO.productName != ''">
                AND fp.product_name LIKE CONCAT('%', #{queryDTO.productName}, '%')
            </if>
            <if test="queryDTO.productType != null">
                AND fp.product_type = #{queryDTO.productType}
            </if>
            <if test="queryDTO.productServiceType != null">
                AND fp.product_service_type = #{queryDTO.productServiceType}
            </if>
            <if test="queryDTO.categoryNo != null and queryDTO.categoryNo != ''">
                AND fp.category_no = #{queryDTO.categoryNo}
            </if>
            <if test="queryDTO.categoryNoList != null and queryDTO.categoryNoList.size() > 0">
                AND (
                <foreach collection="queryDTO.categoryNoList" item="categoryNo" separator=" OR ">
                    fp.category_no_list @> ARRAY[#{categoryNo}]::text[]
                </foreach>
                )
            </if>
            <if test="queryDTO.serviceProviderNo != null and queryDTO.serviceProviderNo != ''">
                AND fp.service_provider_no = #{queryDTO.serviceProviderNo}
            </if>
            <if test="queryDTO.productStatus != null and queryDTO.productStatus != ''">
                AND fp.product_status = #{queryDTO.productStatus}
            </if>
            <if test="queryDTO.productShelfStatus != null">
                AND fp.product_shelf_status = #{queryDTO.productShelfStatus}
            </if>
            <if test="queryDTO.supportUavPilot != null">
                AND fp.support_uav_pilot = #{queryDTO.supportUavPilot}
            </if>
            <!-- 这些条件只有在做了关联查询时才会生效 -->
            <if test="queryDTO.flightUavFlyType != null">
                AND fp.product_type = 'FLIGHT_UAV'
                AND fub.flight_uav_fly_type = #{queryDTO.flightUavFlyType}
            </if>
            <if test="queryDTO.flightUavBmName != null and queryDTO.flightUavBmName != ''">
                AND fp.product_type = 'FLIGHT_UAV'
                AND fub.flight_uav_bm_name LIKE CONCAT('%', #{queryDTO.flightUavBmName}, '%')
            </if>
            <if test="queryDTO.flightUavBmModelNo != null and queryDTO.flightUavBmModelNo != ''">
                AND fp.product_type = 'FLIGHT_UAV'
                AND fub.flight_uav_bm_model_no LIKE CONCAT('%', #{queryDTO.flightUavBmModelNo}, '%')
            </if>
        </where>
        ORDER BY fp.gmt_created DESC
    </select>
</mapper> 