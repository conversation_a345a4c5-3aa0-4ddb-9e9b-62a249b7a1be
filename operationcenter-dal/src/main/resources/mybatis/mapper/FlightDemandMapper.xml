<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.FlightDemandMapper">


    <select id="select90DayStats"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandStatsDO">
        with bizEntities as (
        select
        fd.demand_no,
        fd.publish_time,
        fd.center_point_latitude,
        fd.center_point_longitude,
        fd.area,
        fd.type,
                fmdr.target_demand_code
            from flight_demand fd
            join flight_merge_demand_relation fmdr on fd.demand_no = fmdr.original_demand_code and fmdr.is_deleted = false
        where
        fd.publish_time > (current_date - interval '90 days')
        and fd.publish_time &lt;= now()
        and fd.type in ('EMERGENCY_RESPONSE', 'ROUTINE_INSPECTION')
            and fd.is_deleted = false
        <choose>
            <when test="condition.account != null and condition.account != ''">
                and (fd.biz_data::json->>'account') = #{condition.account}
            </when>
            <otherwise>
                and fd.publisher_no = #{condition.userNo}
            </otherwise>
        </choose>
        ),
        plans as (
        select * from flight_plan where biz_no in (select target_demand_code from bizEntities)
        ),
        plans_count AS (
        SELECT COUNT(*) AS total_plans_count FROM plans
        ),
        aggregated_stats_plan_flight as (
        SELECT
        SUM(COALESCE(flight_miles::double precision, 0)) AS total_flight_miles,
        SUM(COALESCE(flight_duration::double precision, 0)) AS total_flight_duration
        FROM (
        SELECT
        flight_miles,
        flight_duration,
        ROW_NUMBER() OVER (PARTITION BY plan_id ORDER BY report_time DESC) AS rn
        FROM real_time_uav_flight
        WHERE plan_id IN (SELECT plan_id FROM plans)
        ) t
        WHERE rn = 1
        ),
        routine_inspection_demand_area as (
        select sum(COALESCE(d.area, 0)) as aerial_patrol_area
        from bizEntities d
        join plans p on d.target_demand_code = p.biz_no
        where d.type = 'ROUTINE_INSPECTION'
        )
        select
        plans_count.total_plans_count as plan_num,
        aggregated_stats_plan_flight.total_flight_miles as flight_miles,
        aggregated_stats_plan_flight.total_flight_duration as flight_duration,
        routine_inspection_demand_area.aerial_patrol_area as aerial_patrol_area
        from
        plans_count,
        aggregated_stats_plan_flight,
        routine_inspection_demand_area
    </select>

    <select id="selectDayStats" resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandStatsDO">
        with bizEntities as (
            select
                fd.demand_no,
                fd.publish_time,
                fd.center_point_latitude,
                fd.center_point_longitude,
                fd.area,
                fd.type,
                fd.approve_status,
                fd.match_status,
                fd.group_code,
                fd.is_merge_demand,
                fmdr.target_demand_code
            from flight_demand fd
            join flight_merge_demand_relation fmdr on fd.demand_no = fmdr.original_demand_code and fmdr.is_deleted = false
            where fd.publish_time > #{condition.startTime}
            and fd.is_deleted = false
            and fd.scene = 'ONE_NET_UNIFIED_FLY'
            <if test="condition.userNoList != null and condition.userNoList.size() > 0">
                and fd.publisher_no in
                <foreach collection="condition.userNoList" item="userNo" open="(" close=")" separator=",">
                    #{userNo}
                </foreach>
            </if>
            and fd.tenant_id = #{condition.tenantId}
        ),
        plans as (
            select fp.* from flight_plan fp
            join bizEntities b on fp.biz_no = b.target_demand_code
            where b.match_status in ('MATCHED','PARTIAL_MATCHED')
        ),
        plans_count AS (
            SELECT COUNT(distinct plan_id) AS total_plans_count FROM plans
        ),
        plans_finished_count AS (
            SELECT COUNT(distinct plan_id) AS total_plans_count FROM plans where status = 6
        ),
        demands_count as (
            SELECT COUNT(distinct group_code ) AS total_demands_count
            FROM bizEntities
            where approve_status = 'APPROVED' and is_merge_demand = FALSE
        ),
        aggregated_stats_plan_flight as (
            SELECT
            SUM(COALESCE(flight_miles::double precision, 0)) AS total_flight_miles,
            SUM(COALESCE(flight_duration::double precision, 0)) AS total_flight_duration
            FROM (
                SELECT DISTINCT ON (r.plan_id)
                    r.plan_id,
                    r.flight_miles,
                    r.flight_duration
                FROM real_time_uav_flight r
                JOIN plans p ON r.plan_id = p.plan_id
                WHERE r.flight_id IS NOT NULL
                ORDER BY r.plan_id, r.report_time DESC
            ) t
        ),
        routine_inspection_demand_area as (
            select sum(COALESCE(d.area, 0)) as aerial_patrol_area
            from bizEntities d
            join plans p on d.target_demand_code = p.biz_no
            where d.type = 'ROUTINE_INSPECTION'
        )
        select
            plans_count.total_plans_count as plan_num,
            aggregated_stats_plan_flight.total_flight_miles as flight_miles,
            aggregated_stats_plan_flight.total_flight_duration as flight_duration,
            routine_inspection_demand_area.aerial_patrol_area as aerial_patrol_area,
            demands_count.total_demands_count as demand_num,
            plans_finished_count.total_plans_count as plan_finished_num
        from
            plans_count,
            aggregated_stats_plan_flight,
            routine_inspection_demand_area,
            demands_count,
            plans_finished_count
    </select>

    <select id="select90DayEmergencyResponseTimeStats" resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandStatsDO">
        with emergency_response_demands as (
        select
        fd.demand_no,
        fd.publish_time,
        ST_Buffer (
        ST_SetSRID (
        ST_MakePoint (
        CAST(fd.center_point_longitude AS FLOAT),
        CAST(fd.center_point_latitude AS FLOAT)
        ), 4326
        )::geography,
        500
        )::geometry AS buffer_geom,
                fmdr.target_demand_code
            from flight_demand fd
            join flight_merge_demand_relation fmdr on fd.demand_no = fmdr.original_demand_code and fmdr.is_deleted = false
            where
            fd.publish_time > current_date - interval '90 days'
        and fd.publish_time &lt;= now()
        and fd.is_deleted = false
        and fd.type = 'EMERGENCY_RESPONSE'
        <choose>
            <when test="condition.account != null and condition.account != ''">
                and (fd.biz_data::json->>'account') = #{condition.account}
            </when>
            <otherwise>
                and fd.publisher_no = #{condition.userNo}
            </otherwise>
        </choose>
        ),
        demand_plans AS (
        SELECT
        fp.*,
        erd.*
        FROM flight_plan fp
        JOIN emergency_response_demands erd ON erd.target_demand_code = fp.biz_no
        ),
        real_time_data AS (
        SELECT
        rt.ID AS real_time_uav_flight_id,
        rt.uav_position,
        rt.report_time,
        rt.plan_id,
        ROW_NUMBER() OVER (PARTITION BY rt.plan_id ORDER BY rt.report_time) AS rn
        FROM real_time_uav_flight rt
        JOIN demand_plans dp ON rt.plan_id = dp.plan_id
        WHERE ST_Within (
        ST_SetSRID(
        ST_Point(
        CAST(SPLIT_PART(rt.uav_position, ',', 1) AS FLOAT),
        CAST(SPLIT_PART(rt.uav_position, ',', 2) AS FLOAT)
        )::geometry,
        4326
        ),
        dp.buffer_geom
        )
        ),
        detail_result AS (
        SELECT
        ss.demand_no,
        EXTRACT(EPOCH FROM (rt.report_time - ss.publish_time)) / 60 AS time_diff_minutes
        FROM demand_plans ss
        JOIN real_time_data rt ON ss.plan_id = rt.plan_id
        WHERE rt.rn = 1
        )
        SELECT
        AVG(time_diff_minutes) AS average_response_time
        FROM detail_result;
    </select>

    <select id="selectEmergencyResponseTimeStats" resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandStatsDO">
        with emergency_response_demands as (
        select
        fd.demand_no,
        fd.publish_time,
        ST_Buffer (
        ST_SetSRID (
        ST_MakePoint (
        CAST(fd.center_point_longitude AS FLOAT),
        CAST(fd.center_point_latitude AS FLOAT)
        ), 4326
        )::geography,
        500
        )::geometry AS buffer_geom,
        fmdr.target_demand_code
        from flight_demand fd
        join flight_merge_demand_relation fmdr on fd.demand_no = fmdr.original_demand_code and fmdr.is_deleted = false
        where fd.publish_time > #{condition.startTime}
        and fd.is_deleted = false
        and fd.type = 'EMERGENCY_RESPONSE'
        and fd.scene = #{condition.demandScene}
        <if test="condition.userNoList != null and condition.userNoList.size() > 0">
            and fd.publisher_no in
            <foreach collection="condition.userNoList" item="userNo" open="(" close=")" separator=",">
                #{userNo}
            </foreach>
        </if>
        ),
        demand_plans AS (
        SELECT
        fp.*,
        erd.*
        FROM flight_plan fp
        JOIN emergency_response_demands erd ON erd.target_demand_code = fp.biz_no
        ),
        real_time_data AS (
        SELECT
        rt.ID AS real_time_uav_flight_id,
        rt.uav_position,
        rt.report_time,
        rt.plan_id,
        ROW_NUMBER() OVER (PARTITION BY rt.plan_id ORDER BY rt.report_time) AS rn
        FROM real_time_uav_flight rt
        JOIN demand_plans dp ON rt.plan_id = dp.plan_id
        WHERE ST_Within (
        ST_SetSRID(
        ST_Point(
        CAST(SPLIT_PART(rt.uav_position, ',', 1) AS FLOAT),
        CAST(SPLIT_PART(rt.uav_position, ',', 2) AS FLOAT)
        )::geometry,
        4326
        ),
        dp.buffer_geom
        )
        ),
        detail_result AS (
        SELECT
        ss.demand_no,
        EXTRACT(EPOCH FROM (rt.report_time - ss.publish_time)) / 60 AS time_diff_minutes
        FROM demand_plans ss
        JOIN real_time_data rt ON ss.plan_id = rt.plan_id
        WHERE rt.rn = 1
        )
        SELECT
        AVG(time_diff_minutes) AS average_response_time
        FROM detail_result;
    </select>

    <select id="select7DayStats"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDayStatsDO">
        WITH date_series AS (
            SELECT generate_series(
            CURRENT_DATE - INTERVAL '6 days',
            CURRENT_DATE,
            INTERVAL '1 day'
            )::date AS date
        ),
        bizEntities AS (
            SELECT fd.demand_no,
                    fmdr.target_demand_code
                from flight_demand fd
            join flight_merge_demand_relation fmdr on fd.demand_no = fmdr.original_demand_code and fmdr.is_deleted = false
            WHERE fd.publish_time > #{condition.startTime}
            AND fd.type IN ('EMERGENCY_RESPONSE', 'ROUTINE_INSPECTION')
            and fd.is_deleted = false
            and fd.scene = #{condition.demandScene}
            <if test="condition.demandScene != null and condition.demandScene == 'ONE_NET_UNIFIED_FLY'">
                <!-- 一网统飞的数据需要过滤状态 -->
                and fd.match_status in ('MATCHED','PARTIAL_MATCHED')
            </if>
            <if test="condition.userNoList != null and condition.userNoList.size() > 0">
                and fd.publisher_no in
                <foreach collection="condition.userNoList" item="userNo" open="(" close=")" separator=",">
                    #{userNo}
                </foreach>
            </if>
            <if test="condition.account != null and condition.account != ''">
                and (fd.biz_data::json->>'account') = #{condition.account}
            </if>
            and fd.tenant_id = #{condition.tenantId}
        ),
        plans AS (
            SELECT planed_takeoff_time
            FROM flight_plan
            WHERE biz_no IN (SELECT target_demand_code FROM bizEntities)
            and TO_TIMESTAMP (planed_takeoff_time / 1000) >= (current_date - interval '6 days')
        ),
        plan_stats AS (
            SELECT
                date_trunc('day', to_timestamp(planed_takeoff_time / 1000))::date AS date,
                COUNT(*) AS plan_num
            FROM plans
            GROUP BY date
        )
        SELECT
            ds.date,
            COALESCE(ps.plan_num, 0) AS plan_num
        FROM date_series ds
        LEFT JOIN plan_stats ps ON ds.date = ps.date
        ORDER BY ds.date;
    </select>

    <!-- 分页查询待匹配的需求（距离起飞时间小于72小时） -->
    <select id="selectPendingMatchDemands"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO">
        SELECT fd.*
        FROM flight_demand fd
                 INNER JOIN flight_demand_routine_inspection fdri ON fd.demand_no = fdri.demand_no
        WHERE fd.type = 'ROUTINE_INSPECTION'
          AND fd.match_status != 'MATCHED'
          AND (
              (fd.is_merge_demand = true AND fd.merge_status = 'MERGED')
              OR
              (fd.is_merge_demand = false AND fd.merge_status != 'MERGED')
          )
          AND fdri.demand_start_time IS NOT NULL
          AND fdri.demand_start_time &gt;= CURRENT_DATE
          AND fdri.demand_start_time &lt;= (CURRENT_DATE + INTERVAL '3 days')
          AND fd.id > #{lastId}
          AND fd.is_latest = true
          AND fd.is_supplement = false
          AND fd.approve_status in ('APPROVED', 'NOT_NEED_APPROVAL')
        ORDER BY fd.id
            LIMIT #{limit}
    </select>

    <!-- 分页查询待合并的需求（距离起飞时间大于72小时） -->
    <select id="selectPendingMergeDemands"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO">
        SELECT fd.*
        FROM flight_demand fd
                 INNER JOIN flight_demand_routine_inspection fdri ON fd.demand_no = fdri.demand_no
        WHERE fd.is_merge_demand = false
          AND fd.match_status IN ('UNMATCHED', 'MATCH_FAILED')
          AND fd.merge_status != 'MERGED'
          AND fdri.demand_start_time IS NOT NULL
          AND fdri.demand_start_time &gt; CURRENT_DATE + INTERVAL '3 days'
          AND fd.id > #{lastId}
          AND fd.is_latest = true
          AND fd.is_supplement = false
          AND fd.approve_status in ('APPROVED', 'NOT_NEED_APPROVAL')
        ORDER BY fd.id
            LIMIT #{limit}
    </select>

    <!-- 客户端分页查询飞行需求（只查询原始需求） -->
    <select id="pageQueryFlightDemandCustomerManage"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO">
        SELECT DISTINCT fd.*
        FROM flight_demand fd
        <if test="(queryDTO.serviceProviderNo != null and queryDTO.serviceProviderNo != '' )
        or (queryDTO.serviceProviderName != null and queryDTO.serviceProviderName != '' )
        or (queryDTO.companyName != null and queryDTO.companyName != '')">
            INNER JOIN flight_demand_match_service_provider fdmsp ON fd.demand_no = fdmsp.demand_code
        </if>
        <if test="queryDTO.waitMeToApprove != null and queryDTO.waitMeToApprove == true">
            join approval_instance ai on fd.demand_no = ai.biz_id
            join approval_node an on ai.approval_id = an.approval_id and ai.current_step = an.step_order
        </if>
        WHERE fd.is_merge_demand = false
            AND fd.is_latest = true
        <if test="queryDTO.waitMeToApprove != null and queryDTO.waitMeToApprove == true">
            and an.approve_user_id = #{queryDTO.curUserCode}
            and an.status = 'PENDING'
            and ai.status not in ('APPROVED', 'REJECTED', 'CANCELED')
        </if>
        <!-- 基本字段查询条件 -->
        <if test="queryDTO.organizationId != null and queryDTO.organizationId != ''">
            AND fd.organization_id = #{queryDTO.organizationId}
        </if>
        <if test="queryDTO.organizationName != null and queryDTO.organizationName != ''">
            AND fd.organization_name LIKE CONCAT('%', #{queryDTO.organizationName}, '%')
        </if>
        <if test="queryDTO.demandNo != null and queryDTO.demandNo != ''">
            AND fd.demand_no = #{queryDTO.demandNo}
        </if>
        <if test="queryDTO.demandNoList != null and queryDTO.demandNoList.size() > 0">
            AND fd.demand_no IN
            <foreach collection="queryDTO.demandNoList" item="demandNo" open="(" close=")" separator=",">
                #{demandNo}
            </foreach>
        </if>
        <if test="queryDTO.name != null and queryDTO.name != ''">
            AND fd.name LIKE CONCAT('%', #{queryDTO.name}, '%')
        </if>
        <if test="queryDTO.type != null">
            AND fd.type = #{queryDTO.type}
        </if>
        <if test="queryDTO.flightUavBm != null and queryDTO.flightUavBm != ''">
            AND fd.flight_uav_bm = #{queryDTO.flightUavBm}
        </if>
        <if test="queryDTO.categoryNo != null and queryDTO.categoryNo != ''">
            AND fd.category_no = #{queryDTO.categoryNo}
        </if>
        <if test="queryDTO.matchStatus != null">
            AND fd.match_status = #{queryDTO.matchStatus}
        </if>
        <if test="queryDTO.tenantId != null and queryDTO.tenantId != ''">
            AND fd.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.productName != null and queryDTO.productName != ''">
            AND fd.product_name LIKE CONCAT('%', #{queryDTO.productName}, '%')
        </if>
        <!-- 发布者查询条件 -->
        <if test="queryDTO.publisherNoList != null and queryDTO.publisherNoList.size() > 0">
            AND fd.publisher_no IN
            <foreach collection="queryDTO.publisherNoList" item="publisherNo" open="(" close=")" separator=",">
                #{publisherNo}
            </foreach>
        </if>
        <if test="queryDTO.publisherName != null and queryDTO.publisherName != ''">
            AND fd.publisher_name LIKE CONCAT('%', #{queryDTO.publisherName}, '%')
        </if>
        <!-- 飞行订单号查询条件 -->
        <if test="queryDTO.flightOrderNo != null and queryDTO.flightOrderNo != ''">
            AND fd.flight_order_no = #{queryDTO.flightOrderNo}
        </if>
        <if test="queryDTO.approveStatusList != null and queryDTO.approveStatusList.size() > 0">
            AND fd.approve_status in
                <foreach collection="queryDTO.approveStatusList" item="approveStatus" open="(" close=")" separator=",">
                    #{approveStatus}
                </foreach>
        </if>
        <!-- 服务商查询条件 -->
        <if test="queryDTO.serviceProviderNo != null and queryDTO.serviceProviderNo != ''">
            AND fdmsp.service_provider_no = #{queryDTO.serviceProviderNo}
        </if>
        <if test="queryDTO.serviceProviderName != null and queryDTO.serviceProviderName != ''">
            AND fdmsp.service_provider_name LIKE CONCAT('%', #{queryDTO.serviceProviderName}, '%')
        </if>
        <if test="queryDTO.companyName != null and queryDTO.companyName != ''">
            AND fdmsp.company_name LIKE CONCAT('%', #{queryDTO.companyName}, '%')
        </if>
        ORDER BY fd.publish_time DESC, fd.id DESC
    </select>

    <!-- 运营端分页查询飞行需求（查询合并需求和未参与合并的原始需求） -->
    <select id="pageQueryFlightDemandOpManage"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO">
        SELECT DISTINCT fd.*
        FROM flight_demand fd
        <if test="(queryDTO.serviceProviderNo != null and queryDTO.serviceProviderNo != '') or (queryDTO.serviceProviderName != null and queryDTO.serviceProviderName != '') or (queryDTO.companyName != null and queryDTO.companyName != '')">
            INNER JOIN flight_demand_match_service_provider fdmsp ON fd.demand_no = fdmsp.demand_code
        </if>
        <if test="queryDTO.waitMeToApprove != null and queryDTO.waitMeToApprove == true">
            join approval_instance ai on fd.demand_no = ai.biz_id
            join approval_node an on ai.approval_id = an.approval_id and ai.current_step = an.step_order
        </if>
        WHERE fd.is_latest = true
        AND (
        (fd.is_merge_demand = true AND fd.merge_status = 'MERGED')
        OR
        (fd.is_merge_demand = false AND fd.merge_status != 'MERGED')
        )
        <if test="queryDTO.waitMeToApprove != null and queryDTO.waitMeToApprove == true">
            and an.approve_user_id = #{queryDTO.curUserCode}
            and an.status = 'PENDING'
            and ai.status not in ('APPROVED', 'REJECTED', 'CANCELED')
        </if>
        <!-- 基本字段查询条件 -->
        <if test="queryDTO.organizationId != null and queryDTO.organizationId != ''">
            AND fd.organization_id = #{queryDTO.organizationId}
        </if>
        <if test="queryDTO.organizationName != null and queryDTO.organizationName != ''">
            AND fd.organization_name LIKE CONCAT('%', #{queryDTO.organizationName}, '%')
        </if>
        <if test="queryDTO.demandNo != null and queryDTO.demandNo != ''">
            AND fd.demand_no = #{queryDTO.demandNo}
        </if>
        <if test="queryDTO.demandNoList != null and queryDTO.demandNoList.size() > 0">
            AND fd.demand_no IN
            <foreach collection="queryDTO.demandNoList" item="demandNo" open="(" close=")" separator=",">
                #{demandNo}
            </foreach>
        </if>
        <if test="queryDTO.name != null and queryDTO.name != ''">
            AND fd.name LIKE CONCAT('%', #{queryDTO.name}, '%')
        </if>
        <if test="queryDTO.type != null">
            AND fd.type = #{queryDTO.type}
        </if>
        <if test="queryDTO.flightUavBm != null and queryDTO.flightUavBm != ''">
            AND fd.flight_uav_bm = #{queryDTO.flightUavBm}
        </if>
        <if test="queryDTO.categoryNo != null and queryDTO.categoryNo != ''">
            AND fd.category_no = #{queryDTO.categoryNo}
        </if>
        <if test="queryDTO.matchStatus != null">
            AND fd.match_status = #{queryDTO.matchStatus}
        </if>
        <if test="queryDTO.tenantId != null and queryDTO.tenantId != ''">
            AND fd.tenant_id = #{queryDTO.tenantId}
        </if>
        <if test="queryDTO.productName != null and queryDTO.productName != ''">
            AND fd.product_name LIKE CONCAT('%', #{queryDTO.productName}, '%')
        </if>
        <!-- 发布者查询条件 - 直接使用表字段 -->
        <if test="queryDTO.publisherNo != null and queryDTO.publisherNo != ''">
            AND fd.publisher_no = #{queryDTO.publisherNo}
        </if>
        <if test="queryDTO.publisherName != null and queryDTO.publisherName != ''">
            AND fd.publisher_name LIKE CONCAT('%', #{queryDTO.publisherName}, '%')
        </if>
        <!-- 飞行订单号查询条件 - 直接使用表字段 -->
        <if test="queryDTO.flightOrderNo != null and queryDTO.flightOrderNo != ''">
            AND fd.flight_order_no = #{queryDTO.flightOrderNo}
        </if>
        <if test="queryDTO.approveStatusList != null and queryDTO.approveStatusList.size() > 0">
            AND fd.approve_status in
            <foreach collection="queryDTO.approveStatusList" item="approveStatus" open="(" close=")" separator=",">
                #{approveStatus}
            </foreach>
        </if>
        <!-- 服务商查询条件 -->
        <if test="queryDTO.serviceProviderNo != null and queryDTO.serviceProviderNo != ''">
            AND fdmsp.service_provider_no = #{queryDTO.serviceProviderNo}
        </if>
        <if test="queryDTO.serviceProviderName != null and queryDTO.serviceProviderName != ''">
            AND fdmsp.service_provider_name LIKE CONCAT('%', #{queryDTO.serviceProviderName}, '%')
        </if>
        <if test="queryDTO.companyName != null and queryDTO.companyName != ''">
            AND fdmsp.company_name LIKE CONCAT('%', #{queryDTO.companyName}, '%')
        </if>
        ORDER BY fd.publish_time DESC, fd.id DESC
    </select>

</mapper>
