<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.FlightOrderDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderDO">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="status" property="status" />
        <result column="product_name" property="productName" />
        <result column="main_product_type" property="mainProductType" />
        <result column="user_no" property="userNo" />
        <result column="customer_id" property="customerId" />
        <result column="organization_id" property="organizationId" />
        <result column="organization_name" property="organizationName" />
        <result column="order_amount" property="orderAmount" />
        <result column="pay_type" property="payType" />
        <result column="pay_source" property="paySource" />
        <result column="approve_status" property="approveStatus" />
        <result column="pay_status" property="payStatus" />
        <result column="refund_status" property="refundStatus" />
        <result column="pay_amount" property="payAmount" />
        <result column="refund_amount" property="refundAmount" />
        <result column="order_time" property="orderTime" />
        <result column="approved_time" property="approvedTime" />
        <result column="category_no" property="categoryNo" />
        <result column="category_name" property="categoryName" />
        <result column="scene" property="scene" />
        <result column="order_type" property="orderType" />
        <result column="cycle_no" property="cycleNo" />
        <result column="cycle_name" property="cycleName" />
        <result column="tenant_id" property="tenantId" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, status, product_name, main_product_type, user_no, customer_id, organization_id, organization_name, order_amount, pay_type, pay_source, approve_status, pay_status, refund_status, pay_amount, refund_amount, order_time, approved_time, category_no, category_name, scene, order_type, cycle_no, cycle_name, tenant_id, gmt_created, gmt_modified
    </sql>

</mapper>
