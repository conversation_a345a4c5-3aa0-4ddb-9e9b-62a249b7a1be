<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepinnet.skyflow.operationcenter.dal.mapper.FlightPlanDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO">
        <id column="id" property="id" />
        <result column="mission_id" property="missionId" />
        <result column="plan_id" property="planId" />
        <result column="uav_id" property="uavId" />
        <result column="status" property="status" />
        <result column="planed_takeoff_time" property="planedTakeoffTime" />
        <result column="planed_landing_time" property="planedLandingTime" />
        <result column="operator_user" property="operatorUser" />
        <result column="operator_phone" property="operatorPhone" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="landing_aerodrome_id" property="landingAerodromeId" />
        <result column="arrival_aerodrome_id" property="arrivalAerodromeId" />
        <result column="alternate_aerodrome_id" property="alternateAerodromeId" />
        <result column="planed_altitude" property="planedAltitude" />
        <result column="real_ame_verification" property="realAmeVerification" />
        <result column="flight_unit" property="flightUnit" />
        <result column="flight_unit_id" property="flightUnitId" />
        <result column="apply_user_name" property="applyUserName" />
        <result column="airspace_id" property="airspaceId" />
        <result column="scence_code" property="scenceCode" />
        <result column="biz_no" property="bizNo" />
        <result column="tenant_id" property="tenantId" />
        <result column="related_id" property="relatedId" />
        <result column="apply_type" property="applyType" />
        <result column="out_plan_no" property="outPlanNo" />
        <result column="plan_name" property="planName" />
        <result column="requirement_name" property="requirementName" />
        <result column="uav_model" property="uavModel" />
        <result column="flight_report" property="flightReport" />
        <result column="monitor_url" property="monitorUrl" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, mission_id, plan_id, uav_id, status, planed_takeoff_time, planed_landing_time, operator_user, operator_phone, gmt_created, gmt_modified, landing_aerodrome_id, arrival_aerodrome_id, alternate_aerodrome_id, planed_altitude, real_ame_verification, flight_unit, flight_unit_id, apply_user_name, airspace_id, scence_code, biz_no, tenant_id, related_id, apply_type, out_plan_no, plan_name, requirement_name, uav_model, flight_report, monitor_url
    </sql>

    <select id="getFlightPlanListByPage"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO">
        SELECT
            fp.id,
            fp.mission_id,
            fp.plan_id,
            fp.uav_id,
            fp.status,
            fp.planed_takeoff_time,
            fp.planed_landing_time,
            fp.operator_user,
            fp.operator_phone,
            fp.gmt_created,
            fp.gmt_modified,
            fp.landing_aerodrome_id,
            fp.arrival_aerodrome_id,
            fp.alternate_aerodrome_id,
            fp.planed_altitude,
            fp.real_ame_verification,
            fp.flight_unit,
            fp.flight_unit_id,
            fp.apply_user_name,
            fp.airspace_id,
            fp.scence_code,
            fp.tenant_id,
            fp.related_id,
            fp.apply_type,
            fp.out_plan_no,
            fp.plan_name,
            <if test="planQuery.userType != 'customer'">
                fp.requirement_name,
                fp.biz_no,
            </if>
            <if test="planQuery.userType == 'customer'">
                fd.original_demand_name as requirement_name,
                fd.original_demand_code as biz_no,
            </if>
            fp.uav_model,
            fp.flight_report,
            fp.monitor_url
        FROM
            flight_plan fp
        <choose>
            <when test="(planQuery.userNo != null and planQuery.userNo.size() > 0)
                  or (planQuery.orderNo != null and planQuery.orderNo != '')
                  or (planQuery.startTime != null and planQuery.endTime != null)
                  or (planQuery.requirementType != null and planQuery.requirementType != '')
                  or (planQuery.userType == 'customer')">
                LEFT JOIN flight_merge_demand_relation fd ON fp.biz_no = fd.target_demand_code
            </when>
            <when test="(planQuery.userNo != null and planQuery.userNo.size() > 0)
                  or (planQuery.orderNo != null and planQuery.orderNo != '')
                  or (planQuery.startTime != null and planQuery.endTime != null)
                  or (planQuery.requirementType != null and planQuery.requirementType != '')
                  or (planQuery.account != null and planQuery.account != '')">
                LEFT JOIN flight_merge_demand_relation fd ON fp.biz_no = fd.target_demand_code
                LEFT join flight_demand as fdd on fdd.demand_no = fd.original_demand_code
            </when>
        </choose>
        <where>
            <if test="planQuery.userNo != null and planQuery.userNo.size() > 0">
                and fd.publisher_no in
                <foreach collection="planQuery.userNo" item="userNo" open="(" separator="," close=")">
                    #{userNo}
                </foreach>
            </if>
            <if test="planQuery.orderNo != null and planQuery.orderNo != ''">
                AND fd.flight_order_no = #{planQuery.orderNo}
            </if>
            <if test="planQuery.flightUnitId != null and planQuery.flightUnitId != ''">
                AND fp.flight_unit_id = #{planQuery.flightUnitId}
            </if>
            <if test="planQuery.flightUnitName != null and planQuery.flightUnitName != ''">
                AND fp.flight_unit like CONCAT('%', #{planQuery.flightUnitName}, '%')
            </if>
            <if test="planQuery.requirementType != null and planQuery.requirementType != ''">
                AND fd.demand_type = #{planQuery.requirementType}
            </if>
            <if test="planQuery.startTime != null and planQuery.endTime != null">
                AND (fp.planed_takeoff_time &gt;= (EXTRACT(EPOCH FROM (#{planQuery.startTime})::timestamptz) * 1000)::bigint
                AND fp.planed_takeoff_time &lt;= (EXTRACT(EPOCH FROM (#{planQuery.endTime})::timestamptz) * 1000)::bigint)
            </if>
            <if test="planQuery.planName != null and planQuery.planName != ''">
                AND fp.plan_name like CONCAT('%', #{planQuery.planName}, '%')
            </if>
            <if test="planQuery.userType == 'customer' and planQuery.requirementId != null and planQuery.requirementId != ''">
                AND fd.original_demand_code = #{planQuery.requirementId}
            </if>
            <if test="planQuery.userType != 'customer' and planQuery.requirementId != null and planQuery.requirementId != ''">
                AND fp.biz_no = #{planQuery.requirementId}
            </if>
            <if test="planQuery.userType == 'customer' and planQuery.requirementName != null and planQuery.requirementName != ''">
                AND fd.original_demand_name like CONCAT('%', #{planQuery.requirementName}, '%')
            </if>
            <if test="planQuery.userType != 'customer' and planQuery.requirementName != null and planQuery.requirementName != ''">
                AND fp.requirement_name like CONCAT('%', #{planQuery.requirementName}, '%')
            </if>
            <if test="planQuery.uavModel != null and planQuery.uavModel != ''">
                AND fp.uav_model like CONCAT('%', #{planQuery.uavModel}, '%')
            </if>
            <if test="planQuery.account != null and planQuery.account != ''">
                and fdd.biz_data::json->>'account' = #{planQuery.account}
            </if>
        </where>
        <if test="planQuery.userType != 'customer'">
            GROUP BY fp.id
        </if>
        ORDER BY
        <choose>
            <when test="planQuery.orderBy == 'planCreateTime'">
                fp.plan_create_time
            </when>
            <otherwise>
                fp.planed_takeoff_time
            </otherwise>
        </choose>
        DESC, fp.id desc
    </select>

    <!-- 根据订单编号查询计划列表 -->
    <select id="getFlightPlanListByOrderNo"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO">
        select
            fp.id,
            fp.mission_id,
            fp.plan_id,
            fp.uav_id,
            fp.status,
            fp.planed_takeoff_time,
            fp.planed_landing_time,
            fp.operator_user,
            fp.operator_phone,
            fp.gmt_created,
            fp.gmt_modified,
            fp.landing_aerodrome_id,
            fp.arrival_aerodrome_id,
            fp.alternate_aerodrome_id,
            fp.planed_altitude,
            fp.real_ame_verification,
            fp.flight_unit,
            fp.flight_unit_id,
            fp.apply_user_name,
            fp.airspace_id,
            fp.scence_code,
            fp.biz_no,
            fp.tenant_id,
            fp.related_id,
            fp.apply_type,
            fp.out_plan_no,
            fp.plan_name,
            <if test="planQuery.userType == 'operation' or planQuery.userType == 'supplier'">
                fd.requirement_name,
            </if>
            <if test="planQuery.userType == 'customer'">
                fd.original_demand_name as requirement_name,
            </if>
            fp.uav_model,
            fp.flight_report,
            fp.monitor_url
        from flight_plan fp
        left join flight_merge_demand_relation fmdr on fp.biz_no = fmdr.target_demand_code
        <where>
            <if test="orderNo != null and orderNo != ''">
                fmdr.flight_order_no = #{orderNo}
            </if>
            <if test="demandNo != null and demandNo != ''">
                fmdr.target_demand_code = #{demandNo}
            </if>
        </where>
    </select>

    <select id="getFlightPlanList"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO">
        SELECT DISTINCT
            fp.plan_id,
            fp.status,
            fp.planed_takeoff_time
        FROM
            flight_plan fp
        LEFT JOIN flight_merge_demand_relation fd ON fp.biz_no = fd.target_demand_code
        where
            fp.biz_no is not null
            AND fp.planed_takeoff_time &gt;= (EXTRACT(EPOCH FROM (CURRENT_DATE)::timestamptz) * 1000)::bigint
            AND fp.planed_takeoff_time &lt; ((EXTRACT(EPOCH FROM (CURRENT_DATE + INTERVAL '1 day')::timestamptz)) * 1000)::bigint
        <if test="planQuery.userNo != null and planQuery.userNo.size() > 0">
            AND fd.publisher_no in
            <foreach collection="planQuery.userNo" item="userNo" open="(" separator="," close=")">
                #{userNo}
            </foreach>
        </if>
    </select>

    <select id="getYesterdayFlightPlanList"
            resultType="com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO">
        SELECT DISTINCT
            fp.plan_id,
            fp.status,
            fp.planed_takeoff_time
        FROM flight_plan fp
            LEFT JOIN flight_merge_demand_relation fd
                ON fp.biz_no = fd.target_demand_code
        WHERE
            <!-- 截止到昨天 23:59:59.999（< 今天 00:00:00） -->
            fp.biz_no is not null
            AND fp.planed_takeoff_time &lt; ((EXTRACT(EPOCH FROM (CURRENT_DATE + INTERVAL '1 day')::timestamptz)) * 1000)::bigint
        <if test="planQuery.userNo != null and planQuery.userNo.size() > 0">
            AND fd.publisher_no IN
            <foreach collection="planQuery.userNo"
                     item="userNo"
                     open="(" separator="," close=")">
                #{userNo}
            </foreach>
        </if>
    </select>

</mapper>
