package com.deepinnet.skyflow.operationcenter.service.event;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.context.TenantContext;
import com.deepinnet.skyflow.operationcenter.service.demand.FlightDemandService;
import com.deepinnet.skyflow.operationcenter.service.util.TimeFrequencyUtil;
import com.deepinnet.trace.common.CommonFilter;
import org.slf4j.MDC;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * Description:
 * Date: 2025/4/17
 * Author: lijunheng
 */
@Component
public class FlightDemandCreateEventListener implements ApplicationListener<FlightDemandCreateEvent> {

    private static final Logger logger = LoggerFactory.getLogger(FlightDemandCreateEventListener.class);

    @Resource
    private FlightDemandService flightDemandService;

    @Override
    public void onApplicationEvent(FlightDemandCreateEvent event) {
        try {
            TenantContext.disableTenantLine();
            MDC.remove(CommonFilter.TENANT_ID);
            String flightDemandNo = event.getFlightDemandNo();
            logger.info("Received FlightDemandCreateEvent for demand: {}", flightDemandNo);

            // 获取需求详情
            FlightDemandDTO demandDTO = flightDemandService.getFlightDemandByNo(flightDemandNo);
            if (demandDTO == null) {
                logger.error("Flight demand not found with number: {}", flightDemandNo);
                return;
            }

            //补报的需求不需要走分配服务商
            if (Objects.equals(demandDTO.getIsSupplement(), true)) {
                return;
            }

            if (Objects.equals(demandDTO.getType(), FlightDemandTypeEnum.EMERGENCY_RESPONSE)) {
                //应急类需求直接走分配逻辑
                flightDemandService.matchAndSyncDemand(demandDTO);

            } else {
                //  除应急外的其他类型需求，如果距离起飞时间已不足72小时，就直接走分配逻辑
                if (demandDTO.getType() == FlightDemandTypeEnum.ROUTINE_INSPECTION) {
                    boolean moreThan = TimeFrequencyUtil.moreThanHourBeforeTime(demandDTO.getRoutineInspectionDetail().getDemandStartTime(), 72);
                    if (!moreThan) {
                        flightDemandService.matchAndSyncDemand(demandDTO);
                    }
                    //其余的需求表示可以走合并需求逻辑，由定时任务触发
                }
            }
            logger.info("handle finish FlightDemandCreateEvent for demand: {}", flightDemandNo);
        } finally {
            TenantContext.clear();
        }
    }
}