package com.deepinnet.skyflow.operationcenter.service.client;

import com.deepinnet.localdata.integration.GaoDeEnterpriseMapClient;
import com.deepinnet.localdata.integration.model.outsidebean.GDPageResult;
import com.deepinnet.localdata.integration.model.outsidebean.PoiSearchKeyResponse;
import com.deepinnet.localdata.integration.model.outsidebean.PoiSearchRequest;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description:
 * Date: 2025/7/27
 * Author: lijunheng
 */
@Component
public class GaoDeEnterpriseMapRemoteClient {

    @Resource
    private GaoDeEnterpriseMapClient gaoDeEnterpriseMapClient;

    public GDPageResult<PoiSearchKeyResponse> searchKeyPoi(PoiSearchRequest request) {
        return gaoDeEnterpriseMapClient.searchKeyPoi(request);
    }
}
