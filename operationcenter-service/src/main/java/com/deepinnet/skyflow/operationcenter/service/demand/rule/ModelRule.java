package com.deepinnet.skyflow.operationcenter.service.demand.rule;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description: 机型规则
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Component
public class ModelRule extends AbstractDemandMergeRule {

    @Override
    protected DemandMergeRuleResult execute(RuleContext<DemandMergeRuleResult> context) {
        DemandMergeRuleResult previousRuleExecResult = context.getPreviousRuleExecResult();
        List<List<FlightDemandDTO>> fromFilterDemandGroup = previousRuleExecResult.getFilterDemandGroup();
        
        List<List<FlightDemandDTO>> toFilterDemandGroup = new ArrayList<>();
        
        // 遍历每个需求分组
        for (List<FlightDemandDTO> demandGroup : fromFilterDemandGroup) {
            if (CollectionUtils.isEmpty(demandGroup) || demandGroup.size() < 2) {
                // 单个需求或空分组不需要增量服务规则处理
                continue;
            }
            
            // 按机型服务进行分组（需求的机型服务必须完全一致才能合并）
            Map<String, List<FlightDemandDTO>> modelGroupMap = demandGroup.stream()
                .collect(Collectors.groupingBy(this::getModelKey));
            
            // 只保留包含2个或以上需求的分组
            for (List<FlightDemandDTO> modelGroup : modelGroupMap.values()) {
                if (modelGroup.size() >= 2) {
                    toFilterDemandGroup.add(modelGroup);
                }
            }
        }

        DemandMergeRuleResult ruleResult = new DemandMergeRuleResult();
        ruleResult.setFilterDemandGroup(toFilterDemandGroup);
        return ruleResult;
    }

    @Override
    public int order() {
        return 301;
    }

    private String getModelKey(FlightDemandDTO demand) {
        List<String> modelCodeList = demand.getModelCodeList();
        
        if (CollectionUtils.isEmpty(modelCodeList)) {
            return "EMPTY_MODEL";
        }
        
        // 对机型列表进行排序，确保相同内容的列表产生相同的key
        List<String> sortedModelList = new ArrayList<>(modelCodeList);
        Collections.sort(sortedModelList);
        
        // 将排序后的服务列表连接成字符串作为key
        return String.join(",", sortedModelList);
    }

}
