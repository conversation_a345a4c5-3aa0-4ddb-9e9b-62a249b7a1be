package com.deepinnet.skyflow.operationcenter.service.util;

import cn.hutool.core.util.ObjectUtil;
import com.deepinnet.skyflow.operationcenter.service.constants.UserDepartmentIdConstants;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/11
 */
public class UserDepartmentUtil {

    public static Boolean isRootDepartment(List<Long> departmentIds) {

        return departmentIds.contains(UserDepartmentIdConstants.ROOT_DEPARTMENT_ID);

    }

    public static Boolean isRootDepartment(Long departmentId) {

        return ObjectUtil.equals(UserDepartmentIdConstants.ROOT_DEPARTMENT_ID, departmentId);

    }

}
