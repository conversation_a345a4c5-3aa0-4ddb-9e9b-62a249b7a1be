package com.deepinnet.skyflow.operationcenter.service.client;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.dto.UserDetailDTO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightPlanPageQuery;
import com.deepinnet.spatiotemporalplatform.client.skyflow.FlightClient;
import com.deepinnet.spatiotemporalplatform.dto.BatchQueryAerodromeDTO;
import com.deepinnet.spatiotemporalplatform.dto.BatchQueryPlanDTO;
import com.deepinnet.spatiotemporalplatform.dto.PositionQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/23
 */

@Component
public class FlightPlanQueryClient {

    @Resource
    private FlightClient flightClient;

    public FlightPlanVO queryFlightPlan(String flightPlanId) {
        Result<FlightPlanVO> flightPlanVOResult = flightClient.queryPlanDetail(flightPlanId);

        if (!flightPlanVOResult.isSuccess()) {
            LogUtil.error("queryFlightPlan error:{}", flightPlanVOResult.getErrorDesc());
            throw new BizException(flightPlanVOResult.getErrorCode(), flightPlanVOResult.getErrorDesc());
        }

        return flightPlanVOResult.getData();
    }

    public List<AerodromeVO> batchQueryAerodrome(List<String> ids) {
        BatchQueryAerodromeDTO queryAerodromeDTO = new BatchQueryAerodromeDTO();
        queryAerodromeDTO.setAerodromeIds(ids);
        Result<List<AerodromeVO>> aerodromeList = flightClient.queryAerodromeList(queryAerodromeDTO);

        if (!aerodromeList.isSuccess()) {
            LogUtil.error("batchQueryAerodrome error:{}", aerodromeList.getErrorDesc());
            throw new BizException(aerodromeList.getErrorCode(), aerodromeList.getErrorDesc());
        }

        return aerodromeList.getData();
    }

    public List<FlightCountVO> planCountByBizNo(BatchQueryPlanDTO batchQueryPlanDTO) {
        Result<List<FlightCountVO>> flightCountResult = flightClient.planCountByBizNo(batchQueryPlanDTO);
        if (!flightCountResult.isSuccess()) {
            throw new BizException(flightCountResult.getErrorCode(), flightCountResult.getErrorDesc());
        }
        return flightCountResult.getData();
    }

    public List<RealTimeUavFlightVO> getUavPosition(PositionQueryDTO queryDTO) {
        Result<List<RealTimeUavFlightVO>> result = flightClient.getUavPosition(queryDTO);

        if (!result.isSuccess()) {
            LogUtil.error("getUavPosition error:{}", result.getErrorDesc());
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }

    public List<UavFlightTrackVO> getUavTrajectory(PositionQueryDTO queryDTO) {

        Result<List<UavFlightTrackVO>> result = flightClient.getUavTrajectory(queryDTO);

        if (!result.isSuccess()) {
            LogUtil.error("getUavTrajectory error:{}", result.getErrorDesc());
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }

    public String getLiveUrl(String sn, String httpProtocol) {
        Result<String> uavLiveUrl = flightClient.getUavLiveUrl(sn, httpProtocol);

        if (!uavLiveUrl.isSuccess()) {
            throw new BizException(uavLiveUrl.getErrorCode(), uavLiveUrl.getErrorDesc());
        }

        return uavLiveUrl.getData();
    }
}
