package com.deepinnet.skyflow.operationcenter.service.rule.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.MergeRuleConfigDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.MergeRuleConfigRepository;
import com.deepinnet.skyflow.operationcenter.dto.MergeDemandRuleDTO;
import com.deepinnet.skyflow.operationcenter.enums.MergeRuleTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.convert.MergeRuleConfigConvert;
import com.deepinnet.skyflow.operationcenter.service.rule.DemandMergeRuleService;
import com.deepinnet.skyflow.operationcenter.service.task.FlightDemandMergeTask;
import com.deepinnet.skyflow.operationcenter.vo.MergeRuleConfigVO;
import com.deepinnet.tenant.TenantIdUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/11
 */

@Service
public class DemandMergeRuleServiceImpl implements DemandMergeRuleService {

    @Resource
    private MergeRuleConfigRepository ruleConfigRepository;

    @Resource
    private MergeRuleConfigConvert mergeRuleConfigConvert;

    @Resource
    private ThreadPoolExecutor commonTaskExecutor;

    @Resource
    private FlightDemandMergeTask flightDemandMergeTask;

    @Override
    public List<MergeRuleConfigVO> ruleList() {
        List<MergeRuleConfigDO> ruleConfigDOList = ruleConfigRepository.list();

        return  mergeRuleConfigConvert.toVOList(ruleConfigDOList);
    }

    @Override
    public Boolean save(MergeDemandRuleDTO rule) {
        Assert.notNull(rule, "规则参数不能为空!");

        MergeRuleConfigDO existRuleConfig = ruleConfigRepository.getOne(Wrappers.<MergeRuleConfigDO>lambdaQuery()
                .eq(MergeRuleConfigDO::getRuleType, MergeRuleTypeEnum.DEMAND_RULE_CONFIG.getRuleType()));

        boolean result =  saveOrUpdateMergeRule(rule, existRuleConfig);

        if (result) {
            commonTaskExecutor.submit(() -> flightDemandMergeTask.execute());
        }

        return result;

    }

    private boolean saveOrUpdateMergeRule(MergeDemandRuleDTO rule, MergeRuleConfigDO existRuleConfig) {
        MergeRuleConfigDO mergeRuleConfigDO = new MergeRuleConfigDO();
        mergeRuleConfigDO.setRuleType(MergeRuleTypeEnum.DEMAND_RULE_CONFIG.getRuleType());
        mergeRuleConfigDO.setRuleValue(rule.getDemandRuleConfig());
        mergeRuleConfigDO.setTenantId(TenantIdUtil.getTenantId());
        mergeRuleConfigDO.setIsActive(true);


        if (ObjectUtil.isNull(existRuleConfig)) {
            return ruleConfigRepository.save(mergeRuleConfigDO);
        }

        mergeRuleConfigDO.setId(existRuleConfig.getId());
        mergeRuleConfigDO.setGmtModified(LocalDateTime.now());

        return ruleConfigRepository.updateById(mergeRuleConfigDO);
    }

}
