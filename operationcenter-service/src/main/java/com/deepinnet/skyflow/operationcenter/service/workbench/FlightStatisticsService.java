package com.deepinnet.skyflow.operationcenter.service.workbench;

import com.deepinnet.skyflow.operationcenter.dto.BizStatQueryDTO;
import com.deepinnet.skyflow.operationcenter.vo.BizStatVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightWorkbenchCoreVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *    飞行统计接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/18
 */
public interface FlightStatisticsService {

    FlightWorkbenchCoreVO getCoreView();

    List<BizStatVO> getRangeDetail(BizStatQueryDTO dto);

    void exportRangeDetail(BizStatQueryDTO dto, HttpServletResponse response);

    Integer pendingApprovalCount(String type);
}
