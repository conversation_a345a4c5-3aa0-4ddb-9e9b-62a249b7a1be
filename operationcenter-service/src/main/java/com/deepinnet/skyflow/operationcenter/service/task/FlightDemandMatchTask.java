package com.deepinnet.skyflow.operationcenter.service.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandRepository;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.service.context.TenantContext;
import com.deepinnet.skyflow.operationcenter.service.demand.DemandLockHelper;
import com.deepinnet.skyflow.operationcenter.service.demand.FlightDemandService;
import com.deepinnet.trace.common.CommonFilter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Description: 航班需求匹配定时任务
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Slf4j
@Component
public class FlightDemandMatchTask {

    @Resource
    private FlightDemandService flightDemandService;

    @Resource
    private FlightDemandRepository flightDemandRepository;

    @Resource
    private DemandLockHelper demandLockHelper;

    //每天凌晨2点执行
    @Scheduled(cron = "0 0 2 * * ? ")
    public void execute() {
        log.info("开始执行需求匹配任务");

        try {
            TenantContext.disableTenantLine();
            MDC.remove(CommonFilter.TENANT_ID);

            Boolean canExec = null;
            do {
                // 如果获取不到就等10秒后再重试
                if (canExec != null) {
                    ThreadUtil.sleep(10, TimeUnit.SECONDS);
                }
                canExec = demandLockHelper.tryLockMatchDemandTask();
            }
            while(!canExec);

            // 查找待分配的需求
            List<FlightDemandDTO> pendingDemands = findPendingDemands();

            if (CollectionUtil.isEmpty(pendingDemands)) {
                log.info("没有找到待分配的需求");
                return;
            }

            log.info("找到{}个待分配的需求", pendingDemands.size());

            // 执行需求匹配和分配
            int successCount = 0;
            int failureCount = 0;

            for (FlightDemandDTO demand : pendingDemands) {
                try {
                    log.debug("开始处理需求: {}", demand.getDemandNo());
                    flightDemandService.matchAndSyncDemand(demand);
                    successCount++;
                    log.debug("成功处理需求: {}", demand.getDemandNo());
                } catch (Exception e) {
                    failureCount++;
                    log.error("处理需求失败: {}", demand.getDemandNo(), e);
                }
            }

            log.info("需求匹配任务执行完成，成功: {}, 失败: {}", successCount, failureCount);

        } catch (Exception e) {
            log.error("执行需求匹配任务失败", e);
        } finally {
            demandLockHelper.unlockMatchDemandTask();
            TenantContext.clear();
        }
    }

    /**
     * 查找待分配的需求
     * 过滤条件：
     * 1. 只处理日常巡检类型的需求
     * 2. 不是处于已分配状态的需求
     * 3. 如果是原始需求：必须不是处于合并状态的
     * 4. 如果是合并需求：必须是已合并状态的
     * 5. 距离起飞时间小于72小时（通过联表查询flight_demand_routine_inspection获取demand_start_time）
     */
    private List<FlightDemandDTO> findPendingDemands() {
        List<FlightDemandDTO> allDemands = new ArrayList<>();
        Long lastId = 0L;  // 从ID为0开始查询
        int batchSize = 100;  // 每批处理100条数据
        
        while (true) {
            // 使用游标分页查询方法
            List<FlightDemandDO> batchDemands = flightDemandRepository.selectPendingMatchDemands(lastId, batchSize);
            
            if (CollectionUtil.isEmpty(batchDemands)) {
                break;  // 没有更多数据，退出循环
            }
            
            log.debug("本批次找到{}个待分配的需求，lastId: {}", batchDemands.size(), lastId);
            
            // 转换为DTO并加载完整的需求信息
            List<FlightDemandDTO> batchDTOs = batchDemands.stream()
                    .map(demand -> flightDemandService.getFlightDemandByNo(demand.getDemandNo()))
                    .collect(Collectors.toList());
            
            allDemands.addAll(batchDTOs);
            
            // 更新lastId为本批次最后一个记录的ID
            lastId = batchDemands.get(batchDemands.size() - 1).getId();
            
            if (batchDemands.size() < batchSize) {
                break;  // 最后一批数据，退出循环
            }
        }
        
        log.debug("总共找到{}个待分配的需求", allDemands.size());
        return allDemands;
    }
}
