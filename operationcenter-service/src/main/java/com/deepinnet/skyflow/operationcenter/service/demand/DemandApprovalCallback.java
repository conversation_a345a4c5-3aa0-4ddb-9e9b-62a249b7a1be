package com.deepinnet.skyflow.operationcenter.service.demand;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandRepository;
import com.deepinnet.skyflow.operationcenter.service.approval.ApprovalCallback;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApproveStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.constants.ApproveBizTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.event.FlightDemandCreateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * Description:
 * Date: 2025/7/11
 * Author: lijunheng
 */
@Component
@Slf4j
public class DemandApprovalCallback implements ApprovalCallback {

    @Resource
    private FlightDemandRepository flightDemandRepository;

    @Resource
    private ApplicationEventPublisher publisher;

    @Override
    public void onApproved(String bizId, Object param) {
        log.info("需求审批通过回调，bizId:{}, param:{}", bizId, param);
        //bizId是当前需求的编号，现在采用的是版本链的方式编辑需求，每次编辑会产生新的需求编号，通过parentCode关联
        flightDemandRepository.update(Wrappers.lambdaUpdate(FlightDemandDO.class)
                .set(FlightDemandDO::getApproveStatus, ApproveStatusEnum.APPROVED.name())
                .set(FlightDemandDO::getApproveEndTime, LocalDateTime.now())
                .eq(FlightDemandDO::getDemandNo, bizId)
        );

        //发布需求创建事件，异步通知需要关心的业务
        publisher.publishEvent(new FlightDemandCreateEvent(bizId));
    }

    @Override
    public void onRejected(String bizId, Object param) {
        log.info("需求审批不通过回调，bizId:{}, param:{}", bizId, param);
        flightDemandRepository.update(Wrappers.lambdaUpdate(FlightDemandDO.class)
                .set(FlightDemandDO::getApproveStatus, ApproveStatusEnum.REJECTED.name())
                .eq(FlightDemandDO::getDemandNo, bizId)
        );
    }

    @Override
    public String supportBizType() {
        return ApproveBizTypeEnum.FLIGHT_DEMAND.name();
    }
}
