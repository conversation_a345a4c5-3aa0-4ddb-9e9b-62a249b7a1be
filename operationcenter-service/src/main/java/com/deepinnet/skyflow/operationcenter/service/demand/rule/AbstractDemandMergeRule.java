package com.deepinnet.skyflow.operationcenter.service.demand.rule;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.service.rule.IRule;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleContext;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleTypeEnum;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractDemandMergeRule implements IRule<DemandMergeRuleResult> {

    @Override
    public RuleTypeEnum supportRuleType() {
        return RuleTypeEnum.DEMAND_MERGE_TYPE;
    }

    @Override
    public DemandMergeRuleResult apply(RuleContext<DemandMergeRuleResult> context) {
        Object logObj;
        if (context.getRuleIndex() == 0) {
            logObj = context.getInputParam();
        } else {
            //从第二个规则开始打印
            DemandMergeRuleResult previousRuleExecResult = context.getPreviousRuleExecResult();
            List<List<ImmutableMap<String, String>>> demandCodeMap = new ArrayList<>();
            if (previousRuleExecResult != null) {
                List<List<FlightDemandDTO>> filterDemandGroup = previousRuleExecResult.getFilterDemandGroup();
                demandCodeMap = filterDemandGroup.stream()
                        .map(demandDTOList -> demandDTOList.stream()
                                .map(demand ->
                                        ImmutableMap.of("demandCode", demand.getDemandNo(), "demandName", demand.getName()))
                                .collect(Collectors.toList()))
                        .collect(Collectors.toList());
            }
            logObj = demandCodeMap;
        }
        log.info("{} 规则开始执行，入参是：{}", getClass().getName(), logObj);

        return execute(context);
    }

    protected abstract DemandMergeRuleResult execute(RuleContext<DemandMergeRuleResult> context);
}
