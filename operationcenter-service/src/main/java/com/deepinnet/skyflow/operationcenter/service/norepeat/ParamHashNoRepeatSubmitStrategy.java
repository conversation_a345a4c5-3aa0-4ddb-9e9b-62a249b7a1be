package com.deepinnet.skyflow.operationcenter.service.norepeat;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.norepeat.resolve.BizKeyResolver;
import com.deepinnet.skyflow.operationcenter.service.strategy.Strategy;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyContext;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyTypeEnum;
import org.apache.commons.codec.digest.DigestUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;

/**
 * Description:
 * Date: 2025/7/30
 * Author: lijunheng
 */
@Component
public class ParamHashNoRepeatSubmitStrategy implements Strategy {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private BeanFactory beanFactory;

    @Override
    public boolean supports(StrategyTypeEnum strategyType, Enum<?> bizEnum) {
        return strategyType == StrategyTypeEnum.NO_REPEAT_SUBMIT && bizEnum == RepeatMode.PARAM_HASH;
    }

    @Override
    public Boolean execute(StrategyContext context) {
        ProceedingJoinPoint joinPoint = context.getFirstArg();
        NoRepeatSubmit noRepeatSubmit = context.getSecondArg();
        String hashKey = buildHashKey(joinPoint, noRepeatSubmit);

        //setNx + 过期时间，设置过期时间是为了让过一段时间后可以重复发起请求，也不会一直占用存储
        Boolean setOk = redisTemplate.opsForValue().setIfAbsent(hashKey, "1", noRepeatSubmit.timeout(), noRepeatSubmit.timeUnit());

        //如果保存不成功就说明重复请求
        if (!setOk) {
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "请勿重复提交");
        }
        return Boolean.TRUE;
    }

    private String buildHashKey(ProceedingJoinPoint joinPoint, NoRepeatSubmit ann) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = signature.getParameterNames();
        Object[] args = Arrays.stream(joinPoint.getArgs()).map(this::copyObj).toArray();

        // SpEL 上下文
        EvaluationContext context = new StandardEvaluationContext();
        for (int i = 0; i < paramNames.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }

        List<String> parts = new ArrayList<>();
        ExpressionParser parser = new SpelExpressionParser();

        //先过滤掉excludeFields
        for (String excludeField : ann.excludeFields()) {
            parser.parseExpression(excludeField).setValue(context, null);
        }

        if (ann.includeFields().length > 0) {
            // 只计算指定字段
            for (String fieldExpr : ann.includeFields()) {
                Object value = parser.parseExpression(fieldExpr).getValue(context);
                parts.add(convertObj2Str(value));
            }
        } else {
            // 没有设置 fields，则计算所有参数
            for (Object arg : args) {
                parts.add(convertObj2Str(arg));
            }
        }

        //处理bizKey
        for (NoRepeatSubmit.BizKey bizKey : ann.bizKeys()) {
            BizKeyResolver resolver = beanFactory.getBean(bizKey.type().getResolver());
            Object resolvedObj = resolver.resolve(bizKey.name());
            parts.add(convertObj2Str(resolvedObj));
        }

        // 将 parts 拼接后生成 SHA-256
        String rawKey = String.join("|", parts);
        return DigestUtils.sha256Hex(rawKey);
    }

    private String convertObj2Str(Object part) {
        //这里要判断是否可序列化，因为如果是HttpServletRequest、MultipartFile等参数，则不能比较其中参数是否重复
        if (!isSerializableParam(part)) {
            return null;
        }
        //这里的json序列化做了字段排序
        return JsonUtil.toJsonStr(part);
    }

    private boolean isSerializableParam(Object obj) {
        if (obj == null) return false;

        Class<?> clazz = obj.getClass();

        // 基本类型
        if (clazz.isPrimitive()) {
            return true;
        }

        // 业务对象实现了Serializable
        return Serializable.class.isAssignableFrom(clazz);
    }

    private Object copyObj(Object object) {
        return JsonUtil.convertValue(object, object.getClass());
    }
}
