package com.deepinnet.skyflow.operationcenter.service.demand.rule;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.service.util.MergedScheduleTimeEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 包含时间周期信息的需求分组
 * 
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DemandGroupWithSchedule {
    
    /**
     * 需求分组
     */
    private List<FlightDemandDTO> demandGroup;
    
    /**
     * 合并后的时间周期信息
     */
    private MergedScheduleTimeEntity mergedScheduleTime;
} 