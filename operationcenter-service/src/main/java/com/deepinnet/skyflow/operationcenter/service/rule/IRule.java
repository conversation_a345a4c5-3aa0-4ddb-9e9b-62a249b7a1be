package com.deepinnet.skyflow.operationcenter.service.rule;

/**
 * Description: 需求合并规则
 * Date: 2025/6/18
 * Author: lijunheng
 */
public interface IRule<T extends RuleResult> {

    /**
     * 执行需求合并规则
     *
     * @param context 上下文
     * @return 需求编号列表
     */
    T apply(RuleContext<T> context);

    /**
     * 排序
     *
     * @return
     */
    int order();

    /**
     * 支持的规则类型
     * @return
     */
    RuleTypeEnum supportRuleType();

    /**
     * 是否能够执行
     * @param context
     * @return
     */
    default boolean canExec(RuleContext<T> context) {
        return true;
    }
}
