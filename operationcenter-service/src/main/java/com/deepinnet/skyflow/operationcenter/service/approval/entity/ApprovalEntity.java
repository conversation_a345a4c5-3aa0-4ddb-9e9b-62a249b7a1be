package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 * Date: 2025/7/14
 * Author: lijunheng
 */
@Data
public class ApprovalEntity implements Serializable {

    /*************非必传*************/
    /**
     * 业务id
     */
    private String bizId;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 审批id，如果没传则按bizId查找当前处于审批中状态的审批单
     */
    private String approvalId;

    /*************必传*************/
    /**
     * 审批人id
     */
    private String approveUserId;

    private boolean isApproved;

    private String remark;

    /**
     * 租户id
     */
    private String tenantId;
}
