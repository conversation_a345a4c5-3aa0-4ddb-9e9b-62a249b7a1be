package com.deepinnet.skyflow.operationcenter.service.demand.rule;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Component
public class IncrementServiceRule extends AbstractDemandMergeRule {

    @Override
    protected DemandMergeRuleResult execute(RuleContext<DemandMergeRuleResult> context) {
        DemandMergeRuleResult previousRuleExecResult = context.getPreviousRuleExecResult();
        List<List<FlightDemandDTO>> fromFilterDemandGroup = previousRuleExecResult.getFilterDemandGroup();
        
        List<List<FlightDemandDTO>> toFilterDemandGroup = new ArrayList<>();
        
        // 遍历每个需求分组
        for (List<FlightDemandDTO> demandGroup : fromFilterDemandGroup) {
            if (CollectionUtils.isEmpty(demandGroup) || demandGroup.size() < 2) {
                // 单个需求或空分组不需要增量服务规则处理
                continue;
            }
            
            // 按增值服务进行分组（需求的增值服务必须完全一致才能合并）
            Map<String, List<FlightDemandDTO>> serviceGroupMap = demandGroup.stream()
                .collect(Collectors.groupingBy(this::getIncrementServiceKey));
            
            // 只保留包含2个或以上需求的分组
            for (List<FlightDemandDTO> serviceGroup : serviceGroupMap.values()) {
                if (serviceGroup.size() >= 2) {
                    toFilterDemandGroup.add(serviceGroup);
                }
            }
        }

        DemandMergeRuleResult ruleResult = new DemandMergeRuleResult();
        ruleResult.setFilterDemandGroup(toFilterDemandGroup);
        return ruleResult;
    }

    @Override
    public int order() {
        return 300;
    }

    /**
     * 获取需求的增值服务标识key，用于分组
     * 相同key的需求具有相同的增值服务，可以合并
     * @param demand 需求对象
     * @return 增值服务标识key
     */
    private String getIncrementServiceKey(FlightDemandDTO demand) {
        List<String> incrementService = demand.getIncrementService();
        
        if (CollectionUtils.isEmpty(incrementService)) {
            // 如果增值服务为空，返回特殊标识
            return "EMPTY_INCREMENT_SERVICE";
        }
        
        // 对增值服务列表进行排序，确保相同内容的列表产生相同的key
        List<String> sortedServices = new ArrayList<>(incrementService);
        Collections.sort(sortedServices);
        
        // 将排序后的服务列表连接成字符串作为key
        return String.join(",", sortedServices);
    }

}
