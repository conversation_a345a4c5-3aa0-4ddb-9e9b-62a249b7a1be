package com.deepinnet.skyflow.operationcenter.service.helper;

import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.skyflow.operationcenter.enums.MergeRuleTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.rule.DemandMergeRuleService;
import com.deepinnet.skyflow.operationcenter.vo.MergeRuleConfigVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2025/6/19
 * Author: lijunheng
 */
@Service
public class DemandMergeRuleHelper {

    @Resource
    private DemandMergeRuleService demandMergeRuleService;

    /**
     * 样例：[{"demandType":"ROUTINE_INSPECTION","distanceLimit":2},{"demandType":"LOGISTICS_TRANSPORTATION","distanceLimit":3}]
     *
     * @return
     */
    public List<DemandTypeDistanceConfigItem> getDemandTypeAndDistanceConfigItem(String tenantId) {
        List<MergeRuleConfigVO> mergeRuleConfigVOS = demandMergeRuleService.ruleList();
        return mergeRuleConfigVOS.stream()
                .filter(p -> Objects.equals(p.getTenantId(), tenantId) && Objects.equals(p.getRuleType(), MergeRuleTypeEnum.DEMAND_RULE_CONFIG))
                .map(p -> JsonUtil.parseToList(p.getRuleValue(), DemandTypeDistanceConfigItem.class))
                .findFirst().get();
    }

    public List<String> getAllowDemandTypeList(List<DemandTypeDistanceConfigItem> demandTypeAndDistanceConfigItem) {
        return demandTypeAndDistanceConfigItem.stream()
                .map(DemandTypeDistanceConfigItem::getDemandType)
                .collect(Collectors.toList());
    }

    public Double getDemandTypeDistanceLimit(List<DemandTypeDistanceConfigItem> demandTypeAndDistanceConfigItem, String demandType) {
        for (DemandTypeDistanceConfigItem item : demandTypeAndDistanceConfigItem) {
            if (Objects.equals(item.getDemandType(), demandType)) {
                return item.getDistanceLimit();
            }
        }
        return 0d;
    }
}
