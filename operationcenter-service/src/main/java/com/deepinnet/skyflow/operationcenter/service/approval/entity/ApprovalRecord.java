package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description:
 * Date: 2025/7/16
 * Author: lijunheng
 */
@Data
public class ApprovalRecord implements Serializable {

    private String approvalId;

    /**
     * 审批发起人ID
     */
    private String submitUserId;

    private String submitUserName;

    private String submitDepartmentId;

    private String submitDepartmentName;

    private String submitUserPhone;

    private ApproveUserEntity submitUserEntity;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    private ApproveStatusEnum status;

    private List<ApprovalNode> nodeRecordList;
}
