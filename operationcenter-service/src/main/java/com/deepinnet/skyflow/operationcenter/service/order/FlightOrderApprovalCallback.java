package com.deepinnet.skyflow.operationcenter.service.order;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderRepository;
import com.deepinnet.skyflow.operationcenter.enums.OrderStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.approval.ApprovalCallback;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApproveStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.constants.ApproveBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/23
 */

@Component
@Slf4j
public class FlightOrderApprovalCallback implements ApprovalCallback {

    @Resource
    private FlightOrderRepository flightOrderRepository;

    @Override
    public String supportBizType() {
        return ApproveBizTypeEnum.FLIGHT_DEMAND_PLAN.name();
    }

    @Transactional(rollbackFor = Exception.class)
    public void onApproved(String bizId, Object param) {
        log.info("[飞行规划]审批通过回调，bizId:{}, param:{}", bizId, param);
        LocalDateTime dateTime = LocalDateTime.now();
        flightOrderRepository.update(Wrappers.<FlightOrderDO>lambdaUpdate()
                .eq(FlightOrderDO::getOrderNo, bizId)
                .eq(FlightOrderDO::getOrderType, OrderTypeEnum.DEMAND_PLAN.getCode())
                .set(FlightOrderDO::getApproveStatus, ApproveStatusEnum.APPROVED.getStatusCode())
                .set(FlightOrderDO::getStatus, OrderStatusEnum.IN_PROGRESS.getCode())
                .set(FlightOrderDO::getApprovedTime, dateTime)
                .set(FlightOrderDO::getGmtModified, dateTime));
    }

    @Override
    public void onRejected(String bizId, Object param) {
        log.info("[飞行规划]审批不通过回调，bizId:{}, param:{}", bizId, param);
        flightOrderRepository.update(Wrappers.<FlightOrderDO>lambdaUpdate()
                .eq(FlightOrderDO::getOrderNo, bizId)
                .eq(FlightOrderDO::getOrderType, OrderTypeEnum.DEMAND_PLAN.getCode())
                .set(FlightOrderDO::getApproveStatus, ApproveStatusEnum.REJECTED.getStatusCode())
                .set(FlightOrderDO::getStatus, OrderStatusEnum.CLOSED.getCode())
                .set(FlightOrderDO::getGmtModified, LocalDateTime.now()));
    }
}
