package com.deepinnet.skyflow.operationcenter.service.demand;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightDemandStatCondition;
import com.deepinnet.skyflow.operationcenter.dto.*;

import java.util.List;
import java.util.Map;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandAssignDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalEntity;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;

/**
 * 飞行需求服务接口
 *
 * <AUTHOR>
 */
public interface FlightDemandService {

    /**
     * 保存飞行需求
     *
     * @param flightDemandDTO 飞行需求信息
     * @return 需求编号
     */
    String saveFlightDemand(FlightDemandDTO flightDemandDTO);

    /**
     * 根据需求编号获取飞行需求
     *
     * @param demandNo 需求编号
     * @return 飞行需求信息
     */
    FlightDemandDTO getFlightDemandByNo(String demandNo);

    /**
     * 根据合并需求编号获取合并需求区域列表
     *
     * @param mergeDemandNo 合并需求编号
     * @return 合并需求区域列表
     */
    List<FlightDemandAreaDTO> queryMergeDemandAreaList(String mergeDemandNo);

    /**
     * 根据原始需求编号获取原始需求区域列表
     *
     * @param originDemandNo 原始需求编号
     * @return 原始需求区域列表
     */
    List<FlightDemandAreaDTO> queryOriginDemandAreaList(String originDemandNo);

    /**
     * 分页查询飞行需求，只有原始需求
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    CommonPage<FlightDemandDTO> pageQueryFlightDemandCustomerManage(FlightDemandQueryDTO queryDTO);

    /**
     * 运营管理平台-分页查询飞行需求，包含未合并的原始需求和合并需求
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    CommonPage<FlightDemandDTO> pageQueryFlightDemandOpManage(FlightDemandQueryDTO queryDTO);

    /**
     * 根据计划ID获取飞行需求
     *
     * @param planId 计划ID
     * @return 飞行需求信息
     */
    FlightDemandDTO getFlightDemandByPlanId(String planId);

    /**
     * 创建合并需求
     *
     * @param createDTOList 创建合并需求DTO
     * @return 合并需求编号
     */
    String createMergeDemand(List<MergeDemandCreateDTO> createDTOList);

    /**
     * 合并建议列表，分页查询合并处理分组统计
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    MergeHandleGroupAdvice queryMergeHandleGroups(MergeHandleQueryDTO queryDTO);

    /**
     * 根据合并处理编号获取详情
     *
     * @param mergeHandleCode 合并处理编号
     * @return 合并处理详情
     */
    MergeHandleDetailDTO getMergeHandleDetail(String mergeHandleCode);

    /**
     * 执行合并操作
     *
     * @param executeMergeDTO 执行合并参数
     * @return 合并需求编号
     */
    String executeMerge(ExecuteMergeDTO executeMergeDTO);

    /**
     * 取消合并操作
     *
     * @param cancelMergeDTO 取消合并参数
     * @return 操作结果
     */
    Boolean cancelMerge(CancelMergeDTO cancelMergeDTO);

    /**
     * 分配服务商
     *
     * @param dto 分配服务商信息
     */
    void assignServiceProvider(FlightDemandAssignDTO dto);

    /**
     * 检查服务商和需求是否匹配
     *
     * @param demandCode
     * @param serviceProviderNo
     * @return
     */
    Boolean checkMatchServiceProvider(String demandCode, String serviceProviderNo);

    /**
     * 更新服务商匹配状态
     *
     * @param demandCode 需求编号
     * @param isMatchSuccess 匹配状态，匹配成功或者失败
     */
    void updateDemandMatchStatus(String demandCode, boolean isMatchSuccess);

    /**
     * 同步需求到服务商
     *
     * @param demandCode 需求编号
     */
    void syncDemandToProvider(String demandCode, String providerNo);

    FlightDemandStatsDTO get90DayStatistics();

    FlightDemandStatsDTO getStatistics(FlightDemandStatCondition condition);

    List<FlightDemandDayStatsDTO> get7DayStatistics(FlightDemandStatCondition condition);
    /**
     * 根据需求编号查询匹配的服务商列表
     *
     * @param demandCode 需求编号
     * @return 匹配的服务商列表
     */
    List<FlightDemandMatchServiceProviderDTO> queryMatchServiceProviderListByDemandCode(String demandCode);

    /**
     * 批量查询需求匹配的服务商列表
     *
     * @param demandCodes 需求编号列表
     * @return 返回Map，key为需求编号，value为该需求匹配的服务商列表
     */
    Map<String, List<FlightDemandMatchServiceProviderDTO>> queryMatchServiceProviderListByDemandCodes(List<String> demandCodes);

    /**
     * 需求匹配服务商并同步需求
     *
     * @param demandDTO
     */
    void matchAndSyncDemand(FlightDemandDTO demandDTO);

    /**
     * 校验需求是否允许编辑
     *
     * @param demandNo 需求编号
     * @param editUserNo   编辑人用户编号
     */
    void validateAllowEditDemand(String demandNo, String editUserNo);

    /**
     * 编辑需求
     * @param demandDTO
     */
    String editDemand(FlightDemandDTO demandDTO);

    /**
     * 查询所有历史需求的编号，每编辑一次就会生成一个新的需求
     *
     * @param demandCode
     * @return
     */
    List<String> findAllHistoryDemandCode(String demandCode);

    /**
     * 审批需求
     * @param approvalEntity
     */
    void approveDemand(ApprovalEntity approvalEntity);

    /**
     * 删除需求
     * @param demandCode
     */
    void deleteDemand(String demandCode);

    /**
     * 终止需求
     * @param demandCode
     */
    void stopDemand(String demandCode);


    /**
     * 根据需求编号列表获取需求信息
     * @param demandNoList
     * @return
     */
    List<FlightDemandVO> getFlightDemandByNoList(List<String> demandNoList);
}