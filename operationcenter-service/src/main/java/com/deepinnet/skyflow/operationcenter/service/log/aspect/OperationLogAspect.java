package com.deepinnet.skyflow.operationcenter.service.log.aspect;

import cn.hutool.core.net.*;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogContext;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogContextHolder;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogService;
import com.deepinnet.skyflow.operationcenter.service.log.annotation.OperationLog;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 操作日志切面
 * 用于自动记录标记了@OperationLog注解的方法的操作日志
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Order(1)
public class OperationLogAspect {

    @Resource
    private OperationLogService operationLogService;

    /**
     * 定义切点：所有标记了@OperationLog注解的方法
     */
    @Pointcut("@annotation(com.deepinnet.skyflow.operationcenter.service.log.annotation.OperationLog)")
    public void operationLogPointcut() {
    }

    /**
     * 环绕通知：记录操作日志
     */
    @Around("operationLogPointcut() && @annotation(operationLog)")
    public Object around(ProceedingJoinPoint joinPoint, OperationLog operationLog) throws Throwable {
        OperationLogContext context;

        try {
            // 执行目标方法
            Object result = joinPoint.proceed();

            // 获取请求信息
            HttpServletRequest request = getHttpServletRequest();
            if (request == null) {
                return result;
            }

            String requestUri = request.getRequestURI();
            String requestMethod = request.getMethod();
            String ipAddress = ServletUtil.getClientIP(request);

            String userAgent = request.getHeader("User-Agent");

            // 获取或创建操作日志上下文
            if (operationLog.useTemplate()) {
                // 使用注解指定的模板
                context = new OperationLogContext(operationLog.template());
                OperationLogContextHolder.setContext(context);
            } else {
                // 从上下文获取
                context = OperationLogContextHolder.getContext();
            }

            // 如果上下文存在，记录成功日志
            // 这里service里面catch了异常，是不会报错的
            if (context != null && context.isEnabled()) {
                context.setSuccess();
                operationLogService.recordOperationLog(context, requestUri, requestMethod, ipAddress, userAgent);
            }

            return result;
        } finally {
            // 清理上下文
            OperationLogContextHolder.clearContext();
        }
    }

    /**
     * 获取HttpServletRequest
     */
    private HttpServletRequest getHttpServletRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            LogUtil.warn("获取HttpServletRequest失败", e);
            return null;
        }
    }
}
