package com.deepinnet.skyflow.operationcenter.service.log.aspect;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.net.*;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogContext;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogContextHolder;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogService;
import com.deepinnet.skyflow.operationcenter.service.log.RequestParamsUtil;
import com.deepinnet.skyflow.operationcenter.service.log.annotation.OperationLog;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志切面
 * 用于自动记录标记了@OperationLog注解的方法的操作日志
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Order(1)
public class OperationLogAspect {

    @Resource
    private OperationLogService operationLogService;

    /**
     * 定义切点：所有标记了@OperationLog注解的方法
     */
    @Pointcut("@annotation(com.deepinnet.skyflow.operationcenter.service.log.annotation.OperationLog)")
    public void operationLogPointcut() {
    }

    /**
     * 环绕通知：记录操作日志
     */
    @Around("operationLogPointcut() && @annotation(operationLog)")
    public Object around(ProceedingJoinPoint joinPoint, OperationLog operationLog) throws Throwable {
        OperationLogContext context;

        try {
            // 执行目标方法
            Object result = joinPoint.proceed();

            // 获取请求信息
            HttpServletRequest request = getHttpServletRequest();
            if (request == null) {
                return result;
            }

            String requestUri = request.getRequestURI();
            String requestMethod = request.getMethod();
            String ipAddress = ServletUtil.getClientIP(request);

            String userAgent = request.getHeader("User-Agent");

            // 获取或创建操作日志上下文
            if (operationLog.useTemplate()) {
                // 使用注解指定的模板
                context = new OperationLogContext(operationLog.template());
                OperationLogContextHolder.setContext(context);
            } else {
                // 从上下文获取
                context = OperationLogContextHolder.getContext();
            }

            // 如果注解配置了记录请求参数，则自动捕获
            if (operationLog.logParams() && context != null) {
                captureRequestParams(joinPoint, context);
            }

            // 超管不记录操作日志
            Object isSuperAdmin = StpUtil.getExtra("isSuperAdmin");
            if (isSuperAdmin != null && (Boolean) isSuperAdmin) {
                return result;
            }

            // 如果上下文存在，记录成功日志
            // 这里service里面catch了异常，是不会报错的
            if (context != null && context.isEnabled()) {
                context.setSuccess();
                operationLogService.recordOperationLog(context, requestUri, requestMethod, ipAddress, userAgent);
            }

            return result;
        } finally {
            // 清理上下文
            OperationLogContextHolder.clearContext();
        }
    }

    /**
     * 获取HttpServletRequest
     */
    private HttpServletRequest getHttpServletRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            LogUtil.warn("获取HttpServletRequest失败", e);
            return null;
        }
    }

    /**
     * 捕获请求参数
     */
    private void captureRequestParams(ProceedingJoinPoint joinPoint, OperationLogContext context) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                // 如果只有一个参数，直接记录
                if (args.length == 1) {
                    String jsonParams = RequestParamsUtil.limitLength(RequestParamsUtil.toJsonString(args[0]));
                    context.setRequestParams(jsonParams);
                } else {
                    // 多个参数时，构建参数映射
                    String[] paramNames = getParameterNames(joinPoint);
                    Map<String, Object> paramMap = new HashMap<>();

                    for (int i = 0; i < args.length; i++) {
                        String paramName = (paramNames != null && i < paramNames.length)
                            ? paramNames[i] : "param" + i;
                        paramMap.put(paramName, args[i]);
                    }

                    String jsonParams = RequestParamsUtil.limitLength(RequestParamsUtil.toJsonString(paramMap));
                    context.setRequestParams(jsonParams);
                }
            } else {
                // 没有参数时设置为null
                context.setRequestParams(null);
            }
        } catch (Exception e) {
            LogUtil.warn("捕获请求参数失败: {}", e.getMessage());
            context.setRequestParams(null);
        }
    }

    /**
     * 获取方法参数名（简化版本，实际项目中可以使用更复杂的反射获取）
     */
    private String[] getParameterNames(ProceedingJoinPoint joinPoint) {
        try {
            // 这里简化处理，实际可以通过反射获取真实参数名
            Object[] args = joinPoint.getArgs();
            String[] paramNames = new String[args.length];
            for (int i = 0; i < args.length; i++) {
                paramNames[i] = "param" + i;
            }
            return paramNames;
        } catch (Exception e) {
            return null;
        }
    }
}
