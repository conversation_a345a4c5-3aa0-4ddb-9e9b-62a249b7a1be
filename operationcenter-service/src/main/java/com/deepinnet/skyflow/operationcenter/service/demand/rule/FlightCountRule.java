package com.deepinnet.skyflow.operationcenter.service.demand.rule;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.RoutineInspectionFlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Component
public class FlightCountRule extends AbstractDemandMergeRule {

    @Override
    protected DemandMergeRuleResult execute(RuleContext<DemandMergeRuleResult> context) {
        DemandMergeRuleResult previousRuleExecResult = context.getPreviousRuleExecResult();
        List<List<FlightDemandDTO>> fromFilterDemandGroup = previousRuleExecResult.getFilterDemandGroup();
        
        List<List<FlightDemandDTO>> toFilterDemandGroup = new ArrayList<>();
        
        // 遍历每个需求分组
        for (List<FlightDemandDTO> demandGroup : fromFilterDemandGroup) {
            if (CollectionUtils.isEmpty(demandGroup) || demandGroup.size() < 2) {
                // 单个需求或空分组不需要增量服务规则处理
                continue;
            }
            
            // 按飞行次数进行分组（飞行次数必须完全一致才能合并）
            Map<String, List<FlightDemandDTO>> flightCountGroupMap = demandGroup.stream()
                .collect(Collectors.groupingBy(this::getFlightCount));
            
            // 只保留包含2个或以上需求的分组
            for (List<FlightDemandDTO> flightCountGroup : flightCountGroupMap.values()) {
                if (flightCountGroup.size() >= 2) {
                    toFilterDemandGroup.add(flightCountGroup);
                }
            }
        }

        DemandMergeRuleResult ruleResult = new DemandMergeRuleResult();
        ruleResult.setFilterDemandGroup(toFilterDemandGroup);
        return ruleResult;
    }

    @Override
    public int order() {
        return 200;
    }
    
    /**
     * 获取需求的增值服务标识key，用于分组
     * 相同key的需求具有相同的增值服务，可以合并
     * @param demand 需求对象
     * @return 增值服务标识key
     */
    private String getFlightCount(FlightDemandDTO demand) {
        RoutineInspectionFlightDemandDTO routineInspectionDetail = demand.getRoutineInspectionDetail();
        if (routineInspectionDetail == null) {
            throw new IllegalArgumentException("暂时不支持除日常巡检以外的需求类型");
        }
        Integer flyingNum = routineInspectionDetail.getFlyingNum();
        if (flyingNum == null) {
            // 默认为1
            return "1";
        }
        return flyingNum.toString();
    }

}
