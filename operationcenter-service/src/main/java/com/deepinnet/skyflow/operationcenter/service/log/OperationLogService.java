package com.deepinnet.skyflow.operationcenter.service.log;

/**
 * 操作日志服务接口
 *
 * <AUTHOR>
 */
public interface OperationLogService {

    /**
     * 记录操作日志
     *
     * @param context 操作日志上下文
     * @param requestUri 请求URI
     * @param requestMethod 请求方法
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     */
    void recordOperationLog(OperationLogContext context, String requestUri, String requestMethod, 
                           String ipAddress, String userAgent);

    /**
     * 渲染操作日志模板
     *
     * @param context 操作日志上下文
     * @return 渲染后的操作详情
     */
    String renderTemplate(OperationLogContext context);
}
