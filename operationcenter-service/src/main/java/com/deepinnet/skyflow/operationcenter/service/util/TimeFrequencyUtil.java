package com.deepinnet.skyflow.operationcenter.service.util;

import com.deepinnet.skyflow.operationcenter.enums.FlyingFrequencyEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.Set;

/**
 * Description:
 * Date: 2025/6/19
 * Author: lijunheng
 */
public class TimeFrequencyUtil {

    /**
     * 获取频率对应的工作日集合
     *
     * @param frequency 频率
     * @return 工作日集合
     */
    public static Set<DayOfWeek> getDayOfWeekSetFromFrequency(FlyingFrequencyEnum frequency) {
        Set<DayOfWeek> repeatDays = new HashSet<>();
        switch (frequency) {
            case DAILY:
                repeatDays.addAll(EnumSet.allOf(DayOfWeek.class));
                break;

            case WEEKLY_MONDAY:
                repeatDays.add(DayOfWeek.MONDAY);
                break;

            case WEEKLY_TUESDAY:
                repeatDays.add(DayOfWeek.TUESDAY);
                break;

            case WEEKLY_WEDNESDAY:
                repeatDays.add(DayOfWeek.WEDNESDAY);
                break;

            case WEEKLY_THURSDAY:
                repeatDays.add(DayOfWeek.THURSDAY);
                break;

            case WEEKLY_FRIDAY:
                repeatDays.add(DayOfWeek.FRIDAY);
                break;

            case WEEKLY_SATURDAY:
                repeatDays.add(DayOfWeek.SATURDAY);
                break;

            case WEEKLY_SUNDAY:
                repeatDays.add(DayOfWeek.SUNDAY);
                break;

            default:
                break;
        }
        return repeatDays;
    }

    /**
     * 获取指定时间段内符合条件的日期天数
     *
     * @param start
     * @param end
     * @param targetDayList
     * @return
     */
    public static int countWeekdaysInRange(LocalDate start, LocalDate end, Set<DayOfWeek> targetDayList) {
        if (start == null || end == null || start.isAfter(end) || CollectionUtils.isEmpty(targetDayList)) {
            return 0;
        }

        int count = 0;
        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            if (targetDayList.contains(date.getDayOfWeek())) {
                count++;
            }
        }
        return count;
    }

    /**
     * 校验距离需求的最早起飞时间是否超过72小时，超过就返回true
     *
     * 按今日晚0点开始到需求最早日的早上0点计算是否大于72小时
     *
     * @param date
     * @return
     */
    public static boolean moreThanHourBeforeTime(LocalDate date, int hours) {
        LocalDateTime zeroTime = LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime timeThreshold = zeroTime.plusHours(hours); // 72小时后
        return !date.atStartOfDay().isBefore(timeThreshold);
    }
}
