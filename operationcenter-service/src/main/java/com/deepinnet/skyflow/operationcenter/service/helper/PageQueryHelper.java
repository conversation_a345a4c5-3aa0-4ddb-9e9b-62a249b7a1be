package com.deepinnet.skyflow.operationcenter.service.helper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.PageQueryDTO;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * Creator zengjuerui
 * Date 2025-05-21
 **/
public class PageQueryHelper {

    public static <R, T> CommonPage<R> pageQuery(IService<T> iService, Function<List<T>, List<R>> convert, PageQueryDTO pageQuery,
                                                 Wrapper<T> queryWrapper) {


        IPage<T> iPage = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        iPage = iService.page(iPage, queryWrapper);

        if(iPage.getTotal() == 0) {
            return CommonPage.buildPage(pageQuery.getPageNum(),
                    pageQuery.getPageSize(),
                    0,
                    0L,
                    Collections.emptyList());
        }

        List<R> res = convert.apply(iPage.getRecords());

        return CommonPage.buildPage(pageQuery.getPageNum(),
                pageQuery.getPageSize(),
                (int) iPage.getPages(),
                iPage.getTotal(),
                res);
    }
}
