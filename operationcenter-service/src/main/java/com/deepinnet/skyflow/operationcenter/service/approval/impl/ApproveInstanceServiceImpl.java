package com.deepinnet.skyflow.operationcenter.service.approval.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.infra.api.dto.SimpleDepartmentDTO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalInstanceDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalNodeDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalStepDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.ApprovalInstanceRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.ApprovalNodeRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.ApprovalStepRepository;
import com.deepinnet.skyflow.operationcenter.service.approval.ApprovalCallback;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveInstanceService;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.*;
import com.deepinnet.skyflow.operationcenter.service.approval.instance.ApproveConvert;
import com.deepinnet.skyflow.operationcenter.service.approval.instance.CallbackRegistry;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2025/7/11
 * Author: lijunheng
 */
@Service
public class ApproveInstanceServiceImpl implements ApproveInstanceService {

    private static final String APPROVE_ID = "APPROVE";

    @Resource
    private ApprovalInstanceRepository approvalInstanceRepository;

    @Resource
    private ApprovalStepRepository approvalStepRepository;

    @Resource
    private ApprovalNodeRepository approvalNodeRepository;

    @Resource
    private ApproveConvert approveConvert;

    @Resource
    private CallbackRegistry callbackRegistry;

    @Resource
    private ApproveHelper approveHelper;

    @Resource
    private UserRemoteClient userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createApprove(ApprovalInstance instance) {
        String approvalId = IdGenerateUtil.getId(APPROVE_ID);
        instance.setApprovalId(approvalId);
        instance.setApplyTime(LocalDateTime.now());
        instance.setStatus(ApproveStatusEnum.PENDING);
        instance.setCurrentStep(0);
        //保存审批实例
        ApprovalInstanceDO instanceDO = approveConvert.toInstanceDO(instance);
        approvalInstanceRepository.save(instanceDO);

        //保存步骤
        List<ApprovalStepDO> stepDOList = new ArrayList<>();
        List<List<ApprovalNodeDO>> nodeDOListList = new ArrayList<>();
        List<ApprovalStep> stepList = instance.getSteps();
        for (int i = 0; i < stepList.size(); i++) {
            ApprovalStep step = stepList.get(i);
            step.setStatus(ApproveStatusEnum.PENDING);
            ApprovalStepDO stepDO = approveConvert.toStepDO(step);
            stepDO.setApprovalId(approvalId);
            stepDO.setStepOrder(i);
            stepDO.setTenantId(instance.getTenantId());
            stepDOList.add(stepDO);

            List<ApprovalNodeDO> nodeDOList = new ArrayList<>();
            for (ApprovalNode node : step.getApprovalNodeList()) {
                node.setStatus(ApproveStatusEnum.PENDING);
                ApprovalNodeDO nodeDO = approveConvert.toNodeDO(node);
                nodeDO.setApprovalId(approvalId);
                nodeDO.setStepOrder(i);
                nodeDO.setTenantId(instance.getTenantId());
                nodeDOList.add(nodeDO);
            }
            nodeDOListList.add(nodeDOList);
        }
        if (CollectionUtil.isNotEmpty(stepDOList)) {
            approvalStepRepository.saveBatch(stepDOList);
        }

        //保存节点
        List<ApprovalNodeDO> nodeDOList = nodeDOListList.stream().flatMap(List::stream).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(nodeDOList)) {
            approvalNodeRepository.saveBatch(nodeDOList);
        }

        // 处理自动审批逻辑
        processAutoApprovalForNextStep(approvalId, 0);
        
        return approvalId;
    }

    @Override
    public String createAutoPassApprove(ApproveSubmitEntity submitEntity) {
        ApprovalNode approvalNode = approveHelper.createApprovalNode(ApproveHelper.SYSTEM);
        approvalNode.setAutoPass(true);

        ApprovalStep step = new ApprovalStep();
        step.setApprovalNodeList(ListUtil.toList(approvalNode));
        step.setApproveMode(ApproveModeEnum.ALL);

        ApprovalInstance instance = new ApprovalInstance();
        instance.setBizType(submitEntity.getBizType());
        instance.setBizId(submitEntity.getBizId());
        instance.setTenantId(submitEntity.getTenantId());
        instance.setCallbackParams(submitEntity.getCallbackParams());

        SimpleDepartmentDTO simpleDepartmentDTO = userService.getUserRelatedDepartment(submitEntity.getSubmitUserId());
        instance.setSubmitUserId(submitEntity.getSubmitUserId());
        instance.setSubmitUserName(simpleDepartmentDTO.getUserName());
        instance.setSubmitDepartmentId(String.valueOf(simpleDepartmentDTO.getId()));
        instance.setSubmitDepartmentName(simpleDepartmentDTO.getName());
        instance.setSubmitUserPhone(simpleDepartmentDTO.getMemberPhone());

        instance.setSteps(ListUtil.toList(step));

        return createApprove(instance);
    }



    @Override
    public void stopApprove(String approvalId) {
        approvalInstanceRepository.update(Wrappers.lambdaUpdate(ApprovalInstanceDO.class)
                .set(ApprovalInstanceDO::getStatus, ApproveStatusEnum.CANCELED.name())
                .set(ApprovalInstanceDO::getApproveEndTime, LocalDateTime.now())
                .eq(ApprovalInstanceDO::getApprovalId, approvalId)
        );
    }

    @Override
    public ApprovalInstance getApprovalInstance(String approvalId) {
        //查询审批实例
        ApprovalInstanceDO instanceDO = approvalInstanceRepository.getOne(Wrappers.lambdaQuery(ApprovalInstanceDO.class)
                .eq(ApprovalInstanceDO::getApprovalId, approvalId));
        if (instanceDO == null) {
            return null;
        }
        List<ApprovalInstance> instanceList = buildInstanceModel(ListUtil.toList(instanceDO));
        if (CollectionUtil.isEmpty(instanceList)) {
            return null;
        }
        return instanceList.get(0);
    }

    @Override
    public List<ApprovalInstance> getApprovalInstanceListByApprovalIdList(List<String> approvalIdList) {
        if (CollectionUtil.isEmpty(approvalIdList)) {
            return new ArrayList<>();
        }
        List<ApprovalInstanceDO> instanceDOList = approvalInstanceRepository.lambdaQuery()
                .in(ApprovalInstanceDO::getApprovalId, approvalIdList)
                .orderByAsc(ApprovalInstanceDO::getApplyTime)
                .list();
        return buildInstanceModel(instanceDOList);
    }

    @Override
    public List<ApprovalInstance> getApprovalInstanceListByBizIdList(List<String> bizIdList) {
        if (CollectionUtil.isEmpty(bizIdList)) {
            return new ArrayList<>();
        }
        
        // 查询审批实例，按申请时间排序
        List<ApprovalInstanceDO> instanceDOList = approvalInstanceRepository.lambdaQuery()
                .in(ApprovalInstanceDO::getBizId, bizIdList)
                .orderByAsc(ApprovalInstanceDO::getApplyTime)
                .list();
        return buildInstanceModel(instanceDOList);
    }

    private List<ApprovalInstance> buildInstanceModel(List<ApprovalInstanceDO> instanceDOList) {
        if (CollectionUtil.isEmpty(instanceDOList)) {
            return new ArrayList<>();
        }

        List<String> approvalIdList = instanceDOList.stream()
                .map(ApprovalInstanceDO::getApprovalId)
                .collect(Collectors.toList());

        // 查询审批步骤
        List<ApprovalStepDO> stepDOList = approvalStepRepository.lambdaQuery()
                .in(ApprovalStepDO::getApprovalId, approvalIdList)
                .orderByAsc(ApprovalStepDO::getStepOrder)
                .list();
        Map<String, List<ApprovalStepDO>> stepMap = stepDOList.stream()
                .collect(Collectors.groupingBy(ApprovalStepDO::getApprovalId));

        // 查询审批节点
        List<ApprovalNodeDO> nodeDOList = approvalNodeRepository.lambdaQuery()
                .in(ApprovalNodeDO::getApprovalId, approvalIdList)
                .orderByAsc(ApprovalNodeDO::getStepOrder)
                .list();
        Map<String, Map<Integer, List<ApprovalNodeDO>>> nodeMap = nodeDOList.stream()
                .collect(Collectors.groupingBy(ApprovalNodeDO::getApprovalId,
                        Collectors.groupingBy(ApprovalNodeDO::getStepOrder)));

        // 组装完整的审批实例列表
        List<ApprovalInstance> instanceList = new ArrayList<>();
        for (ApprovalInstanceDO instanceDO : instanceDOList) {
            ApprovalInstance instance = approveConvert.toInstance(instanceDO);

            // 组装审批步骤
            List<ApprovalStepDO> steps = stepMap.get(instanceDO.getApprovalId());
            if (CollectionUtil.isNotEmpty(steps)) {
                List<ApprovalStep> stepList = steps.stream()
                        .map(approveConvert::toStep)
                        .collect(Collectors.toList());
                instance.setSteps(stepList);

                // 组装每个步骤的审批节点
                Map<Integer, List<ApprovalNodeDO>> stepNodeMap = nodeMap.get(instanceDO.getApprovalId());
                if (stepNodeMap != null) {
                    for (ApprovalStep step : stepList) {
                        List<ApprovalNodeDO> nodes = stepNodeMap.get(step.getStepOrder());
                        if (CollectionUtil.isNotEmpty(nodes)) {
                            List<ApprovalNode> approvalNodes = nodes.stream()
                                    .map(approveConvert::toNode)
                                    .collect(Collectors.toList());
                            step.setApprovalNodeList(approvalNodes);
                        }
                    }
                }
            }

            instanceList.add(instance);
        }

        return instanceList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApproveStatusEnum doApprove(ApprovalEntity approvalEntity) {
        String approvalId = approvalEntity.getApprovalId();
        if (StringUtils.isBlank(approvalId)) {
            approvalId = findPendingApprovalIdByBizId(approvalEntity.getBizId(), approvalEntity.getBizType(), approvalEntity.getTenantId());
        }
        ApprovalInstance instance = getApprovalInstance(approvalId);
        if (instance == null) {
            throw new RuntimeException("审批实例不存在");
        }

        ApproveStatusEnum approveInstanceStatus = instance.getStatus();
        if (approveInstanceStatus != ApproveStatusEnum.PENDING && approveInstanceStatus != ApproveStatusEnum.APPROVING) {
            throw new RuntimeException("审批单已经被终止，不允许操作");
        }

        ApprovalStep step = instance.getSteps().get(instance.getCurrentStep());

        ApprovalNode approvalNode = step.getApprovalNodeList().stream()
                .filter(a -> Objects.equals(a.getApproveUserId(), approvalEntity.getApproveUserId()))
                .findFirst().orElseThrow(() -> new RuntimeException("你不是当前节点的审批人"));

        if (ApproveStatusEnum.PENDING != approvalNode.getStatus()) throw new RuntimeException("你已经审批过当前节点");

        approvalNode.setStatus(approvalEntity.isApproved() ? ApproveStatusEnum.APPROVED : ApproveStatusEnum.REJECTED);
        approvalNode.setApprovalTime(LocalDateTime.now());
        approvalNode.setRemark(approvalEntity.getRemark());

        //更新审批节点
        ApprovalNodeDO nodeDO = approveConvert.toNodeDO(approvalNode);
        approvalNodeRepository.updateById(nodeDO);

        //计算并且更新当前步骤的审批状态，并且判断是否需要自动执行下一步
        updateStepStatusAndAutoExecNext(step, instance);

        ApprovalInstanceDO currentInstanceDO = approvalInstanceRepository.getOne(Wrappers.lambdaQuery(ApprovalInstanceDO.class)
                .eq(ApprovalInstanceDO::getApprovalId, instance.getApprovalId()));

        return ApproveStatusEnum.getEnumByStatusName(currentInstanceDO.getStatus());
    }

    private void updateStepStatusAndAutoExecNext(ApprovalStep currentStepObj, ApprovalInstance instance) {
        // 判断当前步骤的审批状态
        ApproveStatusEnum stepStatus = determineStepStatus(currentStepObj);

        // 更新步骤状态
        if (stepStatus != ApproveStatusEnum.PENDING) {
            currentStepObj.setStatus(stepStatus);
            ApprovalStepDO stepDO = approveConvert.toStepDO(currentStepObj);
            approvalStepRepository.updateById(stepDO);
        }

        // 根据步骤状态更新实例状态
        ApprovalCallback callback = callbackRegistry.get(instance.getBizType());
        ApproveStatusEnum instanceStatus;
        boolean needProcessAutoApprovalForNextStep = false;
        int next = instance.getCurrentStep() + 1;
        if (stepStatus == ApproveStatusEnum.APPROVED) {
            // 当前步骤通过，检查是否是最后一步
            if (next >= instance.getSteps().size()) {
                // 是最后一步，审批流程完成
                instanceStatus = ApproveStatusEnum.APPROVED;
                instance.setApproveEndTime(LocalDateTime.now());
                callback.onApproved(instance.getBizId(), instance.getCallbackParams());
            } else {
                // 不是最后一步，进入下一步
                instance.setCurrentStep(next);
                instanceStatus = ApproveStatusEnum.APPROVING;

                // 需要继续处理下一步的自动审批
                needProcessAutoApprovalForNextStep = true;
            }
        } else if (stepStatus == ApproveStatusEnum.REJECTED) {
            // 当前步骤被拒绝，整个流程结束
            instanceStatus = ApproveStatusEnum.REJECTED;
            instance.setApproveEndTime(LocalDateTime.now());
            callback.onRejected(instance.getBizId(), instance.getCallbackParams());
        } else {
            // 步骤仍在进行中，更新实例状态为审核中
            instanceStatus = ApproveStatusEnum.APPROVING;
        }

        //先更新完当前步骤和实例状态，然后再执行下一步的审批
        instance.setStatus(instanceStatus);
        ApprovalInstanceDO instanceDO = approveConvert.toInstanceDO(instance);
        approvalInstanceRepository.updateById(instanceDO);

        if (needProcessAutoApprovalForNextStep) {
            // 处理下一步的自动审批
            processAutoApprovalForNextStep(instance.getApprovalId(), next);
        }
    }

    /**
     * 处理下一步的自动审批
     * @param approvalId 审批ID
     * @param nextStep 下一步索引
     */
    private void processAutoApprovalForNextStep(String approvalId, int nextStep) {
        ApprovalInstance instance = getApprovalInstance(approvalId);
        if (instance == null || nextStep >= instance.getSteps().size()) {
            return;
        }

        ApprovalStep nextStepObj = instance.getSteps().get(nextStep);
        
        // 检查这一步是否有自动审批节点
        List<ApprovalNode> autoNodes = nextStepObj.getApprovalNodeList().stream()
                .filter(node -> node.getStatus() == ApproveStatusEnum.PENDING && 
                        (Boolean.TRUE.equals(node.getAutoPass()) || Boolean.TRUE.equals(node.getAutoReject())))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(autoNodes)) {
            // 没有自动审批节点，直接返回
            return;
        }

        // 执行自动审批
        for (ApprovalNode autoNode : autoNodes) {
            boolean isAutoPass = Boolean.TRUE.equals(autoNode.getAutoPass());
            autoNode.setStatus(isAutoPass ? ApproveStatusEnum.APPROVED : ApproveStatusEnum.REJECTED);
            autoNode.setApprovalTime(LocalDateTime.now());
            autoNode.setRemark(isAutoPass ? "自动通过" : "自动拒绝");

            // 更新审批节点
            ApprovalNodeDO nodeDO = approveConvert.toNodeDO(autoNode);
            approvalNodeRepository.updateById(nodeDO);
        }

        // 执行完自动审批后判断这一步的审批状态，是否需要继续执行下一步审批
        updateStepStatusAndAutoExecNext(nextStepObj, instance);
    }

    /**
     * 根据业务ID查找正在审批中的审批ID，因为一个业务id可能会存在多个审批单
     *
     * @param bizId 业务ID
     * @return 审批ID
     */
    private String findPendingApprovalIdByBizId(String bizId, String bizType, String tenantId) {
        if (StringUtils.isBlank(bizId)) {
            throw new RuntimeException("bizId不能为空");
        }
        return approvalInstanceRepository.lambdaQuery()
                .eq(ApprovalInstanceDO::getBizId, bizId)
                .eq(ApprovalInstanceDO::getBizType, bizType)
                .eq(ApprovalInstanceDO::getTenantId, tenantId)
                .in(ApprovalInstanceDO::getStatus, ListUtil.toList(ApproveStatusEnum.PENDING.name(), ApproveStatusEnum.APPROVING.name()))
                .oneOpt()
                .map(ApprovalInstanceDO::getApprovalId)
                .orElse(null);
    }

    /**
     * 判断当前步骤的审批状态
     *
     * @param step 当前审批步骤
     * @return 步骤的状态
     */
    private ApproveStatusEnum determineStepStatus(ApprovalStep step) {

        // 统计当前步骤中各状态的节点数量
        long approvedCount = step.getApprovalNodeList().stream()
                .filter(node -> node.getStatus() == ApproveStatusEnum.APPROVED)
                .count();

        long rejectedCount = step.getApprovalNodeList().stream()
                .filter(node -> node.getStatus() == ApproveStatusEnum.REJECTED)
                .count();

        // 根据审批模式判断步骤状态
        if (step.getApproveMode() == ApproveModeEnum.ALL) {
            // 全部通过模式：所有节点都通过才算通过
            if (approvedCount == step.getApprovalNodeList().size()) {
                return ApproveStatusEnum.APPROVED;
            } else if (rejectedCount > 0) {
                return ApproveStatusEnum.REJECTED;
            } else {
                return ApproveStatusEnum.APPROVING;
            }
        } else {
            // 任意通过模式：只要有一个节点通过就算通过
            if (approvedCount > 0) {
                return ApproveStatusEnum.APPROVED;
            } else if (rejectedCount == step.getApprovalNodeList().size()) {
                // 全部被拒绝
                return ApproveStatusEnum.REJECTED;
            } else {
                return ApproveStatusEnum.APPROVING;
            }
        }
    }

    @Override
    public List<ApprovalNode> getCurrentNeedToApproveNodeList(String approvalId) {
        Map<String, List<ApprovalNode>> approveNodeMap = getCurrentNeedToApproveNodeMap(ListUtil.toList(approvalId));
        List<ApprovalNode> approvalNodes = approveNodeMap.get(approvalId);
        if (approvalNodes == null) {
            return new ArrayList<>();
        }
        return approvalNodes;
    }

    @Override
    public Map<String, List<ApprovalNode>> getCurrentNeedToApproveNodeMap(List<String> approvalIdList) {
        if (CollectionUtil.isEmpty(approvalIdList)) {
            return new HashMap<>(0);
        }
        //查询审批实例，只删选待处理和审核中的
        List<ApprovalInstanceDO> instanceDOList = approvalInstanceRepository.lambdaQuery()
                .in(ApprovalInstanceDO::getApprovalId, approvalIdList)
                .in(ApprovalInstanceDO::getStatus, ListUtil.toList(ApproveStatusEnum.PENDING.name(), ApproveStatusEnum.APPROVING.name()))
                .list();
        if (CollectionUtil.isEmpty(instanceDOList)) {
            return new HashMap<>(0);
        }

        Map<String, Integer> currentStepMap = instanceDOList.stream()
                .collect(Collectors.toMap(ApprovalInstanceDO::getApprovalId, ApprovalInstanceDO::getCurrentStep));

        //查询当前步骤的审批节点
        List<ApprovalNodeDO> nodeDOList = approvalNodeRepository.lambdaQuery()
                .in(ApprovalNodeDO::getApprovalId, instanceDOList.stream().map(ApprovalInstanceDO::getApprovalId).collect(Collectors.toList()))
                .in(ApprovalNodeDO::getStatus, ApproveStatusEnum.PENDING.name())
                .list();

        //按审批实例分组
        return nodeDOList.stream()
                .filter(node -> Objects.equals(node.getStepOrder(), currentStepMap.get(node.getApprovalId())))
                .map(approveConvert::toNode)
                .collect(Collectors.groupingBy(ApprovalNode::getApprovalId));
    }

    @Override
    public List<ApprovalRecord> getApprovalRecordMap(String bizId) {
        Map<String, List<ApprovalRecord>> approvalRecordMap = getApprovalRecordMap(ListUtil.toList(bizId));
        return approvalRecordMap.getOrDefault(bizId, new ArrayList<>());
    }

    @Override
    public Map<String, List<ApprovalRecord>> getApprovalRecordMap(List<String> bizIdList) {
        //查询审批实例
        List<ApprovalInstanceDO> instanceDOList = approvalInstanceRepository.lambdaQuery()
                .in(ApprovalInstanceDO::getBizId, bizIdList)
                .orderByAsc(ApprovalInstanceDO::getApplyTime)
                .list();
        if (CollectionUtil.isEmpty(instanceDOList)) {
            return new HashMap<>(0);
        }

        //查询所有审批过的节点
        List<ApprovalNodeDO> nodeDOList = approvalNodeRepository.lambdaQuery()
                .in(ApprovalNodeDO::getApprovalId, instanceDOList.stream().map(ApprovalInstanceDO::getApprovalId).collect(Collectors.toList()))
                .ne(ApprovalNodeDO::getStatus, ApproveStatusEnum.PENDING)
                .orderByAsc(ApprovalNodeDO::getApprovalTime)
                .list();

        Map<String, List<ApprovalNodeDO>> approvalIdNodeMap = nodeDOList.stream().collect(Collectors.groupingBy(ApprovalNodeDO::getApprovalId));
        Map<String, List<ApprovalInstanceDO>> bizIdInstanceMap = instanceDOList.stream().collect(Collectors.groupingBy(ApprovalInstanceDO::getBizId));
        Map<String, List<ApprovalRecord>> approvalRecordMap = new HashMap<>();
        bizIdInstanceMap.forEach((bizId, approveInstanceDOList) -> {
            List<ApprovalRecord> recordList = approveInstanceDOList.stream().map(instanceDO -> {
                ApprovalRecord approvalRecord = new ApprovalRecord();
                approvalRecord.setApprovalId(instanceDO.getApprovalId());
                approvalRecord.setSubmitUserId(instanceDO.getSubmitUserId());
                approvalRecord.setSubmitUserName(instanceDO.getSubmitUserName());
                approvalRecord.setSubmitDepartmentId(instanceDO.getSubmitDepartmentId());
                approvalRecord.setSubmitDepartmentName(instanceDO.getSubmitDepartmentName());
                approvalRecord.setSubmitUserPhone(instanceDO.getSubmitUserPhone());
                approvalRecord.setApplyTime(instanceDO.getApplyTime());
                approvalRecord.setStatus(ApproveStatusEnum.getEnumByStatusName(instanceDO.getStatus()));
                approvalRecord.setNodeRecordList(approveConvert.toNodeList(approvalIdNodeMap.getOrDefault(instanceDO.getApprovalId(), new ArrayList<>())));
                return approvalRecord;
            }).collect(Collectors.toList());

            approvalRecordMap.put(bizId, recordList);
        });
        return approvalRecordMap;
    }

    @Override
    public List<ApprovalInstance> getNeedApproveInstanceList(List<String> bizIdList) {
        List<ApprovalInstanceDO> list = approvalInstanceRepository.list(Wrappers.<ApprovalInstanceDO>lambdaQuery()
                .in(ApprovalInstanceDO::getBizId, bizIdList)
                .in(ApprovalInstanceDO::getStatus
                        , List.of(ApproveStatusEnum.PENDING.getStatusCode(), ApproveStatusEnum.APPROVING.getStatusCode())));
        return approveConvert.toInstanceList(list);
    }
}
