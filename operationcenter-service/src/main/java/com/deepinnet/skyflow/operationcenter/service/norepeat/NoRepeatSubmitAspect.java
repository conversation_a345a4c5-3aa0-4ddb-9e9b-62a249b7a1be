package com.deepinnet.skyflow.operationcenter.service.norepeat;

import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyDispatcher;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description:
 * Date: 2025/7/30
 * Author: lijunheng
 */
@Aspect
@Component
public class NoRepeatSubmitAspect {

    @Resource
    private StrategyDispatcher strategyDispatcher;

    @Around("@annotation(noRepeatSubmit)")
    public Object around(ProceedingJoinPoint joinPoint, NoRepeatSubmit noRepeatSubmit) throws Throwable {
        strategyDispatcher.noRepeatSubmitCheck(noRepeatSubmit.mode(), joinPoint, noRepeatSubmit);
        return joinPoint.proceed();
    }
}

