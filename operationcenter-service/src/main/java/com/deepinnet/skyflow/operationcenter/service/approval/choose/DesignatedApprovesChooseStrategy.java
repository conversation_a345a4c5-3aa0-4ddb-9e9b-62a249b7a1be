package com.deepinnet.skyflow.operationcenter.service.approval.choose;

import cn.hutool.core.collection.ListUtil;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalChooseConfigRule;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalNode;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalStep;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApproveChooseStrategyEnum;
import com.deepinnet.skyflow.operationcenter.service.approval.impl.ApproveHelper;
import com.deepinnet.skyflow.operationcenter.service.strategy.Strategy;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyContext;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 解析规则获取指定审批人
 */
@Component
public class DesignatedApprovesChooseStrategy implements Strategy {

    @Resource
    private ApproveHelper approveHelper;

    @Override
    public boolean supports(StrategyTypeEnum strategyType, Enum<?> bizEnum) {
        return strategyType == StrategyTypeEnum.CHOOSE_APPROVE && bizEnum == ApproveChooseStrategyEnum.DESIGNATED_APPROVES;
    }

    @Override
    public List<ApprovalStep> execute(StrategyContext context) {
        ApprovalStep step = createDesignatedApprovesApprovalStep(context.getFirstArg(), context.getSecondArg());
        return ListUtil.toList(step);
    }

    private ApprovalStep createDesignatedApprovesApprovalStep(ApprovalChooseConfigRule chooseConfig, String submitUserId) {
        ApprovalStep step = new ApprovalStep();
        step.setApproveMode(chooseConfig.getApproveMode());
        List<ApprovalNode> approvalNodes = getDesignatedApproves(chooseConfig);
        step.setApprovalNodeList(approvalNodes);
        return step;
    }

    private List<ApprovalNode> getDesignatedApproves(ApprovalChooseConfigRule chooseConfig) {
        //获取指定审批人
        List<String> approveUserIdList = JsonUtil.parseToList(chooseConfig.getApproveChooseConfigJson(), String.class);
        return approveUserIdList.stream().map(approveUserId -> approveHelper.createApprovalNode(approveUserId)).collect(Collectors.toList());
    }
}
