package com.deepinnet.skyflow.operationcenter.service.norepeat;

import com.deepinnet.skyflow.operationcenter.service.norepeat.resolve.BizKeyResolver;
import com.deepinnet.skyflow.operationcenter.service.norepeat.resolve.ReqHeaderParamBizKeyResolver;
import com.deepinnet.skyflow.operationcenter.service.norepeat.resolve.TenantIdBizKeyResolver;
import com.deepinnet.skyflow.operationcenter.service.norepeat.resolve.UserIdBizKeyResolver;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizKeyType {

    QUERY_USER_ID("QUERY_USER_ID", "查询登录用户id", UserIdBizKeyResolver.class),
    QUERY_TENANT_ID("QUERY_TENANT_ID", "查询登录用户租户id", TenantIdBizKeyResolver.class),
    QUERY_REQ_HEADER_PARAM("QUERY_REQ_HEADER_PARAM", "查询请求头参数", ReqHeaderParamBizKeyResolver.class),
    ;

    private final String name;

    private final String desc;

    private final Class<? extends BizKeyResolver> resolver;
}
