package com.deepinnet.skyflow.operationcenter.service.client;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson2.JSONObject;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.DepartmentClient;
import com.deepinnet.infra.api.client.UserClient;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/10
 */
@Component
public class UserRemoteClient {

    @Resource
    private UserClient userClient;

    @Resource
    private DepartmentClient departmentClient;

    public List<UserDetailDTO> batchQueryUser(List<String> userNos) {
        UserQueryDTO userQueryDTO = new UserQueryDTO();
        userQueryDTO.setUserNos(userNos);

        Result<List<UserDetailDTO>> userInfoByUserNo = userClient.getUserDetailList(userQueryDTO);

        if (!userInfoByUserNo.isSuccess()) {
            LogUtil.error("UserRemoteClient.batchQueryUser: userInfoByUserNo = {}", userNos);
            throw new BizException(userInfoByUserNo.getErrorCode(), userInfoByUserNo.getErrorDesc());
        }

        return userInfoByUserNo.getData();
    }

    public DataAccessDTO getAvailableQueryData() {
        Result<DataAccessDTO> availableQueryData = userClient.getAvailableQueryData();

        if (!availableQueryData.isSuccess()) {
            LogUtil.error("UserRemoteClient.getAvailableQueryData: availableQueryData = {}", availableQueryData);
            throw new BizException(availableQueryData.getErrorCode(), availableQueryData.getErrorDesc());
        }

        return availableQueryData.getData();
    }

    public List<UserApprovalStatusDTO> getUserApprovalStatus(List<String> userNos) {
        QueryUserApprovalStatusDTO queryUserApprovalStatusDTO = new QueryUserApprovalStatusDTO();
        queryUserApprovalStatusDTO.setUserNos(userNos);
        Result<List<UserApprovalStatusDTO>> userApprovalStatus = userClient.getUserApprovalStatus(queryUserApprovalStatusDTO);
        if (!userApprovalStatus.isSuccess()) {
            LogUtil.error("UserRemoteClient.getUserApprovalStatus: userApprovalStatus = {}", userApprovalStatus);
            throw new BizException(userApprovalStatus.getErrorCode(), userApprovalStatus.getErrorDesc());
        }

        return userApprovalStatus.getData();
    }

    public List<UserDetailDTO> getUserDetailListWithoutTenantId(List<String> userNos) {
        UserQueryDTO queryDTO = new UserQueryDTO();
        queryDTO.setUserNos(userNos);
        Result<List<UserDetailDTO>> result = userClient.getUserDetailListWithoutTenantId(queryDTO);
        if (!result.isSuccess()) {
            LogUtil.error("UserRemoteClient.getUserDetailListWithoutTenantId: result = {}", result);
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }

    public UserInfoDTO getUserInfo() {
        Result<UserInfoDTO> adminInfo = userClient.getAdminInfo();

        if (!adminInfo.isSuccess()) {
            LogUtil.error("UserRemoteClient.getUserInfo: result = {}", adminInfo);
            throw new BizException(adminInfo.getErrorCode(), adminInfo.getErrorDesc());
        }

        return adminInfo.getData();
    }

    public List<SimpleUserInfoDTO> getAllUserInfoWithoutTenantId(QueryUserInfoDTO queryUserInfoDTO) {
        Result<List<SimpleUserInfoDTO>> result = userClient.getSimpleUserInfoWithoutTenantId(queryUserInfoDTO);

        if (!result.isSuccess()) {
            LogUtil.error("UserRemoteClient.getAllUserInfoWithoutTenantId: result = {}", result);
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }

    public List<SimpleDepartmentDTO> getUserRelatedDepartment(QueryUserRelatedDepartmentDTO queryDTO) {
        Result<List<SimpleDepartmentDTO>> result = userClient.getUserRelatedDepartment(queryDTO);
        if (!result.isSuccess()) {
            LogUtil.error("UserRemoteClient.getUserRelatedDepartment: result = {}", result);
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }

    public SimpleDepartmentDTO getUserRelatedDepartment(String userNo) {
        QueryUserRelatedDepartmentDTO queryDTO = new QueryUserRelatedDepartmentDTO();
        queryDTO.setUserNos(ListUtil.toList(userNo));
        List<SimpleDepartmentDTO> userRelatedDepartmentList = this.getUserRelatedDepartment(queryDTO);
        if (CollectionUtils.isEmpty(userRelatedDepartmentList)) {
            throw new BizException(BizErrorCode.ACCOUNT_NOT_FOUND.getCode(), "用户不存在");
        }
        return userRelatedDepartmentList.get(0);
    }

    public List<SimpleUserInfoDTO> getDepartmentBindUsers(String departmentNo) {
        QueryDepartmentBindUserDTO queryDepartmentBindUserDTO = new QueryDepartmentBindUserDTO();
        queryDepartmentBindUserDTO.setDepartmentId(Convert.toLong(departmentNo));

        Result<List<SimpleUserInfoDTO>> result = departmentClient.getDepartmentBindUsers(queryDepartmentBindUserDTO);

        if (!result.isSuccess()) {
            LogUtil.error("UserRemoteClient.getDepartmentBindUsers: result = {}", result);
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }

    public WholeDepartmentDTO getWholeDepartmentInfo(String userNo) {
        WholeDepartmentQueryDTO queryDTO = new WholeDepartmentQueryDTO();
        queryDTO.setUserNo(userNo);

        Result<WholeDepartmentDTO> wholeDepartmentInfo = departmentClient.getWholeDepartmentInfo(queryDTO);

        if (!wholeDepartmentInfo.isSuccess()) {
            LogUtil.error("UserRemoteClient.getWholeDepartmentInfo: result = {}, params:{}"
                    , JSONObject.toJSONString(wholeDepartmentInfo), userNo);
            throw new BizException(wholeDepartmentInfo.getErrorCode(), wholeDepartmentInfo.getErrorDesc());
        }

        return wholeDepartmentInfo.getData();
    }

}
