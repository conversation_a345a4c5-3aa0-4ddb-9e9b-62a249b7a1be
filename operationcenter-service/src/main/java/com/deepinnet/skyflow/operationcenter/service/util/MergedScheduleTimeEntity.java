package com.deepinnet.skyflow.operationcenter.service.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.Set;

/**
 * Description:
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MergedScheduleTimeEntity {

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;
    /**
     * 最早的开始时间
     */
    private LocalTime earliestStartTime;

    /**
     * 最晚的结束时间
     */
    private LocalTime latestEndTime;

    /**
     * 是否上午
     */
    private boolean isMorning;

    /**
     * 连续天数
     */
    private int continuousDays;

    /**
     * 来源ID列表
     */
    private Set<String> sourceBizEntityIds = new HashSet<>();

    public String toString() {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return String.format("%s %s 至 %s（共 %d 天）, 起飞时间: %s ~ %s, 来源需求: %s",
                isMorning ? "上午" : "下午",
                startDate.format(df),
                endDate.format(df),
                continuousDays,
                earliestStartTime,
                latestEndTime,
                String.join(", ", sourceBizEntityIds));
    }
}
