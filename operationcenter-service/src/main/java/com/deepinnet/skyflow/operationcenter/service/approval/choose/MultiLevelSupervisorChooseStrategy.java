package com.deepinnet.skyflow.operationcenter.service.approval.choose;

import cn.hutool.core.collection.ListUtil;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.infra.api.dto.SimpleDepartmentDTO;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.*;
import com.deepinnet.skyflow.operationcenter.service.approval.impl.ApproveHelper;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.skyflow.operationcenter.service.strategy.Strategy;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyContext;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 获取连续多级主管
 */
@Slf4j
@Component
public class MultiLevelSupervisorChooseStrategy implements Strategy {

    @Resource
    private ApproveHelper approveHelper;

    @Resource
    private UserRemoteClient userService;

    @Override
    public boolean supports(StrategyTypeEnum strategyType, Enum<?> bizEnum) {
        return strategyType == StrategyTypeEnum.CHOOSE_APPROVE && bizEnum == ApproveChooseStrategyEnum.CONTINUOUS_MULTI_LEVEL_SUPERVISOR;
    }

    @Override
    public List<ApprovalStep> execute(StrategyContext context) {
        return generateSupervisorSteps(context.getFirstArg(), context.getSecondArg(), context.getThirdArg());
    }

    /**
     * 生成连续多级主管的审批步骤
     */
    private List<ApprovalStep> generateSupervisorSteps(ApprovalChooseConfigRule chooseConfig, String submitUserId, ApprovalInstanceConfigRule configRule) {
        List<ApprovalStep> steps = new ArrayList<>();

        Map<String, List<String>> orgUserMap = convertConfig(configRule.getApproveOrgUserConfig());
        try {
            // 解析配置JSON，获取需要查找的层级数
            MultiLevelSupervisorChooseConfig multiLevelSupervisorChooseConfig = Optional.ofNullable(JsonUtil.parseJson(chooseConfig.getApproveChooseConfigJson(), MultiLevelSupervisorChooseConfig.class)).orElse(new MultiLevelSupervisorChooseConfig());
            SimpleDepartmentDTO userRelatedDepartment = userService.getUserRelatedDepartment(submitUserId);
            for (int level = 0; level < multiLevelSupervisorChooseConfig.getMaxLevels(); level++) {
                List<String> approverList = findSupervisor(userRelatedDepartment, level, multiLevelSupervisorChooseConfig, orgUserMap);
                if (CollectionUtils.isEmpty(approverList)) {
                    if (multiLevelSupervisorChooseConfig.isSuperiorApproveProxy()) {
                        continue;
                    } else {
                        break;
                    }
                }
                ApprovalStep step = new ApprovalStep();
                step.setApproveMode(chooseConfig.getApproveMode());

                // 创建审批节点
                List<ApprovalNode> approvalNodes = approverList.stream()
                        .map(approveUserId -> approveHelper.createApprovalNode(approveUserId))
                        .collect(Collectors.toList());

                step.setApprovalNodeList(approvalNodes);

                steps.add(step);
            }

        } catch (Exception e) {
            log.error("生成连续多级主管审批步骤失败", e);
        }

        return steps;
    }

    private Map<String, List<String>> convertConfig(ApproveOrgUserConfig approveOrgUserConfig) {
        //组织信息，key: 组织编码，value: 组织审批人列表
        Map<String, List<String>> orgUserMap = new HashMap<>();
        if (approveOrgUserConfig == null) {
            return orgUserMap;
        }
        
        // 递归处理组织结构
        processOrgConfig(approveOrgUserConfig, orgUserMap);
        
        log.info("转换组织配置成功，组织信息为：{}", orgUserMap);
        return orgUserMap;
    }
    
    /**
     * 递归处理组织配置，提取组织编码和对应的审批人列表
     * 
     * @param orgConfig 组织配置
     * @param orgUserMap 结果Map
     */
    private void processOrgConfig(ApproveOrgUserConfig orgConfig, Map<String, List<String>> orgUserMap) {
        if (orgConfig == null) {
            return;
        }
        
        // 处理当前组织
        String orgCode = orgConfig.getOrgCode();
        List<ApproveUserEntity> approverList = orgConfig.getApproverList();
        
        if (orgCode != null && CollectionUtils.isNotEmpty(approverList)) {
            // 提取审批人ID列表
            List<String> approverIds = approverList.stream()
                    .map(ApproveUserEntity::getUserId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            if (CollectionUtils.isNotEmpty(approverIds)) {
                orgUserMap.put(orgCode, approverIds);
            }
        }
        
        // 递归处理子组织
        List<ApproveOrgUserConfig> children = orgConfig.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            for (ApproveOrgUserConfig child : children) {
                processOrgConfig(child, orgUserMap);
            }
        }
    }

    /**
     * 查找审批提交人的第level级的主管
     *
     * @param userRelatedDepartment 审批提交人
     * @param level        层级，从0开始
     * @param config       配置
     * @param orgUserMap   组织信息，key: 组织编码，value: 组织审批人列表
     * @return
     */
    private List<String> findSupervisor(SimpleDepartmentDTO userRelatedDepartment, int level, MultiLevelSupervisorChooseConfig config, Map<String, List<String>> orgUserMap) {
        String submitUserId = userRelatedDepartment.getUserNo();
        //根据level查询组织
        List<Long> fullPath = userRelatedDepartment.getFullPath();
        if (level >= fullPath.size()) {
            return new ArrayList<>();
        }

        Long findOrgCode = fullPath.get(fullPath.size() - level - 1);

        List<String> supervisorList = orgUserMap.getOrDefault(String.valueOf(findOrgCode), ListUtil.toList());
        log.info("组织{}的审批人为：{}", findOrgCode, supervisorList);
        if (CollectionUtils.isEmpty(supervisorList)) {
            return new ArrayList<>();
        }

        //判断如果这个审批人是自己或者自己就是管理员的话那就跳过，必须是上级主管
        if (supervisorList.contains(submitUserId)) {
            if (!config.isAllowSelfLevelApprove()) {
                return new ArrayList<>();
            }

            if (config.isOnlySameLevelOtherApprove()) {
                return supervisorList.stream().filter(userNo -> !Objects.equals(userNo, submitUserId)).collect(Collectors.toList());
            }
        }
        return supervisorList;
    }
}
