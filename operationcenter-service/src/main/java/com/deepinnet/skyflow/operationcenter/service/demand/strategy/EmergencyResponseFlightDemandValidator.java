package com.deepinnet.skyflow.operationcenter.service.demand.strategy;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.strategy.Strategy;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyContext;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyTypeEnum;
import org.springframework.stereotype.Component;

/**
 * Description:
 * Date: 2025/4/27
 * Author: lijunheng
 */
@Component
public class EmergencyResponseFlightDemandValidator implements Strategy {

    @Override
    public boolean supports(StrategyTypeEnum strategyType, Enum<?> demandType) {
        return StrategyTypeEnum.VALIDATOR == strategyType && FlightDemandTypeEnum.EMERGENCY_RESPONSE == demandType;
    }

    @Override
    public Object execute(StrategyContext context) {
        FlightDemandDTO demand = context.getFirstArg();
        validate(demand);
        return null;
    }

    private void validate(FlightDemandDTO demand) {
    }
}
