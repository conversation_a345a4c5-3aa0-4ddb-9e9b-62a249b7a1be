package com.deepinnet.skyflow.operationcenter.service.approval.property;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Description:
 * Date: 2025/7/23
 * Author: lijunheng
 */
@Data
@Component
@ConfigurationProperties(prefix = "approve")
public class ApproveOrgProperty {

    private List<OrgApproval> org;
    private DesignateApproval designate;

    private String zsjOrgCode;

    @Data
    public static class OrgApproval {
        private String orgCode;
        private List<String> director;
    }

    @Data
    public static class DesignateApproval {
        private List<String> director;
    }
}
