package com.deepinnet.skyflow.operationcenter.service.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2025/6/18
 * Author: lijunheng
 */
public class ScheduleTimeMergerUtil {

    public static List<MergedScheduleTimeEntity> mergeBizEntity(List<BizTimeEntity> bizEntities) {
        List<DailyTimeSlot> allSlots = new ArrayList<>();
        for (BizTimeEntity bizTimeEntity : bizEntities) {
            allSlots.addAll(expandToDailySlots(bizTimeEntity));
        }

        // Map<isMorning, Map<date, List<slot>>>
        Map<Boolean, TreeMap<LocalDate, List<DailyTimeSlot>>> grouped = new HashMap<>();
        for (DailyTimeSlot slot : allSlots) {
            grouped
                    .computeIfAbsent(slot.isMorning, k -> new TreeMap<>())
                    .computeIfAbsent(slot.date, k -> new ArrayList<>())
                    .add(slot);
        }

        List<MergedScheduleTimeEntity> result = new ArrayList<>();

        for (boolean isMorning : grouped.keySet()) {
            TreeMap<LocalDate, List<DailyTimeSlot>> dateMap = grouped.get(isMorning);

            LocalDate curStart = null, curEnd = null;
            LocalTime minStart = null, maxEnd = null;
            Set<String> currentDemandIds = new HashSet<>();

            LocalDate prevDate = null;

            for (Map.Entry<LocalDate, List<DailyTimeSlot>> entry : dateMap.entrySet()) {
                LocalDate date = entry.getKey();
                List<DailyTimeSlot> slots = entry.getValue();

                LocalTime dayStart = slots.stream().map(s -> s.startTime).min(LocalTime::compareTo).get();
                LocalTime dayEnd = slots.stream().map(s -> s.endTime).max(LocalTime::compareTo).get();
                Set<String> dayIds = slots.stream().map(s -> s.bizTimeEntityId).collect(Collectors.toSet());

                if (prevDate != null && (!date.equals(prevDate.plusDays(1)) || !dayIds.equals(currentDemandIds))) {
                    result.add(makeMerged(curStart, curEnd, minStart, maxEnd, isMorning, currentDemandIds));
                    curStart = curEnd = null;
                    minStart = maxEnd = null;
                    currentDemandIds = new HashSet<>();
                }

                if (curStart == null) {
                    curStart = curEnd = date;
                    minStart = dayStart;
                    maxEnd = dayEnd;
                    currentDemandIds = dayIds;
                } else {
                    curEnd = date;
                    if (dayStart.isBefore(minStart)) minStart = dayStart;
                    if (dayEnd.isAfter(maxEnd)) maxEnd = dayEnd;
                }

                prevDate = date;
            }

            if (curStart != null) {
                result.add(makeMerged(curStart, curEnd, minStart, maxEnd, isMorning, currentDemandIds));
            }
        }

        return result;
    }

    private static List<DailyTimeSlot> expandToDailySlots(BizTimeEntity bizTimeEntity) {
        List<DailyTimeSlot> slots = new ArrayList<>();
        for (LocalDate date = bizTimeEntity.getStartDate(); !date.isAfter(bizTimeEntity.getEndDate()); date = date.plusDays(1)) {
            if (bizTimeEntity.getRepeatDays().contains(date.getDayOfWeek())) {
                // 上午为 00:00 ~ 11:59，下午为 12:00 ~ 23:59
                boolean isMorning = !bizTimeEntity.getStartTime().isAfter(LocalTime.of(11, 59, 59));
                slots.add(new DailyTimeSlot(date, bizTimeEntity.getStartTime(), bizTimeEntity.getEndTime(), isMorning, bizTimeEntity.getId()));
            }
        }
        return slots;
    }

    private static MergedScheduleTimeEntity makeMerged(LocalDate start, LocalDate end, LocalTime minStart, LocalTime maxEnd,
                                                       boolean isMorning, Set<String> ids) {
        MergedScheduleTimeEntity md = new MergedScheduleTimeEntity();
        md.setStartDate(start);
        md.setEndDate(end);
        md.setEarliestStartTime(minStart);
        md.setLatestEndTime(maxEnd);
        md.setMorning(isMorning);
        md.getSourceBizEntityIds().addAll(ids);
        md.setContinuousDays((int) (ChronoUnit.DAYS.between(start, end) + 1));
        return md;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class DailyTimeSlot {
        LocalDate date;
        LocalTime startTime;
        LocalTime endTime;
        boolean isMorning;
        String bizTimeEntityId;
    }

    public static void main(String[] args) {
        List<BizTimeEntity> bizEntities = List.of(
                new BizTimeEntity("A",
                        LocalDate.of(2025, 6, 1),
                        LocalDate.of(2025, 6, 5),
                        LocalTime.of(10, 10),
                        LocalTime.of(13, 12),
                        EnumSet.allOf(DayOfWeek.class)
                ),
                new BizTimeEntity("B",
                        LocalDate.of(2025, 6, 3),
                        LocalDate.of(2025, 6, 7),
                        LocalTime.of(7, 0),
                        LocalTime.of(11, 0),
                        EnumSet.allOf(DayOfWeek.class)
                ),
                new BizTimeEntity("C",
                        LocalDate.of(2025, 6, 8),
                        LocalDate.of(2025, 6, 10),
                        LocalTime.of(5, 0),
                        LocalTime.of(10, 0),
                        EnumSet.allOf(DayOfWeek.class)
                )
        );

        List<MergedScheduleTimeEntity> merged = mergeBizEntity(bizEntities);
        merged.forEach(md -> System.out.println(md));
    }
}

