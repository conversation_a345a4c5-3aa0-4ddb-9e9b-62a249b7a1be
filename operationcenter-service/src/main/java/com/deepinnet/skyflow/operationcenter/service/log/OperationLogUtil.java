package com.deepinnet.skyflow.operationcenter.service.log;

import com.deepinnet.skyflow.operationcenter.enums.OperationLogTemplateEnum;

/**
 * 操作日志工具类
 * 提供各种业务操作的便捷日志记录方法
 *
 * <AUTHOR> wong
 * @create 2025/8/11 17:55
 * @Description 操作日志便捷工具类，为每个操作日志模板提供对应的便捷方法
 */
public class OperationLogUtil {

    // ==================== 飞行需求 ====================

    /**
     * 便捷方法：飞行需求 - 发布需求
     */
    public static void demandPublish(String demandName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.DEMAND_PUBLISH)
                        .setVariable("demandName", demandName)
        );
    }

    /**
     * 便捷方法：飞行需求 - 编辑需求
     */
    public static void demandEdit(String demandName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.DEMAND_EDIT)
                        .setVariable("demandName", demandName)
        );
    }

    /**
     * 便捷方法：飞行需求 - 删除需求
     */
    public static void demandDelete(String demandName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.DEMAND_DELETE)
                        .setVariable("demandName", demandName)
        );
    }

    /**
     * 便捷方法：飞行需求 - 审批通过需求
     */
    public static void demandApprove(String demandName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.DEMAND_APPROVE)
                        .setVariable("demandName", demandName)
        );
    }

    /**
     * 便捷方法：飞行需求 - 驳回需求
     */
    public static void demandReject(String demandName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.DEMAND_REJECT)
                        .setVariable("demandName", demandName)
        );
    }

    // ==================== 数据下载 ====================

    /**
     * 便捷方法：数据下载 - 下载业务数据报表
     */
    public static void dataDownload(String reportName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.DATA_DOWNLOAD)
                        .setVariable("reportName", reportName)
        );
    }

    // ==================== 审批流设置 ====================

    /**
     * 便捷方法：审批流设置 - 更新飞行规划审批设置
     */
    public static void approvalPlanUpdate() {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.APPROVAL_PLAN_UPDATE)
        );
    }

    /**
     * 便捷方法：审批流设置 - 更新飞行需求审批设置
     */
    public static void approvalDemandUpdate() {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.APPROVAL_DEMAND_UPDATE)
        );
    }

    /**
     * 便捷方法：审批流设置 - 更新飞行调度审批设置
     */
    public static void approvalScheduleUpdate() {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.APPROVAL_SCHEDULE_UPDATE)
        );
    }

    // ==================== 规划周期 ====================

    /**
     * 便捷方法：规划周期 - 新增规划周期
     */
    public static void planningCycleAdd(String cycleName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.PLANNING_CYCLE_ADD)
                        .setVariable("cycleName", cycleName)
        );
    }

    /**
     * 便捷方法：规划周期 - 编辑规划周期
     */
    public static void planningCycleEdit(String cycleName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.PLANNING_CYCLE_EDIT)
                        .setVariable("cycleName", cycleName)
        );
    }

    /**
     * 便捷方法：规划周期 - 删除规划周期
     */
    public static void planningCycleDelete(String cycleName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.PLANNING_CYCLE_DELETE)
                        .setVariable("cycleName", cycleName)
        );
    }

    // ==================== 飞行规划 ====================

    /**
     * 便捷方法：飞行规划 - 发布飞行规划
     */
    public static void flightPlanPublish(String planName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.FLIGHT_PLAN_PUBLISH)
                        .setVariable("planName", planName)
        );
    }

    /**
     * 便捷方法：飞行规划 - 编辑飞行规划
     */
    public static void flightPlanEdit(String planName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.FLIGHT_PLAN_EDIT)
                        .setVariable("planName", planName)
        );
    }

    /**
     * 便捷方法：飞行规划 - 删除飞行规划
     */
    public static void flightPlanDelete(String planName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.FLIGHT_PLAN_DELETE)
                        .setVariable("planName", planName)
        );
    }

    /**
     * 便捷方法：飞行规划 - 审批通过飞行规划
     */
    public static void flightPlanApprove(String planName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.FLIGHT_PLAN_APPROVE)
                        .setVariable("planName", planName)
        );
    }

    /**
     * 便捷方法：飞行规划 - 驳回飞行规划
     */
    public static void flightPlanReject(String planName) {
        OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.FLIGHT_PLAN_REJECT)
                        .setVariable("planName", planName)
        );
    }

    // ==================== 通用方法 ====================

    /**
     * 通用方法：设置操作失败
     */
    public static void setFailure(String errorMessage) {
        OperationLogContext context = OperationLogContextHolder.getContext();
        if (context != null) {
            context.setFailure(errorMessage);
        }
    }

    /**
     * 通用方法：设置操作成功
     */
    public static void setSuccess() {
        OperationLogContext context = OperationLogContextHolder.getContext();
        if (context != null) {
            context.setSuccess();
        }
    }

    /**
     * 通用方法：添加变量
     */
    public static void setVariable(String key, Object value) {
        OperationLogContext context = OperationLogContextHolder.getContext();
        if (context != null) {
            context.setVariable(key, value);
        }
    }

    /**
     * 通用方法：禁用日志记录
     */
    public static void disable() {
        OperationLogContext context = OperationLogContextHolder.getContext();
        if (context != null) {
            context.disable();
        }
    }

    /**
     * 通用方法：启用日志记录
     */
    public static void enable() {
        OperationLogContext context = OperationLogContextHolder.getContext();
        if (context != null) {
            context.enable();
        }
    }

    /**
     * 通用方法：清除上下文
     */
    public static void clearContext() {
        OperationLogContextHolder.clearContext();
    }
}
