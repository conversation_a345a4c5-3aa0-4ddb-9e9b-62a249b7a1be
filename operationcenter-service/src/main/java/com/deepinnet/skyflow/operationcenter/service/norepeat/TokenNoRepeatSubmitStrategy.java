package com.deepinnet.skyflow.operationcenter.service.norepeat;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.strategy.Strategy;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyContext;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 * Date: 2025/7/30
 * Author: lijunheng
 */
@Component
public class TokenNoRepeatSubmitStrategy implements Strategy {

    private static final String NO_REPEAT_TOKEN_KEY = "NO_REPEAT_TOKEN_KEY:";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private DefaultRedisScript<Long> luaScript = new DefaultRedisScript<>();

    @Override
    public boolean supports(StrategyTypeEnum strategyType, Enum<?> bizEnum) {
        return strategyType == StrategyTypeEnum.NO_REPEAT_SUBMIT && bizEnum == RepeatMode.TOKEN;
    }

    @Override
    public Boolean execute(StrategyContext context) {
        ProceedingJoinPoint joinPoint = context.getFirstArg();
        NoRepeatSubmit noRepeatSubmit = context.getSecondArg();

        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        String tokenStr = (String) requestAttributes.getAttribute(noRepeatSubmit.tokenKey(), RequestAttributes.SCOPE_REQUEST);
        if (StringUtils.isBlank(tokenStr)) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "缺少" + noRepeatSubmit.tokenKey());
        }
        if (!checkAndRemoveToken(tokenStr)) {
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "请勿重复提交");
        }
        return Boolean.TRUE;
    }

    @PostConstruct
    public void init() {
        luaScript.setScriptText(
                "if redis.call('EXISTS', KEYS[1]) == 1 then " +
                        "    redis.call('DEL', KEYS[1]); " +
                        "    return 1 " +
                        "else " +
                        "    return 0 " +
                        "end");
        luaScript.setResultType(Long.class);
    }

    /**
     * 校验并删除 token，返回 true 表示可执行
     */
    public boolean checkAndRemoveToken(String tokenKey) {
        Long result = redisTemplate.execute(luaScript, Collections.singletonList(NO_REPEAT_TOKEN_KEY + tokenKey));
        return result != null && result == 1;
    }

    public void putToken(String tokenKey, long timeout, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(NO_REPEAT_TOKEN_KEY + tokenKey, "1", timeout, timeUnit);
    }
}
