package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:
 * Date: 2025/7/11
 * Author: lijunheng
 */
@Data
public class ApprovalNode implements Serializable {

    private Long id;

    private String approvalId;

    private Integer stepOrder;

    /**
     * 审批人id
     */
    private String approveUserId;

    private String approveUserName;

    private String approveDepartmentId;

    private String approveDepartmentName;

    private String approveUserPhone;

    private ApproveUserEntity approveUserEntity;

    /**
     * 审批结果
     */
    private ApproveStatusEnum status = ApproveStatusEnum.PENDING;

    /**
     * 审批结果备注
     */
    private String remark;

    /**
     * 审批结果操作时间
     */
    private LocalDateTime approvalTime;

    /**
     * 是否自动通过
     */
    private Boolean autoPass = false;

    /**
     * 是否自动拒绝
     */
    private Boolean autoReject = false;

    /**
     * 审批超时配置
     */
    private ApproveTimeoutConfig approveTimeoutConfig;
}
