package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 * Date: 2025/7/11
 * Author: lijunheng
 */
@Data
public class ApprovalInstance implements Serializable {

    private Long id;

    /**
     * 审批实例ID
     */
    private String approvalId;

    /**
     * 审批业务类型，比如需求审核、撮合订单审核、需求规划审核
     */
    private String bizType;

    /**
     * 业务ID
     */
    private String bizId;

    /**
     * 审批状态
     */
    private ApproveStatusEnum status;

    /**
     * 审批发起人ID
     */
    private String submitUserId;

    private String submitUserName;

    private String submitDepartmentId;

    private String submitDepartmentName;

    private String submitUserPhone;

    private ApproveUserEntity submitUserEntity;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 审批结束时间
     */
    private LocalDateTime approveEndTime;

    /**
     * 当前审批步骤
     */
    private int currentStep;

    /**
     * 审批步骤列表
     */
    private List<ApprovalStep> steps = new ArrayList<>();

    /**
     * 审批完成后的回调参数
     */
    private String callbackParams;

    private String tenantId;

    /**
     * 审批超时配置
     */
    private ApproveTimeoutConfig approveTimeoutConfig;
}
