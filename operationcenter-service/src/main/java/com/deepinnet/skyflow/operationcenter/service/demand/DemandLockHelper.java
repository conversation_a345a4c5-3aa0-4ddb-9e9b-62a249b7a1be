package com.deepinnet.skyflow.operationcenter.service.demand;

import com.deepinnet.lock.DatabaseDistributedLock;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 执行表：id、task_current、task_expire_time、operate_current、operate_expire_time、tenant_id
 * 当执行定时任务时需要先判断任务是否未执行或者已过期，同时还得判断操作未执行或者已过期
 * update task = true where ((task = false) or (task = true and task_expire_time < now())
 * and (operate = false and operate_expire_time < now())
 *
 * 当执行操作时需要先判断任务未执行或者已过期。
 * 因为任务是不可重入的，所以只需要加一个时间续期任务
 * 当执行操作时，需要设定一个定时续期任务，由一个AtomicInteger计数，执行一个操作就加一，结束就减一，当计数为0时，取消续期任务
 *
 * 执行合并操作时，对merge_handle_code加锁
 * 执行分配操作时，对需求上的merge_handle_code加锁，如果不存在就说明是不参与合并的原始需求，则对demandCode加锁
 */
@Component
public class DemandLockHelper {

    /**
     * 系统定时创建合并需求建议任务锁
     */
    private static final String LOCK_KEY_DEMAND_MERGE_TASK = "demand_merge_task";

    /**
     * 合并-合并需求任务锁
     */
    private static final String LOCK_KEY_DEMAND_MERGE_HANDLE = "demand_merge_handle_";

    /**
     * 需求分配服务商锁
     */
    private static final String LOCK_KEY_DEMAND_ASSIGN_SERVICE_PROVIDER_HANDLE = "demand_assign_service_provider_handle_";

    /**
     * 系统定时执行需求匹配服务商任务锁
     */
    private static final String LOCK_KEY_DEMAND_MATCH_TASK = "demand_match_service_provider_task";

    /**
     * 需求同步给服务商锁
     */
    private static final String LOCK_KEY_DEMAND_SYNC_TO_PROVIDER_HANDLE = "demand_sync_to_provider_handle_";

    /**
     * 需求编辑任务锁
     */
    private static final String LOCK_KEY_DEMAND_EDIT_TASK = "demand_edit_task_";

    @Resource
    private DatabaseDistributedLock databaseDistributedLock;

    public boolean tryLockMergeDemandTask() {
        return databaseDistributedLock.tryLock(LOCK_KEY_DEMAND_MERGE_TASK);
    }

    public boolean tryLockMatchDemandTask() {
        return databaseDistributedLock.tryLock(LOCK_KEY_DEMAND_MERGE_TASK);
    }

    public boolean tryLockExecMergeHandle(String mergeHandleCode) {
        // 执行合并时应该获取
        // 合并任务锁：如果一个人在合并操作中，定时任务在跑合并任务，可能将要合并的这个需求被任务取消掉了，或者被重新组合了
        // 分配任务锁：如果一个人在合并操作中，定时任务在跑分配任务，合并的这个需求中有的原始需求被分配了，那么就不应该再进行合并操作
        // 获取这个合并建议的锁，因为可能同时有多个人在操作这个合并建议

        //todo 但有个问题，这两个任务锁几乎在每次合并或者分配时都会获取，这样的话会极大的限制系统的并发，有什么好的解决方案吗？
        return databaseDistributedLock.tryLock(LOCK_KEY_DEMAND_MERGE_TASK);
    }

    public boolean tryLockAssignDemand(String demandCode) {
        //执行分配服务商时应该获取
        // 合并任务锁：如果一个人在分配服务商操作中，定时任务在跑合并任务，可能将这个分配掉的需求重新加入到了合并建议中
        // 分配任务锁：如果一个人在分配服务商操作中，定时任务在跑分配任务，这个需求就可能被重复分配，而且可能被分配到了不同的服务商，引起冲突
        // 获取这个需求的锁，因为可能同时有多个人在为这个需求分配服务商
        //这里不需要再获取合并需求的锁了，因为他们都必须持有任务锁，这里就会被限制住

        //todo 但有个问题，这两个任务锁几乎在每次合并或者分配时都会获取，这样的话会极大的限制系统的并发，有什么好的解决方案吗？

        return databaseDistributedLock.tryLock(LOCK_KEY_DEMAND_MERGE_TASK);
    }



    /**
     * 释放锁列表
     * @param lockKeys 要释放的锁键列表
     */
    private void releaseLocks(List<String> lockKeys) {
        for (String lockKey : lockKeys) {
            try {
                databaseDistributedLock.unlock(lockKey);
            } catch (Exception e) {
                // 记录日志但不抛出异常，确保其他锁能继续释放
                // 这里可以加日志记录
            }
        }
    }

    public void unlockMergeDemandTask() {
        databaseDistributedLock.unlock(LOCK_KEY_DEMAND_MERGE_TASK);
    }

    public void unlockMatchDemandTask() {
        databaseDistributedLock.unlock(LOCK_KEY_DEMAND_MERGE_TASK);
    }

    public void unlockExecMergeHandle(String mergeHandleCode) {
        // 释放执行合并时获取的所有锁
        databaseDistributedLock.unlock(LOCK_KEY_DEMAND_MERGE_TASK);
    }

    public void unlockAssignDemand(String demandCode) {
        // 释放执行分配时获取的所有锁
        databaseDistributedLock.unlock(LOCK_KEY_DEMAND_MERGE_TASK);
    }

    public boolean tryLockEditDemand(String demandNo) {
        return databaseDistributedLock.tryLock(LOCK_KEY_DEMAND_EDIT_TASK + demandNo);
    }

    public void unlockEditDemand(String demandNo) {
        databaseDistributedLock.unlock(LOCK_KEY_DEMAND_EDIT_TASK + demandNo);
    }
}
