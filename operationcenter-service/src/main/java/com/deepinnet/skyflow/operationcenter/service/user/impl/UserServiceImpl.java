package com.deepinnet.skyflow.operationcenter.service.user.impl;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.UserClient;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.DataAccessDTO;
import com.deepinnet.infra.api.dto.DataScopeMemberUserDTO;
import com.deepinnet.infra.api.dto.DataScopeTreeNodeDTO;
import com.deepinnet.skyflow.operationcenter.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wong
 * @create 2025/4/21 17:36
 * @Description
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private UserClient userClient;

    private static final String NOOP = "noop";

    @Override
    public Boolean checkUserInfoStatus() {
        Result<Boolean> res = userClient.checkUserInfoStatus();
        if (!res.isSuccess()) {
            throw new BizException(res.getErrorCode(), res.getErrorDesc());
        }

        return res.getData();
    }

    @Override
    public List<String> getAvailableUserNoByDepartmentId(Long departmentId) {

        Result<DataAccessDTO> dataAccessDTOResult = userClient.getAvailableQueryData();
        if(!dataAccessDTOResult.isSuccess()) {
            throw new BizException(dataAccessDTOResult.getErrorCode(), dataAccessDTOResult.getErrorDesc());
        }

        if(departmentId == null) {
            // 超管查所有，返回空
            if(dataAccessDTOResult.getData().getSupportQueryUserNos().contains(DataScopeConstants.SUPER_ADMIN)) {
                return List.of();
            }

            return dataAccessDTOResult.getData().getSupportQueryUserNos();
        }

        Result<List<DataScopeTreeNodeDTO>> listResult = userClient.getAvailableDataScopeTree();
        if(!listResult.isSuccess()) {
            throw new BizException(listResult.getErrorCode(), listResult.getErrorDesc());
        }

        if(CollectionUtils.isEmpty(listResult.getData())) {
            return dataAccessDTOResult.getData().getSupportQueryUserNos();
        }

        DataScopeTreeNodeDTO treeNodeDTO = findInTree(departmentId, listResult.getData());
        if(treeNodeDTO == null) {
            log.warn("无法在权限树下找到部门 [{}]", departmentId);
            // 超管查所有，返回空
            if(dataAccessDTOResult.getData().getSupportQueryUserNos().contains(DataScopeConstants.SUPER_ADMIN)) {
                return List.of();
            }

            return dataAccessDTOResult.getData().getSupportQueryUserNos();
        }

        List<String> res = new ArrayList<>();
        collectUserNoRecursively(treeNodeDTO, res);

        // 如果为空那表明部门下没有人了
        if(CollectionUtils.isEmpty(res)) {
            res.add(NOOP);
        }

        return res;
    }

    private DataScopeTreeNodeDTO findInTree(Long departmentId, List<DataScopeTreeNodeDTO> list) {
        if(CollectionUtils.isEmpty(list)) {
            return null;
        }

        Optional<DataScopeTreeNodeDTO> op = list.stream().filter(l -> l.getDepartmentId().equals(departmentId)).findFirst();
        return op.orElseGet(() -> findInTree(departmentId, list.stream().flatMap(l -> l.getChildren().stream()).collect(Collectors.toList())));
    }

    private void collectUserNoRecursively(DataScopeTreeNodeDTO treeNodeDTO, List<String> userNoList) {
        if(treeNodeDTO == null) {
            return;
        }

        userNoList.addAll(treeNodeDTO.getMemberUsers().stream()
                .map(DataScopeMemberUserDTO::getUserNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList()));

        // 遍历子部门，递归收集成员
        if (treeNodeDTO.getChildren() != null) {
            for (DataScopeTreeNodeDTO child : treeNodeDTO.getChildren()) {
                collectUserNoRecursively(child, userNoList);
            }
        }
    }
}
