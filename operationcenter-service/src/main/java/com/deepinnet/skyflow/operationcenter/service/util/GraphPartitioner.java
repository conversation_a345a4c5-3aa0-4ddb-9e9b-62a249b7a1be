package com.deepinnet.skyflow.operationcenter.service.util;

/**
 * Description:
 * Date: 2025/6/19
 * Author: lijunheng
 */
import java.util.*;

public class GraphPartitioner {

    private final double[][] distances;
    private final double limit;
    private final int size;

    public GraphPartitioner(double[][] distances, double limit) {
        this.distances = distances;
        this.limit = limit;
        this.size = distances.length;
    }

    // 判断某节点是否可以加入当前组
    private boolean canJoinGroup(List<Integer> group, int nodeIndex) {
        for (int member : group) {
            if (distances[member][nodeIndex] >= limit) {
                return false;
            }
        }
        return true;
    }

    // 返回分组结果
    public List<List<Integer>> partitionIntoCliques() {
        List<List<Integer>> result = new ArrayList<>();
        boolean[] used = new boolean[size];

        for (int i = 0; i < size; i++) {
            if (used[i]) continue;

            boolean added = false;
            for (List<Integer> group : result) {
                if (canJoinGroup(group, i)) {
                    group.add(i);
                    used[i] = true;
                    added = true;
                    break;
                }
            }

            if (!added) {
                List<Integer> newGroup = new ArrayList<>();
                newGroup.add(i);
                result.add(newGroup);
                used[i] = true;
            }
        }

        return result;
    }

    // 示例用法
    public static void main(String[] args) {
        double[][] distances = {
                {0, 3.2, 4.9, 5.5, 2.0},
                {3.2, 0, 2.1, 4.0, 6.0},
                {4.9, 2.1, 0, 3.8, 4.5},
                {5.5, 4.0, 3.8, 0, 1.0},
                {2.0, 6.0, 4.5, 1.0, 0}
        };

        double limit = 5.0;
        GraphPartitioner partitioner = new GraphPartitioner(distances, limit);
        List<List<Integer>> result = partitioner.partitionIntoCliques();

        int idx = 1;
        for (List<Integer> group : result) {
            System.out.println("Group " + (idx++) + ": " + group);
        }
    }
}

