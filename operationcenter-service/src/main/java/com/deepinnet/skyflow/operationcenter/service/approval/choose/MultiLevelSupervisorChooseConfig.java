package com.deepinnet.skyflow.operationcenter.service.approval.choose;

import lombok.Data;

import java.io.Serializable;

@Data
public class MultiLevelSupervisorChooseConfig implements Serializable {

    /**
     * 默认最多3级
     */
    private int maxLevels = 3;

    /**
     * 如果提交人自己是主管，是否允许自己层级的主管审核，如果不允许那就需要上级组织主管审核
     */
    private boolean allowSelfLevelApprove = true;

    /**
     * 是否只允许同层级的其它主管审核，如果是那么需要移除提交人自己。必须在allowSelfLevelApprove为True时才有效
     */
    private boolean onlySameLevelOtherApprove = true;

    /**
     * 上级主管代行审批
     */
    private boolean superiorApproveProxy = true;
}
