package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.OperationLogDO;
import com.deepinnet.skyflow.operationcenter.dto.OperationLogDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 操作日志转换器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface OperationLogConvert {

    /**
     * DO转DTO
     */
    OperationLogDTO convertToDTO(OperationLogDO operationLogDO);

    /**
     * DO列表转DTO列表
     */
    List<OperationLogDTO> convertToDTO(List<OperationLogDO> operationLogDOList);

    /**
     * DTO转DO
     */
    OperationLogDO convertToDO(OperationLogDTO operationLogDTO);
}
