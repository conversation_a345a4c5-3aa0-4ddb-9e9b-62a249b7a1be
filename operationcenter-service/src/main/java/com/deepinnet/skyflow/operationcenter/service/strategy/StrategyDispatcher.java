package com.deepinnet.skyflow.operationcenter.service.strategy;

import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApproveChooseStrategyEnum;
import com.deepinnet.skyflow.operationcenter.service.norepeat.RepeatMode;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description: 通用策略转发器
 * Date: 2025/6/16
 * Author: lijunheng
 */
@Component
public class StrategyDispatcher {

    @Resource
    private List<Strategy> strategies;

    /**
     * 执行策略
     * 
     * @param strategyType 策略类型
     * @param bizEnum 业务类型
     * @param context 执行上下文
     * @return 执行结果
     */
    public <T> T execute(StrategyTypeEnum strategyType, Enum bizEnum, StrategyContext context) {
        for (Strategy strategy : strategies) {
            if (strategy.supports(strategyType, bizEnum)) {
                return (T) strategy.execute(context);
            }
        }
        return null;
    }
    
    /**
     * 便捷方法：验证需求
     */
    public void validate(FlightDemandTypeEnum demandType, Object... args) {
        StrategyContext context = StrategyContext.builder().args(args).build();
        execute(StrategyTypeEnum.VALIDATOR, demandType, context);
    }
    
    /**
     * 便捷方法：合并需求详情
     */
    public <T> T mergeDetails(FlightDemandTypeEnum demandType, Object... args) {
        StrategyContext context = StrategyContext.builder().args(args).build();
        return execute(StrategyTypeEnum.MERGE_DETAIL, demandType, context);
    }

    public <T> T chooseApproves(ApproveChooseStrategyEnum chooseStrategy, Object... args) {
        StrategyContext context = StrategyContext.builder().args(args).build();
        return execute(StrategyTypeEnum.CHOOSE_APPROVE, chooseStrategy, context);
    }

    public <T> T noRepeatSubmitCheck(RepeatMode repeatMode, Object... args) {
        StrategyContext context = StrategyContext.builder().args(args).build();
        return execute(StrategyTypeEnum.NO_REPEAT_SUBMIT, repeatMode, context);
    }
}