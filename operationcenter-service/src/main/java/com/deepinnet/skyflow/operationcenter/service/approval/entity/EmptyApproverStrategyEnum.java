package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 * Date: 2025/7/21
 * Author: lijunheng
 */
@AllArgsConstructor
@Getter
public enum EmptyApproverStrategyEnum {
    AUTO_PASS("AUTO_PASS", "自动通过"),
    AUTO_REJECT("AUTO_REJECT", "自动拒绝"),
    AUTO_SUBMIT_ADMIN("AUTO_SUBMIT_ADMIN", "自动转交管理员"),
    DESIGNATED_PERSON("DESIGNATED_PERSON", "指定人员审批"),
    ;

    private final String type;

    private final String desc;
}
