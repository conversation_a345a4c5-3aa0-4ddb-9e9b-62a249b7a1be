package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.MergeRuleConfigDO;
import com.deepinnet.skyflow.operationcenter.enums.MergeRuleTypeEnum;
import com.deepinnet.skyflow.operationcenter.vo.MergeRuleConfigVO;
import org.mapstruct.*;
import java.util.List;

/**
 * <p>
 *     map_struct_mapping
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/11
 */

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface MergeRuleConfigConvert {

    List<MergeRuleConfigVO> toVOList(List<MergeRuleConfigDO> doList);

    @Mapping(target = "ruleType", expression = "java(getRuleTypeEnum(mergeRuleConfigDO.getRuleType()))")
    MergeRuleConfigVO toVO(MergeRuleConfigDO mergeRuleConfigDO);

    /**
     * 根据规则类型获取对应的枚举
     * @param ruleType 规则类型
     * @return 对应的枚举，不存在则返回null
     */
    default MergeRuleTypeEnum getRuleTypeEnum(String ruleType) {
        return MergeRuleTypeEnum.getRuleEnumByRuleType(ruleType);
    }

}
