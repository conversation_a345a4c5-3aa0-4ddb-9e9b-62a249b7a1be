package com.deepinnet.skyflow.operationcenter.service.log;

/**
 * 操作日志上下文持有者
 * 使用ThreadLocal存储当前线程的操作日志上下文
 *
 * <AUTHOR>
 */
public class OperationLogContextHolder {

    private static final ThreadLocal<OperationLogContext> CONTEXT_HOLDER = new ThreadLocal<>();

    /**
     * 设置操作日志上下文
     */
    public static void setContext(OperationLogContext context) {
        CONTEXT_HOLDER.set(context);
    }

    /**
     * 获取操作日志上下文
     */
    public static OperationLogContext getContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 清除操作日志上下文
     */
    public static void clearContext() {
        CONTEXT_HOLDER.remove();
    }

    /**
     * 判断是否存在上下文
     */
    public static boolean hasContext() {
        return CONTEXT_HOLDER.get() != null;
    }

    /**
     * 获取上下文，如果不存在则创建一个新的
     */
    public static OperationLogContext getOrCreateContext() {
        OperationLogContext context = CONTEXT_HOLDER.get();
        if (context == null) {
            context = new OperationLogContext();
            CONTEXT_HOLDER.set(context);
        }
        return context;
    }
}
