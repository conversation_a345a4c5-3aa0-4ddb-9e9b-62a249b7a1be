package com.deepinnet.skyflow.operationcenter.service.rule;

import com.deepinnet.skyflow.operationcenter.dto.MergeDemandRuleDTO;
import com.deepinnet.skyflow.operationcenter.vo.MergeRuleConfigVO;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/11
 */
public interface DemandMergeRuleService {

    /**
     * 查询合并规则列表
     * @return 合规规则
     */
    List<MergeRuleConfigVO> ruleList();

    /**
     * 保存合并规则
     * @param rule 保存规则
     * @return 是否保存成功
     */
    Boolean save(MergeDemandRuleDTO rule);
}
