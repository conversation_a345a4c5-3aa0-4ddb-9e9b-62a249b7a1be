package com.deepinnet.skyflow.operationcenter.service.flow.order;

import com.deepinnet.skyflow.operationcenter.service.flow.context.OrderApproveContext;
import com.yomahub.liteflow.annotation.LiteflowCmpDefine;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import com.yomahub.liteflow.enums.NodeTypeEnum;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@LiteflowComponent(value = "flightOrderTypeApproveSwitchNode", name = "订单类型审核切换节点")
@LiteflowCmpDefine(value = NodeTypeEnum.SWITCH)
public class FlightOrderTypeApproveSwitchNode {

    private static final String APPROVE = "APPROVE_";

    @LiteflowMethod(LiteFlowMethodEnum.PROCESS_SWITCH)
    public String process(NodeComponent bindCmp) {
        OrderApproveContext contextBean = bindCmp.getContextBean(OrderApproveContext.class);

        return APPROVE + contextBean.getOrderType();
    }
}
