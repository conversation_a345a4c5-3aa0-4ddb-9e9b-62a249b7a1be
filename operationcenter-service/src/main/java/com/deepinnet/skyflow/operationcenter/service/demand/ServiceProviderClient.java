package com.deepinnet.skyflow.operationcenter.service.demand;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;

import java.util.List;

/**
 * 服务提供商客户端接口
 * 
 * <AUTHOR>
 */
public interface ServiceProviderClient {

    /**
     * 获取根据需求最匹配满足条件的服务提供商
     * 
     * @param demand 飞行需求
     * @return 匹配的服务提供商编号列表
     */
    String getBestMatchServiceProvider(FlightDemandDTO demand);

    /**
     * 获取根据需求满足条件的所有服务提供商
     *
     * @param demand 飞行需求
     * @return 匹配的服务提供商编号列表
     */
    List<String> getMatchServiceProviderList(FlightDemandDTO demand);

    /**
     * 同步需求给服务提供商
     * 
     * @param demand 飞行需求
     * @return 服务提供商方的唯一订单号
     */
    String syncDemandToProvider(FlightDemandDTO demand, String providerNo);
} 