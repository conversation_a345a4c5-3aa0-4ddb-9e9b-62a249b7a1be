package com.deepinnet.skyflow.operationcenter.service.product.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderProductUsageDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightProductDO;
import com.deepinnet.skyflow.operationcenter.dal.mapper.FlightProductMapper;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderProductUsageRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightProductRepository;
import com.deepinnet.skyflow.operationcenter.dto.CategoryDTO;
import com.deepinnet.skyflow.operationcenter.dto.CategoryQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductStatQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmDTO;
import com.deepinnet.skyflow.operationcenter.dto.PriceDTO;
import com.deepinnet.skyflow.operationcenter.enums.BizTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.CategoryTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightProductTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.ProductServiceTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.ProductShelfStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightProductConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.product.CategoryService;
import com.deepinnet.skyflow.operationcenter.service.product.FlightProductService;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavBmService;
import com.deepinnet.skyflow.operationcenter.service.product.PriceService;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.deepinnet.skyflow.operationcenter.vo.FlightProductCategoryStatVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightProductStatVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightProductVO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 飞行产品服务实现类
 *
 * <AUTHOR>
 */
@Service
public class FlightProductServiceImpl implements FlightProductService {

    @Resource
    private FlightProductRepository flightProductRepository;

    @Resource
    private FlightOrderProductUsageRepository orderProductUsageRepository;

    @Resource
    private FlightProductConvert flightProductConvert;

    @Resource
    private FlightUavBmService flightUavBmService;

    @Resource
    private PriceService priceService;

    @Resource
    private FlightProductMapper flightProductMapper;

    @Resource
    private CategoryService categoryService;

    @Override
    public String saveFlightProduct(FlightProductDTO flightProductDTO) {
        if (StringUtils.isBlank(flightProductDTO.getProductNo())) {
            flightProductDTO.setProductNo(IdGenerateUtil.getId(BizTypeEnum.PRODUCT.getType()));
        }

        // 检查产品编号是否已存在
        FlightProductDO existProduct = flightProductRepository.getOne(
                Wrappers.lambdaQuery(FlightProductDO.class)
                        .eq(FlightProductDO::getProductNo, flightProductDTO.getProductNo())
        );
        if (existProduct != null) {
            LogUtil.error("保存飞行产品失败，产品编号已存在: {}", flightProductDTO.getProductNo());
            throw new BizException(BizErrorCode.PRODUCT_ALREADY_EXISTS.getCode(), BizErrorCode.PRODUCT_ALREADY_EXISTS.getDesc());
        }

        // 设置默认服务类型
        if (flightProductDTO.getProductServiceType() == null) {
            flightProductDTO.setProductServiceType(ProductServiceTypeEnum.NORMAL_SERVICE);
        }
        
        // 设置默认上下架状态
        if (flightProductDTO.getProductShelfStatus() == null) {
            flightProductDTO.setProductShelfStatus(ProductShelfStatusEnum.PENDING);
        }
        
        // 根据产品类型进行参数校验
        validateProductParams(flightProductDTO);

        // 计算最低价格
        if (flightProductDTO.getPriceList() != null && !flightProductDTO.getPriceList().isEmpty()) {
            BigDecimal minPrice = flightProductDTO.getPriceList().stream()
                    .map(PriceDTO::getBasePrice)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
            flightProductDTO.setProductMinPrice(minPrice);
        }

        FlightProductDO flightProductDO = flightProductConvert.convertToDO(flightProductDTO);
        flightProductDO.setGmtCreated(LocalDateTime.now());
        flightProductDO.setGmtModified(LocalDateTime.now());
        flightProductDO.setId(null);

        boolean success = flightProductRepository.save(flightProductDO);
        if (!success) {
            LogUtil.error("保存飞行产品失败: {}", flightProductDTO.getProductNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("保存飞行产品成功: {}", flightProductDO.getId());
        return flightProductDO.getProductNo();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createFlightProduct(FlightProductDTO flightProductDTO) {
        FlightProductTypeEnum productType = flightProductDTO.getProductType();
        String productNo = IdGenerateUtil.getId(BizTypeEnum.PRODUCT.getType());
        flightProductDTO.setProductNo(productNo);

        // 保存产品
        String id = saveFlightProduct(flightProductDTO);

        // 保存定价
        List<PriceDTO> priceList = flightProductDTO.getPriceList();
        if (priceList != null && !priceList.isEmpty()) {
            // 设置产品编号
            for (PriceDTO price : priceList) {
                price.setProductNo(productNo);
            }
            // 批量保存价格
            priceService.batchSavePrice(priceList);
        }

        // 保存机型
        FlightUavBmDTO flightUavBm = flightProductDTO.getFlightUavBm();
        if (flightUavBm != null) {
            String flightUavBmNo = flightUavBmService.saveFlightUavBm(flightUavBm);

            // 更新FlightProduct的flightUavBmList字段
            if (StringUtils.isNotBlank(flightUavBmNo)) {
                FlightProductDO flightProductDO = new FlightProductDO();
                flightProductDO.setFlightUavBmList(new String[]{flightUavBmNo});
                flightProductDO.setProductNo(productNo);
                flightProductDO.setGmtModified(LocalDateTime.now());

                // 根据productNo更新
                boolean updateResult = flightProductRepository.update(
                        flightProductDO,
                        Wrappers.lambdaQuery(FlightProductDO.class)
                                .eq(FlightProductDO::getProductNo, productNo)
                );
                LogUtil.info("更新产品机型列表成功: {}", productNo);
            }
        }


        return id;
    }

    @Override
    public boolean updateFlightProduct(FlightProductDTO flightProductDTO) {
        if (flightProductDTO.getId() == null) {
            LogUtil.error("更新飞行产品失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查产品是否存在
        FlightProductDO existProduct = flightProductRepository.getById(flightProductDTO.getId());
        if (existProduct == null) {
            LogUtil.error("更新飞行产品失败，产品不存在: {}", flightProductDTO.getId());
            throw new BizException(BizErrorCode.PRODUCT_NOT_FOUND.getCode(), BizErrorCode.PRODUCT_NOT_FOUND.getDesc());
        }

        // 如果更新了产品编号，需要检查是否与其他产品冲突
        if (!existProduct.getProductNo().equals(flightProductDTO.getProductNo())) {
            FlightProductDO conflictProduct = flightProductRepository.getOne(
                    Wrappers.lambdaQuery(FlightProductDO.class)
                            .eq(FlightProductDO::getProductNo, flightProductDTO.getProductNo())
                            .ne(FlightProductDO::getId, flightProductDTO.getId())
            );
            if (conflictProduct != null) {
                LogUtil.error("更新飞行产品失败，产品编号已被其他产品使用: {}", flightProductDTO.getProductNo());
                throw new BizException(BizErrorCode.PRODUCT_ALREADY_EXISTS.getCode(), BizErrorCode.PRODUCT_ALREADY_EXISTS.getDesc());
            }
        }
        
        // 根据产品类型进行参数校验
        validateProductParams(flightProductDTO);

        // 计算最低价格
        if (flightProductDTO.getPriceList() != null && !flightProductDTO.getPriceList().isEmpty()) {
            BigDecimal minPrice = flightProductDTO.getPriceList().stream()
                    .map(PriceDTO::getBasePrice)
                    .min(BigDecimal::compareTo)
                    .orElse(null);
            flightProductDTO.setProductMinPrice(minPrice);
        }

        FlightProductDO flightProductDO = flightProductConvert.convertToDO(flightProductDTO);
        flightProductDO.setGmtModified(LocalDateTime.now());

        boolean success = flightProductRepository.updateById(flightProductDO);
        if (!success) {
            LogUtil.error("更新飞行产品失败: {}", flightProductDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("更新飞行产品成功: {}", flightProductDO.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFlightProductWithRelated(FlightProductDTO flightProductDTO) {
        // 更新产品基本信息
        boolean success = updateFlightProduct(flightProductDTO);

        // 更新机型
        FlightUavBmDTO flightUavBm = flightProductDTO.getFlightUavBm();
        if (flightUavBm != null && flightUavBm.getId() != null) {
            flightUavBmService.updateFlightUavBm(flightUavBm);
        }

        // 更新定价
        List<PriceDTO> priceList = flightProductDTO.getPriceList();
        if (priceList != null && !priceList.isEmpty()) {
            // 先删除原有的价格
            priceService.deletePricesByProductNo(flightProductDTO.getProductNo());
            // 批量添加新的价格
            for (PriceDTO price : priceList) {
                price.setProductNo(flightProductDTO.getProductNo());
            }
            priceService.batchSavePrice(priceList);
        }

        return success;
    }

    @Override
    public FlightProductDTO getFlightProductById(Integer id) {
        if (id == null) {
            LogUtil.error("获取飞行产品失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightProductDO flightProductDO = flightProductRepository.getById(id);
        if (flightProductDO == null) {
            LogUtil.error("获取飞行产品失败，产品不存在: {}", id);
            throw new BizException(BizErrorCode.PRODUCT_NOT_FOUND.getCode(), BizErrorCode.PRODUCT_NOT_FOUND.getDesc());
        }

        return flightProductConvert.convert(flightProductDO);
    }

    @Override
    public FlightProductVO getFlightProductWithRelatedById(Integer id) {
        FlightProductDTO flightProductDTO = getFlightProductById(id);

        if (flightProductDTO == null) {
            LogUtil.error("获取飞行产品失败，产品不存在: {}", id);
            throw new BizException(BizErrorCode.PRODUCT_NOT_FOUND.getCode(), BizErrorCode.PRODUCT_NOT_FOUND.getDesc());
        }
        // 如果是机型服务产品，需要加载关联的无人机品牌型号信息
        if (flightProductDTO.getProductType() == FlightProductTypeEnum.FLIGHT_UAV) {
            if (flightProductDTO.getFlightUavBmList() == null) {
                LogUtil.error("获取飞行产品失败，关联的无人机品牌型号为空: {}", flightProductDTO.getProductNo());
                throw new BizException(BizErrorCode.PRODUCT_NOT_FOUND.getCode(), BizErrorCode.PRODUCT_NOT_FOUND.getDesc());
            }
            // 通过productNo查找关联的无人机品牌型号
            List<FlightUavBmDTO> flightUavBm = flightUavBmService.listFlightUavBm(Arrays.asList(flightProductDTO.getFlightUavBmList()));
            if (CollectionUtil.isEmpty(flightUavBm)) {
                LogUtil.error("获取飞行产品失败，关联的无人机品牌型号不存在: {}", flightProductDTO.getProductNo());
                throw new BizException(BizErrorCode.PRODUCT_NOT_FOUND.getCode(), BizErrorCode.PRODUCT_NOT_FOUND.getDesc());
            }
            flightProductDTO.setFlightUavBm(flightUavBm.get(0));
        }

        // 获取关联的价格列表
        List<PriceDTO> pricesByProductNo = priceService.getPricesByProductNo(flightProductDTO.getProductNo());
        flightProductDTO.setPriceList(pricesByProductNo);


        return flightProductConvert.convertToVO(flightProductDTO);
    }

    @Override
    public FlightProductDTO getFlightProductByNo(String productNo) {
        if (StringUtils.isBlank(productNo)) {
            LogUtil.error("获取飞行产品失败，产品编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightProductDO flightProductDO = flightProductRepository.getOne(
                Wrappers.lambdaQuery(FlightProductDO.class)
                        .eq(FlightProductDO::getProductNo, productNo)
        );
        if (flightProductDO == null) {
            LogUtil.error("获取飞行产品失败，产品不存在: {}", productNo);
            throw new BizException(BizErrorCode.PRODUCT_NOT_FOUND.getCode(), BizErrorCode.PRODUCT_NOT_FOUND.getDesc());
        }

        return flightProductConvert.convert(flightProductDO);
    }

    @Override
    public FlightProductVO getFlightProductWithRelatedByNo(String productNo) {
        FlightProductDTO flightProductDTO = getFlightProductByNo(productNo);

        if (flightProductDTO != null) {
            // 如果是机型服务产品，需要加载关联的无人机品牌型号信息
            if (flightProductDTO.getProductType() == FlightProductTypeEnum.FLIGHT_UAV) {
                // 通过productNo查找关联的无人机品牌型号
                if (flightProductDTO.getFlightUavBmList() == null) {
                    LogUtil.error("获取飞行产品失败，关联的无人机品牌型号为空: {}", flightProductDTO.getProductNo());
                    throw new BizException(BizErrorCode.PRODUCT_NOT_FOUND.getCode(), "关联的无人机品牌型号为空");
                }
                List<FlightUavBmDTO> flightUavBm = flightUavBmService.listFlightUavBm(Arrays.asList(flightProductDTO.getFlightUavBmList()));
                if (CollectionUtil.isEmpty(flightUavBm)) {
                    LogUtil.error("获取飞行产品失败，关联的无人机品牌型号不存在: {}", flightProductDTO.getProductNo());
                    throw new BizException(BizErrorCode.PRODUCT_NOT_FOUND.getCode(), "关联的无人机品牌型号不存在");
                }
                flightProductDTO.setFlightUavBm(flightUavBm.get(0));
            }

            // 获取关联的价格列表
            List<PriceDTO> pricesByProductNo = priceService.getPricesByProductNo(flightProductDTO.getProductNo());
            flightProductDTO.setPriceList(pricesByProductNo);

            // 获取关联的无人机型号列表
            if (flightProductDTO.getFlightUavBmList() != null) {
                List<FlightUavBmDTO> flightUavBmDTOS = flightUavBmService.listFlightUavBm(Arrays.asList(flightProductDTO.getFlightUavBmList()));
                flightProductDTO.setFlightUavBmModelList(flightUavBmDTOS);
            }
        }

        return flightProductConvert.convertToVO(flightProductDTO);
    }

    @Override
    public CommonPage<FlightProductDTO> pageQueryFlightProduct(FlightProductQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<FlightProductDO> queryWrapper = Wrappers.lambdaQuery(FlightProductDO.class)
                .in(!CollectionUtils.isEmpty(queryDTO.getProductNoList()), FlightProductDO::getProductNo, queryDTO.getProductNoList())
                .eq(StringUtils.isNotBlank(queryDTO.getProductNo()), FlightProductDO::getProductNo, queryDTO.getProductNo())
                .like(StringUtils.isNotBlank(queryDTO.getProductName()), FlightProductDO::getProductName, queryDTO.getProductName())
                .eq(queryDTO.getProductType() != null, FlightProductDO::getProductType,
                        queryDTO.getProductType() != null ? queryDTO.getProductType().getType() : null)
                .eq(queryDTO.getProductServiceType() != null, FlightProductDO::getProductServiceType,
                        queryDTO.getProductServiceType() != null ? queryDTO.getProductServiceType().getCode() : null)
                .eq(StringUtils.isNotBlank(queryDTO.getCategoryNo()), FlightProductDO::getCategoryNo, queryDTO.getCategoryNo())
                .eq(StringUtils.isNotBlank(queryDTO.getServiceProviderNo()), FlightProductDO::getServiceProviderNo, queryDTO.getServiceProviderNo())
                .eq(StringUtils.isNotBlank(queryDTO.getProductStatus()), FlightProductDO::getProductStatus, queryDTO.getProductStatus())
                .eq(queryDTO.getProductShelfStatus() != null, FlightProductDO::getProductShelfStatus, queryDTO.getProductShelfStatus())
                .eq(queryDTO.getSupportUavPilot() != null, FlightProductDO::getSupportUavPilot, queryDTO.getSupportUavPilot())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), FlightProductDO::getTenantId, queryDTO.getTenantId())
                .orderByDesc(FlightProductDO::getGmtCreated);

        List<FlightProductDO> flightProductDOList = flightProductRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(flightProductDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<FlightProductDTO> flightProductDTOList = flightProductConvert.convertList(flightProductDOList);
        PageInfo<FlightProductDTO> pageInfo = new PageInfo<>(flightProductDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), flightProductDTOList);
    }

    @Override
    public CommonPage<FlightProductDTO> pageQueryFlightProductWithRelated(FlightProductQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 使用自定义的join查询方法
        List<FlightProductDO> flightProductDOList = ((FlightProductMapper) flightProductRepository.getBaseMapper()).pageQueryFlightProductWithJoin(queryDTO);
        
        if (CollectionUtils.isEmpty(flightProductDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<FlightProductDTO> flightProductDTOList = flightProductConvert.convertList(flightProductDOList);
        
        // 加载关联数据
        for (FlightProductDTO flightProductDTO : flightProductDTOList) {
            // 如果是机型服务产品，需要加载关联的无人机品牌型号信息
            if (flightProductDTO.getProductType() == FlightProductTypeEnum.FLIGHT_UAV) {
                if (flightProductDTO.getFlightUavBmList() != null && flightProductDTO.getFlightUavBmList().length > 0) {
                    // 通过productNo查找关联的无人机品牌型号
                    List<FlightUavBmDTO> flightUavBmList = flightUavBmService.listFlightUavBm(Arrays.asList(flightProductDTO.getFlightUavBmList()));
                    if (!CollectionUtil.isEmpty(flightUavBmList)) {
                        flightProductDTO.setFlightUavBm(flightUavBmList.get(0));
                    }
                }
            }

            // 获取关联的价格列表
            List<PriceDTO> pricesByProductNo = priceService.getPricesByProductNo(flightProductDTO.getProductNo());
            flightProductDTO.setPriceList(pricesByProductNo);
        }

        PageInfo<FlightProductDTO> pageInfo = new PageInfo<>(flightProductDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), flightProductDTOList);
    }

    @Override
    public List<FlightProductDTO> listByProductNoList(List<String> productNoList) {
        FlightProductQueryDTO flightProductQueryDTO = new FlightProductQueryDTO();
        flightProductQueryDTO.setProductNoList(productNoList);
        flightProductQueryDTO.setPageNum(1);
        flightProductQueryDTO.setPageSize(1000);
        CommonPage<FlightProductDTO> flightProductDTOCommonPage = pageQueryFlightProductWithRelated(flightProductQueryDTO);
        return flightProductDTOCommonPage.getList();
    }

    @Override
    public boolean deleteFlightProduct(Integer id) {
        if (id == null) {
            LogUtil.error("删除飞行产品失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查产品是否存在
        FlightProductDO existProduct = flightProductRepository.getById(id);
        if (existProduct == null) {
            LogUtil.error("删除飞行产品失败，产品不存在: {}", id);
            throw new BizException(BizErrorCode.PRODUCT_NOT_FOUND.getCode(), BizErrorCode.PRODUCT_NOT_FOUND.getDesc());
        }

        // 检查是否被订单关联使用
        long orderCount = orderProductUsageRepository.count(
                Wrappers.lambdaQuery(FlightOrderProductUsageDO.class)
                        .eq(FlightOrderProductUsageDO::getProductNo, existProduct.getProductNo())
                        .eq(FlightOrderProductUsageDO::getIsDeleted, false)
        );
        if (orderCount > 0) {
            LogUtil.error("删除飞行产品失败，该产品已被订单关联使用: {}", existProduct.getProductNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "该产品已被订单关联使用，不允许删除");
        }

        boolean success = flightProductRepository.removeById(id);
        if (!success) {
            LogUtil.error("删除飞行产品失败: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("删除飞行产品成功: {}", id);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFlightProductWithRelated(Integer id) {
        FlightProductDTO flightProductDTO = getFlightProductById(id);
        boolean success = false;

        if (flightProductDTO != null) {
            // 删除主产品
            success = deleteFlightProduct(id);


            // 删除关联的价格信息
            if (success) {
                priceService.deletePricesByProductNo(flightProductDTO.getProductNo());
            }
        }

        return success;
    }

    /**
     * 根据产品类型进行参数校验
     *
     * @param flightProductDTO 飞行产品DTO
     */
    private void validateProductParams(FlightProductDTO flightProductDTO) {
        if (flightProductDTO.getProductType() == null) {
            LogUtil.error("保存飞行产品失败，产品类型为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请选择产品类型");
        }
        
        switch (flightProductDTO.getProductType()) {
            case FLIGHT_UAV:
                // 机型服务产品校验 (只能添加一个机型)
                validateFlightUavProduct(flightProductDTO);
                break;
            case FLIGHT_SCENARIO:
                // 场景服务产品校验 (可以添加多个机型)
                validateFlightScenarioProduct(flightProductDTO);
                break;
            case FLIGHT_SERVICE:
                // 增值服务产品校验
                validateFlightServiceProduct(flightProductDTO);
                break;
            default:
                LogUtil.error("保存飞行产品失败，产品类型不支持: {}", flightProductDTO.getProductType());
                throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "不支持的产品类型");
        }
        
        // 通用产品参数校验
        validateCommonProductParams(flightProductDTO);
    }
    
    /**
     * 机型服务产品校验
     *
     * @param flightProductDTO 飞行产品DTO
     */
    private void validateFlightUavProduct(FlightProductDTO flightProductDTO) {
        // 机型服务只能添加一个机型
        if (flightProductDTO.getFlightUavBmList() == null || flightProductDTO.getFlightUavBmList().length == 0) {
            LogUtil.error("保存飞行产品失败，机型服务必须添加一个机型: {}", flightProductDTO.getProductNo());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "机型服务必须添加一个机型");
        }

        if (flightProductDTO.getFlightUavBmList().length > 1) {
            LogUtil.error("保存飞行产品失败，机型服务只能添加一个机型: {}", flightProductDTO.getProductNo());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "机型服务只能添加一个机型");
        }
    }
    
    /**
     * 场景服务产品校验
     *
     * @param flightProductDTO 飞行产品DTO
     */
    private void validateFlightScenarioProduct(FlightProductDTO flightProductDTO) {
        // 场景服务可以添加多个机型，但至少要添加一个机型
        if (flightProductDTO.getFlightUavBmList() == null || flightProductDTO.getFlightUavBmList().length == 0) {
            LogUtil.error("保存飞行产品失败，场景服务必须添加至少一个机型: {}", flightProductDTO.getProductNo());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "场景服务必须添加至少一个机型");
        }
        // 产品详情必填
        if (StringUtils.isBlank(flightProductDTO.getProductDetail())) {
            LogUtil.error("保存飞行产品失败，产品详情为空: {}", flightProductDTO.getProductNo());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入产品详情");
        }
        // 场景名称必填
        if (StringUtils.isBlank(flightProductDTO.getProductName())) {
            LogUtil.error("保存飞行产品失败，场景服务产品名称为空: {}", flightProductDTO.getProductNo());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入场景服务产品名称");
        }
    }
    
    /**
     * 增值服务产品校验
     *
     * @param flightProductDTO 飞行产品DTO
     */
    private void validateFlightServiceProduct(FlightProductDTO flightProductDTO) {
        // 增值服务产品名称必填
        if (StringUtils.isBlank(flightProductDTO.getProductName())) {
            LogUtil.error("保存飞行产品失败，增值服务产品名称为空: {}", flightProductDTO.getProductNo());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入增值服务产品名称");
        }
        // 产品内容必填
        if (StringUtils.isBlank(flightProductDTO.getProductContent())) {
            LogUtil.error("保存飞行产品失败，产品内容为空: {}", flightProductDTO.getProductContent());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入产品内容");
        }
        // 增值服务必须有价格
        if (flightProductDTO.getPriceList() == null || flightProductDTO.getPriceList().isEmpty()) {
            LogUtil.error("保存飞行产品失败，增值服务必须设置价格: {}", flightProductDTO.getProductNo());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请为增值服务设置价格");
        }
    }
    
    /**
     * 通用产品参数校验
     *
     * @param flightProductDTO 飞行产品DTO
     */
    private void validateCommonProductParams(FlightProductDTO flightProductDTO) {
        // 产品名称必填
        if (StringUtils.isBlank(flightProductDTO.getProductName())) {
            LogUtil.error("保存飞行产品失败，产品名称为空: {}", flightProductDTO.getProductNo());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入产品名称");
        }
        

    }

    @Override
    public FlightProductStatVO statCategoryFlightProduct(FlightProductStatQueryDTO queryDTO) {
        // 使用pageQueryFlightProductWithRelated方法获取所有产品数据
        FlightProductQueryDTO flightProductQueryDTO = new FlightProductQueryDTO();
        flightProductQueryDTO.setTenantId(queryDTO.getTenantId());
        flightProductQueryDTO.setProductType(FlightProductTypeEnum.FLIGHT_UAV);
        flightProductQueryDTO.setProductShelfStatus(ProductShelfStatusEnum.ONLINE);
        // 不分页，获取所有数据
        flightProductQueryDTO.setPageNum(1);
        flightProductQueryDTO.setPageSize(5000);
        
        CommonPage<FlightProductDTO> productPage = pageQueryFlightProductWithRelated(flightProductQueryDTO);
        List<FlightProductDTO> allProducts = productPage.getList();
        
        if (CollectionUtils.isEmpty(allProducts)) {
            return new FlightProductStatVO();
        }
        
        // 获取所有类目编号（从categoryNoList中提取）
        Set<String> categoryNos = allProducts.stream()
                .filter(p -> p.getCategoryNoList() != null && p.getCategoryNoList().length > 0)
                .flatMap(p -> Arrays.stream(p.getCategoryNoList()))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        
        // 如果没有从categoryNoList获取到类目，则尝试从传统的categoryNo字段获取（向后兼容）
        if (categoryNos.isEmpty()) {
            categoryNos = allProducts.stream()
                    .filter(p -> StringUtils.isNotBlank(p.getCategoryNo()))
                    .map(FlightProductDTO::getCategoryNo)
                    .collect(Collectors.toSet());
        }
        
        // 使用CategoryService查询类目信息
        List<CategoryDTO> validCategories = new ArrayList<>();
        try {
            if (categoryService != null && !categoryNos.isEmpty()) {
                // 创建查询条件，按类目编号查询
                CategoryQueryDTO categoryQueryDTO = new CategoryQueryDTO();
                categoryQueryDTO.setType(CategoryTypeEnum.FLIGHT.getCode());
                categoryQueryDTO.setPageNum(1);
                categoryQueryDTO.setPageSize(2000); // 设置足够大以获取所有匹配项
                
                // 查询有效类目
                CommonPage<CategoryDTO> categoryPage = categoryService.pageQueryCategory(categoryQueryDTO);
                if (categoryPage != null && !CollectionUtils.isEmpty(categoryPage.getList())) {
                    validCategories = categoryPage.getList();
                }
            }
        } catch (Exception e) {
            // 忽略查询异常，继续处理
        }
        
        // 提取有效的类目编号
        Set<String> validCategoryNos = validCategories.stream()
                .map(CategoryDTO::getCategoryNo)
                .collect(Collectors.toSet());
        
        // 如果没有从类目表获取到数据，则使用所有类目编号（兼容处理）
        if (validCategoryNos.isEmpty() && categoryService == null) {
            validCategoryNos = categoryNos;
        }
        
        // 创建产品到类目的映射关系（一个产品可能对应多个类目）
        Map<String, List<FlightProductDTO>> categoryToProductsMap = new HashMap<>();
        Set<String> finalValidCategoryNos = validCategoryNos;
        
        for (FlightProductDTO product : allProducts) {
            Set<String> productCategories = new HashSet<>();
            
            // 优先使用categoryNoList
            if (product.getCategoryNoList() != null && product.getCategoryNoList().length > 0) {
                for (String categoryNo : product.getCategoryNoList()) {
                    if (StringUtils.isNotBlank(categoryNo) && finalValidCategoryNos.contains(categoryNo)) {
                        productCategories.add(categoryNo);
                    }
                }
            }
            // 如果categoryNoList为空，则回退到传统的categoryNo字段（向后兼容）
//            else if (StringUtils.isNotBlank(product.getCategoryNo()) && finalValidCategoryNos.contains(product.getCategoryNo())) {
//                productCategories.add(product.getCategoryNo());
//            }
            
            // 将产品添加到对应的类目分组中
            for (String categoryNo : productCategories) {
                categoryToProductsMap.computeIfAbsent(categoryNo, k -> new ArrayList<>()).add(product);
            }
        }
        
        // 创建类目名称映射
        Map<String, String> categoryNameMap = validCategories.stream()
                .collect(Collectors.toMap(
                    CategoryDTO::getCategoryNo,
                    CategoryDTO::getCategoryName,
                    (v1, v2) -> v1
                ));
        
        List<FlightProductCategoryStatVO> result = new ArrayList<>();
        
        // 填充统计结果 - 统计所有类目
        for (Map.Entry<String, List<FlightProductDTO>> entry : categoryToProductsMap.entrySet()) {
            String categoryNo = entry.getKey();
            List<FlightProductDTO> products = entry.getValue();
            
            FlightProductCategoryStatVO statVO = new FlightProductCategoryStatVO();
            statVO.setCategoryNo(categoryNo);
            
            // 查找类目名称：优先使用从类目表获取的名称
            if (categoryNameMap.containsKey(categoryNo)) {
                statVO.setCategoryName(categoryNameMap.get(categoryNo));
            } else {
                // 若未从类目表获取到，尝试使用产品中的类目名称
                try {
                    if (!products.isEmpty() && StringUtils.isNotBlank(products.get(0).getCategoryName())) {
                        statVO.setCategoryName(products.get(0).getCategoryName());
                    } else {
                        statVO.setCategoryName(categoryNo);
                    }
                } catch (Exception e) {
                    statVO.setCategoryName(categoryNo);
                }
            }
            
            // 总数量
            statVO.setTotalCount((long) products.size());
            
            // 如果指定了类目编号且当前类目不是指定类目，则设置空的飞行方式列表
            if (StringUtils.isNotBlank(queryDTO.getCategoryNo()) && !queryDTO.getCategoryNo().equals(categoryNo)) {
                statVO.setFlyTypeList(Collections.emptyList());
                result.add(statVO);
                continue;
            }
            
            // 按飞行方式分组 (从flightUavBm中获取)
            Map<String, List<FlightProductDTO>> groupByFlyType = products.stream()
                    .filter(p -> p.getFlightUavBm() != null && p.getFlightUavBm().getFlightUavFlyType() != null)
                    .collect(Collectors.groupingBy(p -> p.getFlightUavBm().getFlightUavFlyType().getType()));
                    
            List<FlightProductCategoryStatVO.FlightFlyTypeStatVO> flyTypeList = new ArrayList<>();
            
            // 处理每个飞行方式组 - 统计所有飞行方式
            for (Map.Entry<String, List<FlightProductDTO>> flyTypeEntry : groupByFlyType.entrySet()) {
                String flyType = flyTypeEntry.getKey();
                List<FlightProductDTO> flyTypeProducts = flyTypeEntry.getValue();
                
                FlightProductCategoryStatVO.FlightFlyTypeStatVO flyTypeStatVO = new FlightProductCategoryStatVO.FlightFlyTypeStatVO();
                flyTypeStatVO.setFlightUavFlyType(flyType);
                flyTypeStatVO.setFlyTypeCount((long) flyTypeProducts.size());
                
                // 如果指定了飞行方式类型且当前飞行方式不是指定飞行方式，则设置空的品牌列表
                if (StringUtils.isNotBlank(queryDTO.getFlightUavFlyType()) && !queryDTO.getFlightUavFlyType().equals(flyType)) {
                    flyTypeStatVO.setBrandList(Collections.emptyList());
                    flyTypeList.add(flyTypeStatVO);
                    continue;
                }
                
                // 按品牌名分组 (从flightUavBm中获取)
                Map<String, List<FlightProductDTO>> groupByBrand = flyTypeProducts.stream()
                        .filter(p -> p.getFlightUavBm() != null && StringUtils.isNotBlank(p.getFlightUavBm().getFlightUavBmName()))
                        .collect(Collectors.groupingBy(p -> p.getFlightUavBm().getFlightUavBmName()));
                        
                List<FlightProductCategoryStatVO.FlightBrandStatVO> brandList = new ArrayList<>();
                
                // 处理每个品牌组 - 统计所有品牌
                for (Map.Entry<String, List<FlightProductDTO>> brandEntry : groupByBrand.entrySet()) {
                    String brandName = brandEntry.getKey();
                    List<FlightProductDTO> brandProducts = brandEntry.getValue();
                    
                    // 如果指定了品牌名称且当前品牌不是指定品牌，则跳过
                    if (StringUtils.isNotBlank(queryDTO.getFlightUavBmName()) && !queryDTO.getFlightUavBmName().equals(brandName)) {
                        continue;
                    }
                    
                    FlightProductCategoryStatVO.FlightBrandStatVO brandStatVO = new FlightProductCategoryStatVO.FlightBrandStatVO();
                    brandStatVO.setFlightUavBmName(brandName);
                    brandStatVO.setBrandCount((long) brandProducts.size());
                    
                    brandList.add(brandStatVO);
                }
                
                flyTypeStatVO.setBrandList(brandList);
                flyTypeList.add(flyTypeStatVO);
            }
            
            statVO.setFlyTypeList(flyTypeList);
            result.add(statVO);
        }
        
        // 统计全局飞行方式和品牌信息
        List<FlightProductCategoryStatVO.FlightFlyTypeStatVO> globalFlyTypeList = generateGlobalFlyTypeStats(allProducts, queryDTO);
        
        FlightProductStatVO flightProductStatVO = new FlightProductStatVO();
        flightProductStatVO.setTotalCount((long) allProducts.size());
        flightProductStatVO.setCategoryStatList(result);
        flightProductStatVO.setFlyTypeList(globalFlyTypeList);

        return flightProductStatVO;
    }

    /**
     * 生成全局飞行方式统计信息
     *
     * @param allProducts 所有产品
     * @param queryDTO 查询条件
     * @return 飞行方式统计列表
     */
    private List<FlightProductCategoryStatVO.FlightFlyTypeStatVO> generateGlobalFlyTypeStats(
            List<FlightProductDTO> allProducts, FlightProductStatQueryDTO queryDTO) {
        
        if (CollectionUtils.isEmpty(allProducts)) {
            return Collections.emptyList();
        }
        
        // 按飞行方式分组统计所有产品
        Map<String, List<FlightProductDTO>> globalGroupByFlyType = allProducts.stream()
                .filter(p -> p.getFlightUavBm() != null && p.getFlightUavBm().getFlightUavFlyType() != null)
                .collect(Collectors.groupingBy(p -> p.getFlightUavBm().getFlightUavFlyType().getType()));
        
        List<FlightProductCategoryStatVO.FlightFlyTypeStatVO> globalFlyTypeList = new ArrayList<>();
        
        // 处理每个飞行方式组
        for (Map.Entry<String, List<FlightProductDTO>> flyTypeEntry : globalGroupByFlyType.entrySet()) {
            String flyType = flyTypeEntry.getKey();
            List<FlightProductDTO> flyTypeProducts = flyTypeEntry.getValue();
            
            // 如果指定了飞行方式类型且当前飞行方式不是指定飞行方式，则跳过
            if (StringUtils.isNotBlank(queryDTO.getFlightUavFlyType()) && !queryDTO.getFlightUavFlyType().equals(flyType)) {
                continue;
            }
            
            FlightProductCategoryStatVO.FlightFlyTypeStatVO flyTypeStatVO = new FlightProductCategoryStatVO.FlightFlyTypeStatVO();
            flyTypeStatVO.setFlightUavFlyType(flyType);
            flyTypeStatVO.setFlyTypeCount((long) flyTypeProducts.size());
            
            // 按品牌名分组统计该飞行方式下的品牌
            Map<String, List<FlightProductDTO>> groupByBrand = flyTypeProducts.stream()
                    .filter(p -> p.getFlightUavBm() != null && StringUtils.isNotBlank(p.getFlightUavBm().getFlightUavBmName()))
                    .collect(Collectors.groupingBy(p -> p.getFlightUavBm().getFlightUavBmName()));
            
            List<FlightProductCategoryStatVO.FlightBrandStatVO> brandList = new ArrayList<>();
            
            // 处理每个品牌组
            for (Map.Entry<String, List<FlightProductDTO>> brandEntry : groupByBrand.entrySet()) {
                String brandName = brandEntry.getKey();
                List<FlightProductDTO> brandProducts = brandEntry.getValue();
                
                // 如果指定了品牌名称且当前品牌不是指定品牌，则跳过
                if (StringUtils.isNotBlank(queryDTO.getFlightUavBmName()) && !queryDTO.getFlightUavBmName().equals(brandName)) {
                    continue;
                }
                
                FlightProductCategoryStatVO.FlightBrandStatVO brandStatVO = new FlightProductCategoryStatVO.FlightBrandStatVO();
                brandStatVO.setFlightUavBmName(brandName);
                brandStatVO.setBrandCount((long) brandProducts.size());
                
                brandList.add(brandStatVO);
            }
            
            flyTypeStatVO.setBrandList(brandList);
            globalFlyTypeList.add(flyTypeStatVO);
        }
        
        return globalFlyTypeList;
    }

    @Override
    public boolean batchUpdateProductShelfStatus(List<Integer> productIds, ProductShelfStatusEnum shelfStatus) {
        if (CollectionUtils.isEmpty(productIds) || shelfStatus == null) {
            return false;
        }

        // 批量更新产品上下架状态
        FlightProductDO updateDO = new FlightProductDO();
        updateDO.setProductShelfStatus(shelfStatus);
        updateDO.setGmtModified(LocalDateTime.now());

        boolean success = flightProductRepository.update(
                updateDO,
                Wrappers.lambdaQuery(FlightProductDO.class)
                        .in(FlightProductDO::getId, productIds)
        );

        return success;
    }

    @Override
    public boolean onlineProduct(Integer productId) {
        if (productId == null) {
            return false;
        }

        // 检查产品是否存在
        FlightProductDO existProduct = flightProductRepository.getById(productId);
        if (existProduct == null) {
            return false;
        }

        // 更新为已上架状态
        FlightProductDO updateDO = new FlightProductDO();
        updateDO.setId(productId);
        updateDO.setProductShelfStatus(ProductShelfStatusEnum.ONLINE);
        updateDO.setGmtModified(LocalDateTime.now());

        return flightProductRepository.updateById(updateDO);
    }

    @Override
    public boolean offlineProduct(Integer productId) {
        if (productId == null) {
            return false;
        }

        // 检查产品是否存在
        FlightProductDO existProduct = flightProductRepository.getById(productId);
        if (existProduct == null) {
            return false;
        }

        // 更新为待上架状态
        FlightProductDO updateDO = new FlightProductDO();
        updateDO.setId(productId);
        updateDO.setProductShelfStatus(ProductShelfStatusEnum.PENDING);
        updateDO.setGmtModified(LocalDateTime.now());

        return flightProductRepository.updateById(updateDO);
    }
} 