package com.deepinnet.skyflow.operationcenter.service.client;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dal.anno.AccountQuery;
import com.deepinnet.spatiotemporalplatform.client.skyflow.FlightEventClient;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsStatDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsStatQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.RealTimeUavFlightVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Creator zengjuerui
 * Date 2025-06-11
 **/

@Service
public class FlightEventRemoteClient {

    @Resource
    private FlightEventClient flightEventClient;


    @AccountQuery
    public List<FlightEventsStatDTO> queryFlightEventsStat(FlightEventsStatQueryDTO queryDTO) {
        Result<List<FlightEventsStatDTO>> result = flightEventClient.queryFlightEventsStat(queryDTO);

        if (!result.isSuccess()) {
            LogUtil.error("queryFlightEventsStat error:{}", result.getErrorDesc());
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }
}
