package com.deepinnet.skyflow.operationcenter.service.draft.impl;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDraftDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDraftRepository;
import com.deepinnet.skyflow.operationcenter.dto.FlightDraftDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDraftQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightDraftConvert;
import com.deepinnet.skyflow.operationcenter.service.draft.FlightDraftService;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 飞行草稿服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Service
public class FlightDraftServiceImpl implements FlightDraftService {

    @Resource
    private FlightDraftRepository flightDraftRepository;

    @Resource
    private FlightDraftConvert flightDraftConvert;

    @Override
    public String saveDraft(FlightDraftDTO flightDraftDTO) {
        String code = IdGenerateUtil.getId("flight_draft");
        flightDraftDTO.setCode(code);
        FlightDraftDO flightDraftDO = flightDraftConvert.convertToEntity(flightDraftDTO);
        flightDraftRepository.saveDraft(flightDraftDO);
        return code;
    }

    @Override
    public void updateDraft(FlightDraftDTO flightDraftDTO) {
        if (StringUtils.isBlank(flightDraftDTO.getCode())) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "更新草稿失败，code不能为空");
        }
        
        FlightDraftDO flightDraftDO = flightDraftConvert.convertToEntity(flightDraftDTO);
        flightDraftRepository.updateDraft(flightDraftDO);
    }

    @Override
    public boolean removeDraft(String code) {
        return flightDraftRepository.removeDraft(code);
    }

    @Override
    public FlightDraftDTO getDraft(String code) {
        FlightDraftDO flightDraftDO = flightDraftRepository.getDraft(code);
        return flightDraftDO != null ? flightDraftConvert.convertToDTO(flightDraftDO) : null;
    }

    @Override
    public CommonPage<FlightDraftDTO> pageDrafts(FlightDraftQueryDTO queryDTO) {
        Page<FlightDraftDO> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<FlightDraftDO> resultPage = flightDraftRepository.listDrafts(queryDTO);
        
        List<FlightDraftDTO> dtoList = resultPage.stream()
                .map(flightDraftDO -> flightDraftConvert.convertToDTO(flightDraftDO))
                .collect(Collectors.toList());
        
        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), page.getPages(), page.getTotal(), dtoList);
    }
}