package com.deepinnet.skyflow.operationcenter.service.norepeat.resolve;

import cn.hutool.core.util.ObjectUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

@Component
public class ReqHeaderParamBizKeyResolver implements BizKeyResolver {
    @Override
    public Object resolve(String key) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (ObjectUtil.isNull(requestAttributes)) {
            return null;
        }

        return requestAttributes.getAttribute(key, RequestAttributes.SCOPE_REQUEST);
    }
}
