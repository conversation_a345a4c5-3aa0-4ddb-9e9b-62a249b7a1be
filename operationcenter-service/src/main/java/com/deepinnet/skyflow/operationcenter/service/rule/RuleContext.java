package com.deepinnet.skyflow.operationcenter.service.rule;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Description:
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Data
public class RuleContext<T extends RuleResult> {

    /**
     * 每个规则的执行结果
     */
    private Map<Class<? extends IRule>, T> ruleResultMap = new HashMap<>();

    /**
     * 规则引擎执行的入参
     */
    private Object inputParam;

    /**
     * 上一个规则的执行结果
     */
    private T previousRuleExecResult;

    /**
     * 规则引擎执行过程中，规则引擎需要存储的属性
     */
    private Map<String, Object> attrMap = new HashMap<>();

    private String tenantId;

    /**
     * 当前可执行的规则的索引下标
     */
    private Integer ruleIndex;

    public void addRuleExecResult(Class<? extends IRule> ruleClass, T ruleResult) {
        ruleResultMap.put(ruleClass, ruleResult);
    }
}

