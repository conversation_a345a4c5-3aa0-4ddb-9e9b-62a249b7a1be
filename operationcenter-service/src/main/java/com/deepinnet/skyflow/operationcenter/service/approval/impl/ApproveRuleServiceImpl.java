package com.deepinnet.skyflow.operationcenter.service.approval.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.infra.api.dto.SimpleDepartmentDTO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalConfigRuleDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.ApprovalConfigRuleRepository;
import com.deepinnet.skyflow.operationcenter.dto.ApprovalConfigRuleQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveRuleService;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.*;
import com.deepinnet.skyflow.operationcenter.service.approval.instance.ApproveConvert;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyDispatcher;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Description: 审批规则服务实现类
 * Date: 2025/7/21
 * Author: lijunheng
 */
@Slf4j
@Service
public class ApproveRuleServiceImpl implements ApproveRuleService {

    @Resource
    private StrategyDispatcher strategyDispatcher;

    @Resource
    private ApproveHelper approveHelper;

    @Resource
    private UserRemoteClient userService;

    @Resource
    private ApprovalConfigRuleRepository approvalConfigRuleRepository;

    @Resource
    private ApproveConvert approveConvert;

    @Override
    public String createApproveRule(ApprovalInstanceConfigRule approvalInstanceConfigRule) {
        String configRuleCode = IdGenerateUtil.getId("approval_rule");
        approvalInstanceConfigRule.setCode(configRuleCode);
        ApprovalConfigRuleDO ruleDO = approveConvert.toDO(approvalInstanceConfigRule);
        ruleDO.setGmtCreated(LocalDateTime.now());
        ruleDO.setGmtModified(LocalDateTime.now());
        approvalConfigRuleRepository.save(ruleDO);
        log.info("创建审批规则成功，规则编码: {}", approvalInstanceConfigRule.getCode());
        return configRuleCode;
    }

    @Override
    public Boolean updateApproveRule(ApprovalInstanceConfigRule approvalInstanceConfigRule) {
        // 更新现有规则
        ApprovalConfigRuleDO configRuleDO = approveConvert.toDO(approvalInstanceConfigRule);
        boolean update = approvalConfigRuleRepository.update(configRuleDO,
                Wrappers.lambdaUpdate(ApprovalConfigRuleDO.class)
                        .eq(ApprovalConfigRuleDO::getCode, configRuleDO.getCode()));
        log.info("更新审批规则成功，规则编码: {}", approvalInstanceConfigRule.getCode());
        return update;
    }

    @Override
    public ApprovalInstanceConfigRule getApproveRule(String configRuleCode) {
        ApprovalConfigRuleDO existingRule = approvalConfigRuleRepository.getOne(Wrappers.lambdaQuery(ApprovalConfigRuleDO.class)
                .eq(ApprovalConfigRuleDO::getCode, configRuleCode));
        return approveConvert.toEntity(existingRule);
    }

    @Override
    public ApprovalInstanceConfigRule getApproveRule(String userNo, String ruleType) {
        ApprovalConfigRuleQueryDTO queryDTO = new ApprovalConfigRuleQueryDTO();
        List<ApprovalInstanceConfigRule> list = this.getApprovalConfigRuleList(queryDTO).getList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public CommonPage<ApprovalInstanceConfigRule> getApprovalConfigRuleList(ApprovalConfigRuleQueryDTO queryDTO) {
        if (StrUtil.isNotBlank(queryDTO.getUserNo())) {
            //查询用户的一级部门组织编号
            SimpleDepartmentDTO departmentDTO = userService.getUserRelatedDepartment(queryDTO.getUserNo());
            queryDTO.setOrgCode(String.valueOf(departmentDTO.getFullPath().get(0)));
        }
        Page<ApprovalConfigRuleDO> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        LambdaQueryWrapper<ApprovalConfigRuleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(queryDTO.getCode()), ApprovalConfigRuleDO::getCode, queryDTO.getCode());
        queryWrapper.like(StrUtil.isNotBlank(queryDTO.getRuleName()), ApprovalConfigRuleDO::getRuleName, queryDTO);
        queryWrapper.eq(StrUtil.isNotBlank(queryDTO.getRuleType()), ApprovalConfigRuleDO::getRuleType, queryDTO.getRuleType());
        queryWrapper.eq(StrUtil.isNotBlank(queryDTO.getOrgCode()), ApprovalConfigRuleDO::getOrgCode, queryDTO.getOrgCode());
        queryWrapper.orderByDesc(ApprovalConfigRuleDO::getGmtModified);
        List<ApprovalConfigRuleDO> configRuleDOList = approvalConfigRuleRepository.list(queryWrapper);
        List<ApprovalInstanceConfigRule> resultList = approveConvert.toEntityList(configRuleDOList);

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), page.getPages(), page.getTotal(), resultList);
    }

    @Override
    public ApprovalInstance executeApproveRule(ApproveRuleExecEntity approveRuleExecEntity) {
        String instanceConfigRuleCode = approveRuleExecEntity.getInstanceConfigRuleCode();
        if (instanceConfigRuleCode == null || approveRuleExecEntity.getApproveSubmitEntity().getSubmitUserId() == null) {
            throw new IllegalArgumentException("规则编码和提交用户ID不能为空");
        }

        // 检查规则编码是否已存在
        ApprovalInstanceConfigRule configRule = this.getApproveRule(instanceConfigRuleCode);

        // 创建审批实例
        ApprovalInstance approvalInstance = createApprovalInstance(approveRuleExecEntity);

        // 生成审批步骤
        List<ApprovalStep> steps = generateApprovalSteps(configRule, approveRuleExecEntity);
        approvalInstance.setSteps(steps);

        log.info("执行审批规则成功，审批实例ID: {}, 步骤数: {}",
                approvalInstance.getApprovalId(), steps.size());

        return approvalInstance;
    }

    /**
     * 创建审批实例
     */
    private ApprovalInstance createApprovalInstance(ApproveRuleExecEntity approveRuleExecEntity) {
        ApproveSubmitEntity submitEntity = approveRuleExecEntity.getApproveSubmitEntity();
        ApprovalInstance instance = new ApprovalInstance();
        instance.setBizType(submitEntity.getBizType());
        instance.setBizId(submitEntity.getBizId());
        instance.setSubmitUserId(submitEntity.getSubmitUserId());

        SimpleDepartmentDTO simpleDepartmentDTO = userService.getUserRelatedDepartment(submitEntity.getSubmitUserId());
        instance.setSubmitUserName(simpleDepartmentDTO.getUserName());
        instance.setSubmitDepartmentId(String.valueOf(simpleDepartmentDTO.getId()));
        instance.setSubmitDepartmentName(simpleDepartmentDTO.getName());
        instance.setSubmitUserPhone(simpleDepartmentDTO.getMemberPhone());
        instance.setTenantId(submitEntity.getTenantId());

        instance.setApplyReason(submitEntity.getApplyReason());
        return instance;
    }

    /**
     * 生成审批步骤
     */
    private List<ApprovalStep> generateApprovalSteps(ApprovalInstanceConfigRule configRule, ApproveRuleExecEntity approveRuleExecEntity) {
        List<ApprovalChooseConfigRule> chooseConfigs = configRule.getApprovalChooseConfigRuleList();

        if (chooseConfigs == null || chooseConfigs.isEmpty()) {
            log.warn("审批选择配置为空");
            throw new IllegalArgumentException("审批模板规则未正确配置");
        }

        String submitUserId = approveRuleExecEntity.getApproveSubmitEntity().getSubmitUserId();
        List<ApprovalStep> steps = new ArrayList<>();
        for (ApprovalChooseConfigRule chooseConfig : chooseConfigs) {
            List<ApprovalStep> supervisorSteps = strategyDispatcher.chooseApproves(chooseConfig.getChooseStrategy(), chooseConfig, submitUserId, configRule);
            if (CollectionUtils.isNotEmpty(supervisorSteps)) {
                steps.addAll(supervisorSteps);
            } else {
                //走审批人为空时的策略
                steps.addAll(handleEmptyApproversSteps(chooseConfig, submitUserId));
            }

        }

        return steps;
    }

    /**
     * 处理没有找到审批人的情况
     */
    private List<ApprovalStep> handleEmptyApproversSteps(ApprovalChooseConfigRule chooseConfig, String submitUserId) {
        List<ApprovalStep> steps = new ArrayList<>();

        ApprovalStep step = new ApprovalStep();

        step.setApproveMode(chooseConfig.getApproveMode());

        step.setApprovalNodeList(handleEmptyApprovers(chooseConfig, submitUserId));
        steps.add(step);

        return steps;
    }

    /**
     * 处理审批人为空的情况
     */
    private List<ApprovalNode> handleEmptyApprovers(ApprovalChooseConfigRule chooseConfig, String submitUserId) {
        List<ApprovalNode> approvalNodes = new ArrayList<>();

        switch (chooseConfig.getIfEmptyStrategy()) {
            case AUTO_PASS:
                log.info("审批人为空，自动通过");
                // 创建一个系统自动通过节点
                ApprovalNode autoPassNode = approveHelper.createApprovalNode(ApproveHelper.SYSTEM);
                autoPassNode.setAutoPass(true);
                approvalNodes.add(autoPassNode);
                break;

            case AUTO_REJECT:
                log.info("审批人为空，自动拒绝");
                // 创建一个系统自动拒绝节点
                ApprovalNode autoRejectNode = approveHelper.createApprovalNode(ApproveHelper.SYSTEM);
                autoRejectNode.setAutoReject(true);
                approvalNodes.add(autoRejectNode);
                break;

            default:
                log.warn("未知的空审批人处理策略: {}", chooseConfig.getIfEmptyStrategy());
                break;
        }

        return approvalNodes;
    }
}
