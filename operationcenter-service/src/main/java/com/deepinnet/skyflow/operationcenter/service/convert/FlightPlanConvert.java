package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandPlanDTO;
import org.mapstruct.*;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * C<PERSON> zengjuerui
 * Date 2025-05-28
 **/

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface FlightPlanConvert {

    @Mappings({
            @Mapping(source = "planedTakeoffTime", target = "planedTakeoffTime", qualifiedByName = "toLocalDateTime"),
            @Mapping(source = "planedLandingTime", target = "planedLandingTime", qualifiedByName = "toLocalDateTime"),
    })
    FlightDemandPlanDTO toFlightPlanDTO(FlightPlanDO flightPlanDO);

    List<FlightDemandPlanDTO> toFlightPlanDTOList(List<FlightPlanDO> list);


    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(Long timestamp) {
        return timestamp == null ? null : LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }
}
