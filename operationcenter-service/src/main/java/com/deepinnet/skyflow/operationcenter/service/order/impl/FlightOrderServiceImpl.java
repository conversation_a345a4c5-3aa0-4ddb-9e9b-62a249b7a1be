package com.deepinnet.skyflow.operationcenter.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.DataAccessDTO;
import com.deepinnet.infra.api.dto.UserDetailDTO;
import com.deepinnet.infra.api.dto.WholeDepartmentDTO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.*;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightPlanPageQuery;
import com.deepinnet.skyflow.operationcenter.dal.repository.*;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.enums.FlightOrderApproveStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightProductTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveInstanceService;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalInstance;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalNode;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalRecord;
import com.deepinnet.skyflow.operationcenter.service.approval.instance.ApproveConvert;
import com.deepinnet.skyflow.operationcenter.service.client.FlightPlanQueryClient;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightConverter;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.flow.manager.LiteFlowManager;
import com.deepinnet.skyflow.operationcenter.service.order.FlightOrderService;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightOrderConvert;
import com.deepinnet.skyflow.operationcenter.service.helper.FlightOrderProductUsageHelper;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.util.enums.UserTypeEnum;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightPositionVO;
import com.deepinnet.spatiotemporalplatform.dto.BatchQueryPlanDTO;
import com.deepinnet.spatiotemporalplatform.vo.AerodromeVO;
import com.deepinnet.spatiotemporalplatform.vo.FlightCountVO;
import com.deepinnet.spatiotemporalplatform.vo.FlightPlanVO;
import com.deepinnet.tenant.TenantIdUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 飞行订单服务实现类
 *
 * <AUTHOR>
 */
@Service
public class FlightOrderServiceImpl implements FlightOrderService {

    @Resource
    private FlightOrderRepository flightOrderRepository;

    @Resource
    private FlightPlanRepository flightPlanRepository;

    @Resource
    private FlightOrderConvert flightOrderConvert;

    @Resource
    private FlightConverter flightConverter;

    @Resource
    private FlightOrderProductUsageHelper flightOrderProductUsageHelper;

    @Resource
    private LiteFlowManager liteFlowManager;

    @Resource
    private FlightPlanQueryClient flightPlanQueryClient;
    
    @Resource
    private FlightDemandRepository flightDemandRepository;
    
    @Resource
    private FlightOrderProductUsageRepository flightOrderProductUsageRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private UserRemoteClient userRemoteClient;

    @Resource
    private FlightMergeDemandRelationRepository mergeDemandRelationRepository;

    @Resource
    private ApproveInstanceService approveInstanceService;

    @Resource
    private ApproveConvert approveConvert;
    @Autowired
    private FlightPositionRepository flightPositionRepository;

    @Override
    public FlightOrderVO getFlightOrderByNo(OrderQueryDTO dto) {
        Assert.notNull(dto, "订单查询参数不能为空!");
        Assert.notNull(dto.getOrderType(), "订单类型不能为空!");
        Assert.hasText(dto.getOrderNo(), "请输入订单编号!");
        Assert.hasText(dto.getTenantId(), "租户不能为空!");
        Assert.notNull(OrderTypeEnum.getEnumByCode(dto.getOrderType()), "请传入正确的订单类型!");

        FlightOrderDO flightOrderDO = flightOrderRepository.getOne(
                Wrappers.lambdaQuery(FlightOrderDO.class)
                        .eq(FlightOrderDO::getOrderNo, dto.getOrderNo())
                        .in(CollUtil.isNotEmpty(dto.getUserNo()), FlightOrderDO::getUserNo, dto.getUserNo())
                        .eq(FlightOrderDO::getTenantId, dto.getTenantId())
                        .eq(FlightOrderDO::getOrderType, dto.getOrderType()));

        if (ObjectUtil.isNull(flightOrderDO)) {
            LogUtil.error("获取飞行订单失败，订单不存在: {}, tenantId:{}, userNo:{}", dto.getOrderNo(), dto.getTenantId(), dto.getUserNo());
            throw new BizException(BizErrorCode.ORDER_NOT_FOUND.getCode(), BizErrorCode.ORDER_NOT_FOUND.getDesc());
        }

        FlightOrderVO flightOrderVO = flightOrderConvert.convertToVO(flightOrderDO);

        flightOrderProductUsageHelper.setProductUsageAndProductInfo(flightOrderVO, dto.getOrderNo(), dto.getTenantId());

        List<UserDetailDTO> userDetailList = userRemoteClient.batchQueryUser(Lists.newArrayList(flightOrderDO.getUserNo()));

        if (CollUtil.isNotEmpty(userDetailList)) {
            flightOrderVO.setUserName(userDetailList.get(0).getUserName());
        }

        List<ApprovalInstance> approvalInstances = approveInstanceService.getNeedApproveInstanceList(List.of(flightOrderVO.getOrderNo()));

        if (CollUtil.isNotEmpty(approvalInstances)) {
            ApprovalInstance approvalInstance = approvalInstances.get(0);

            List<ApprovalNode> approvalNodeList = approveInstanceService.getCurrentNeedToApproveNodeList(approvalInstance.getApprovalId());

            flightOrderVO.setAllowApprove(approvalNodeList.stream().anyMatch(approvalNode -> Objects.equals(approvalNode.getApproveUserId(), UserUtil.getUserNo())));
        }

        List<ApprovalRecord> approvalRecordList = approveInstanceService.getApprovalRecordMap(flightOrderVO.getOrderNo());

        flightOrderVO.setApprovalRecords(approveConvert.toApprovalRecordVO(approvalRecordList));

        boolean isEdit = (StrUtil.equals(flightOrderVO.getStatus(), OrderStatusEnum.APPROVING.getCode())
                || StrUtil.equals(flightOrderVO.getApproveStatus(), FlightOrderApproveStatusEnum.REJECTED.getStatusCode()))
                && StrUtil.equals(UserUtil.getUserNo(), flightOrderVO.getUserNo());

        flightOrderVO.setAllowEdit(isEdit);

        return flightOrderVO;
    }

    private List<String> getAvailableUsers(OrderTypeEnum orderTypeEnum) {
        DataAccessDTO availableQueryData = userRemoteClient.getAvailableQueryData();

        if (ObjectUtil.isNull(availableQueryData)) {
            LogUtil.warn("当前暂无可用用户");
            return Lists.newArrayList();
        }

        List<String> userNos = Lists.newArrayList();

        // 需求计划订单塞入 用户编号 ｜ 部门编号
        if (ObjectUtil.equals(orderTypeEnum, OrderTypeEnum.DEMAND_PLAN)
                && CollUtil.isNotEmpty(availableQueryData.getSupportQueryUserNos())
                && !availableQueryData.getSupportQueryUserNos().contains(DataScopeConstants.SUPER_ADMIN)
                && ObjectUtil.notEqual(UserTypeEnum.OPERATION, UserUtil.getUserType())) {
            userNos.addAll(availableQueryData.getSupportQueryUserNos());
        }

        return userNos;
    }

    private List<String> getAvailableUsers() {
        DataAccessDTO availableQueryData = userRemoteClient.getAvailableQueryData();

        if (ObjectUtil.isNull(availableQueryData)) {
            LogUtil.warn("当前暂无可用用户");
            return Lists.newArrayList();
        }

        List<String> userNos = Lists.newArrayList();

        // 需求计划订单塞入 用户编号 ｜ 部门编号
        if (CollUtil.isNotEmpty(availableQueryData.getSupportQueryUserNos())
                && !availableQueryData.getSupportQueryUserNos().contains(DataScopeConstants.SUPER_ADMIN)
                && ObjectUtil.notEqual(UserTypeEnum.OPERATION, UserUtil.getUserType())) {
            userNos.addAll(availableQueryData.getSupportQueryUserNos());
        }

        return userNos;
    }

    @Override
    public CommonPage<FlightOrderVO> getFlightOrderListByPage(OrderPageQueryDTO dto) {
        Assert.notNull(dto, "订单列表查询参数不能为空!");
        Assert.hasText(dto.getTenantId(), "租户ID不能为空!");
        Assert.notNull(OrderTypeEnum.getEnumByCode(dto.getOrderType()), "请传入正确的订单类型!");

        List<String> users = Lists.newArrayList();

        // 普通订单塞入用户编号
        if (ObjectUtil.equals(dto.getOrderType(), OrderTypeEnum.NORMAL.getCode())
                && (ObjectUtil.equals(UserTypeEnum.CUSTOMER, UserUtil.getUserType()) || ObjectUtil.equals(UserTypeEnum.SUPPLIER, UserUtil.getUserType()))) {
            users.add(UserUtil.getUserNo());
        }

        users.addAll(getAvailableUsers(OrderTypeEnum.getEnumByCode(dto.getOrderType())));

        OrderStatusEnum statusEnum = OrderStatusEnum.getByCode(dto.getStatus());
        if (ObjectUtil.isNull(statusEnum)) {
            dto.setStatus(null);
        }


        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        List<FlightOrderDO> flightOrderDOList = flightOrderRepository.list(
                Wrappers.lambdaQuery(FlightOrderDO.class)
                        .eq(StrUtil.isNotEmpty(dto.getOrderNo()), FlightOrderDO::getOrderNo, dto.getOrderNo())
                        .in(CollUtil.isNotEmpty(users), FlightOrderDO::getUserNo, users)
                        .eq(StrUtil.isNotEmpty(dto.getCustomerId()), FlightOrderDO::getCustomerId, dto.getCustomerId())
                        .eq(StrUtil.isNotEmpty(dto.getStatus()), FlightOrderDO::getStatus, dto.getStatus())
                        .eq(StrUtil.isNotEmpty(dto.getCategoryNo()), FlightOrderDO::getCategoryNo, dto.getCategoryNo())
                        .like(StrUtil.isNotEmpty(dto.getServiceName()), FlightOrderDO::getProductName, dto.getServiceName())
                        .like(StrUtil.isNotEmpty(dto.getOrganizationName()), FlightOrderDO::getOrganizationName, dto.getOrganizationName())
                        .like(StrUtil.isNotEmpty(dto.getCycleName()), FlightOrderDO::getCycleName, dto.getCycleName())
                        .eq(StrUtil.isNotEmpty(dto.getCycleNo()), FlightOrderDO::getCycleNo, dto.getCycleNo())
                        .eq(FlightOrderDO::getTenantId, dto.getTenantId())
                        .eq(FlightOrderDO::getOrderType, dto.getOrderType())
                        .orderByDesc(FlightOrderDO::getOrderTime)
                        .orderByDesc(FlightOrderDO::getId));

        List<FlightOrderVO> flightOrderList = Collections.emptyList();

        if (CollUtil.isEmpty(flightOrderDOList)) {
            return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), flightOrderList);
        }

        flightOrderList = flightOrderConvert.convertToFlightOrderVOList(flightOrderDOList);

        flightOrderProductUsageHelper.batchSetProductUsageAndProductInfo(flightOrderList, dto.getTenantId());

        List<UserDetailDTO> userDetailList = userRemoteClient.batchQueryUser(flightOrderList.stream().map(FlightOrderVO::getUserNo).collect(Collectors.toList()));

        if (CollUtil.isEmpty(userDetailList)) {
            return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), flightOrderList);
        }

        Map<String, UserDetailDTO> userDetailMap = userDetailList.stream().collect(Collectors.toMap(UserDetailDTO::getUserNo, Function.identity(), (k1, k2) -> k2));
        
        flightOrderList.forEach(e -> e.setUserName(ObjectUtil.isNull(userDetailMap.get(e.getUserNo())) ? null : userDetailMap.get(e.getUserNo()).getUserName()));

        List<String> orders = flightOrderList.stream().map(FlightOrderVO::getOrderNo).collect(Collectors.toList());
        // 判断当前用户是否有权限编辑
        List<ApprovalInstance> approvalInstanceList = approveInstanceService.getNeedApproveInstanceList(orders);

        if (CollUtil.isEmpty(approvalInstanceList)) {
            return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), flightOrderList);
        }

        Map<String, List<ApprovalNode>> approvalNodeMap = approveInstanceService
                .getCurrentNeedToApproveNodeMap(approvalInstanceList.stream()
                        .map(ApprovalInstance::getApprovalId).collect(Collectors.toList()));

        if (CollUtil.isEmpty(approvalNodeMap)) {
            return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), flightOrderList);
        }

        String currentUserNo = UserUtil.getUserNo();
        Set<String> allowedOrderNos = approvalInstanceList.stream()
                .filter(instance -> {
                    List<ApprovalNode> nodes = approvalNodeMap.get(instance.getApprovalId());
                    return CollUtil.isNotEmpty(nodes) && nodes.stream()
                            .anyMatch(n -> Objects.equals(n.getApproveUserId(), currentUserNo));
                })
                .map(ApprovalInstance::getBizId)
                .collect(Collectors.toSet());

        flightOrderList.forEach(order -> {
            boolean isEdit = (StrUtil.equals(order.getStatus(), OrderStatusEnum.APPROVING.getCode())
                    || StrUtil.equals(order.getApproveStatus(), FlightOrderApproveStatusEnum.REJECTED.getStatusCode()))
                    && StrUtil.equals(UserUtil.getUserNo(), order.getUserNo());
            order.setAllowEdit(isEdit);
            order.setAllowApprove(allowedOrderNos.contains(order.getOrderNo()));
        });

        return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), flightOrderList);
    }

    @Override
    public Boolean orderApprove(OrderApproveDTO dto) {

        Assert.notNull(dto, "审批参数不能为空!");
        Assert.notNull(OrderTypeEnum.getEnumByCode(dto.getOrderType()), "订单类型非法!");
        Assert.hasText(dto.getOrderNo(), "订单编号不能为空!");
        Assert.hasText(dto.getApproveStatus(), "订单审核状态不能为空!");
        Assert.hasText(dto.getTenantId(), "租户ID不能为空!");

        FlightOrderApproveStatusEnum approveStatusEnum = FlightOrderApproveStatusEnum.getEnumByStatusName(dto.getApproveStatus());

        if (ObjectUtil.isNull(approveStatusEnum)) {
            LogUtil.error("传入审核状态:{}, 不合法", dto.getApproveStatus());
            throw new BizException(BizErrorCode.ORDER_APPROVE_STATUS_ILLEGAL.getCode(), BizErrorCode.ORDER_APPROVE_STATUS_ILLEGAL.getDesc());
        }

        if (StrUtil.equals(approveStatusEnum.getStatusCode(), FlightOrderApproveStatusEnum.APPROVING.getStatusCode())) {
            LogUtil.error("传入审核状态:{}, 不合法", dto.getApproveStatus());
            throw new BizException(BizErrorCode.ORDER_APPROVE_STATUS_ILLEGAL.getCode(), BizErrorCode.ORDER_APPROVE_STATUS_ILLEGAL.getDesc());
        }

        return liteFlowManager.approveOrder(dto);
    }

    @Override
    public String createOrder(FlightOrderCreateDTO dto) {

        Assert.notNull(dto, "创建订单参数不能为空!");
        Assert.notNull(OrderTypeEnum.getEnumByCode(dto.getOrderType()), "订单类型不能为空!");
        Assert.notNull(dto.getPayType(), "支付类型不能为空!");
        if (StrUtil.equals(dto.getOrderType(), OrderTypeEnum.NORMAL.getCode())) {
            Assert.notEmpty(dto.getProducts(), "产品不能为空!");
            dto.getProducts().forEach(product -> {
                Assert.hasText(product.getProductNo(), "产品编号不能为空!");
                Assert.notNull(product.getCount(), "产品数量不能为空!");
            });
        }

        Assert.notNull(OrderTypeEnum.getEnumByCode(dto.getOrderType()), "请传入正确的订单类型!");
        Assert.hasText(dto.getTenantId(), "租户ID不能为空!");

        return liteFlowManager.createOrder(dto);
    }

    @Override
    public CommonPage<FlightPlanVO> getFlightPlanListByPage(FlightPlanPageQueryDTO dto) {
        Assert.notNull(dto, "飞行计划查询参数不能为空!");

        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        FlightPlanPageQuery flightPlanPageQuery = flightConverter.toFlightPlanPageQuery(dto);
        if(UserUtil.getUserType() != null) {
            flightPlanPageQuery.setUserType(UserUtil.getUserType().getCode());
        }
        List<FlightPlanDO> flightPlanList = flightPlanRepository.getFlightPlanListByPage(flightPlanPageQuery);

        if (CollUtil.isEmpty(flightPlanList)) {
            return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), page.getPages(), page.getTotal(), Lists.newArrayList());
        }

        List<FlightPlanVO> flightPlanVOList = flightConverter.toFlightPlanVOList(flightPlanList);

        List<String> demandCodes = flightPlanVOList.stream().map(FlightPlanVO::getBizNo).collect(Collectors.toList());

        setPlanDemand(demandCodes, flightPlanVOList);

        List<String> aerodromeIds = flightPlanList.stream()
                .map(FlightPlanDO::getLandingAerodromeId)
                .filter(Objects::nonNull)
                .flatMap(s -> Arrays.stream(s.split(",")))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .distinct()
                .collect(Collectors.toList());

        if (aerodromeIds.isEmpty()) {
            return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), page.getPages(), page.getTotal(), flightPlanVOList);
        }

        List<AerodromeVO> aerodromeLists = flightPlanQueryClient.batchQueryAerodrome(aerodromeIds);

        if (CollUtil.isEmpty(aerodromeLists)) {
            return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), page.getPages(), page.getTotal(), flightPlanVOList);
        }

        Map<String, AerodromeVO> aerodromeMap = aerodromeLists.stream()
                        .collect(Collectors.toMap(AerodromeVO::getUavPortId, Function.identity(), (v1, v2) -> v1));

        flightPlanVOList.forEach(planVO -> setAerodromeInfo(aerodromeMap, planVO));

        return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), page.getPages(), page.getTotal(), flightPlanVOList);
    }

    private void setPlanDemand(List<String> demandCodes, List<FlightPlanVO> flightPlanVOList) {

        if (CollectionUtil.isEmpty(demandCodes)) {
            return;
        }

        List<FlightDemandDO> flightDemandList = queryDemandList(demandCodes);

        if (CollUtil.isEmpty(flightDemandList)) {
            return;
        }

        Map<String, FlightDemandDO> flightDemandMap = flightDemandList.stream()
                .collect(Collectors.toMap(FlightDemandDO::getDemandNo, Function.identity(), (k1, k2) -> k2));

        flightPlanVOList.forEach(plan -> {
            FlightDemandDO flightDemandDO = flightDemandMap.get(plan.getBizNo());
            setDemandProps(plan, flightDemandDO);
        });
    }

    private void setDemandProps(FlightPlanVO plan, FlightDemandDO flightDemandDO) {
        if (ObjectUtil.isNotNull(flightDemandDO)) {
            plan.setOrganizationId(flightDemandDO.getOrganizationId());
            plan.setOrganizationName(flightDemandDO.getOrganizationName());
            plan.setCycleType(flightDemandDO.getCycleType());
            plan.setType(flightDemandDO.getType());
            plan.setFlyingFrequency(flightDemandDO.getFlyingFrequency());
            plan.setFlyingNums(flightDemandDO.getFlyingNum());
        }
    }

    private List<FlightDemandDO> queryDemandList(List<String> demandCodes) {
        return flightDemandRepository.list(Wrappers.<FlightDemandDO>lambdaQuery()
                .in(FlightDemandDO::getDemandNo, demandCodes));
    }

    @Override
    public CommonPage<FlightPlanVO> getPlanList(FlightPlanPageQueryDTO dto) {
        Assert.notNull(dto, "飞行计划查询参数不能为空!");

        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        FlightPlanPageQuery flightPlanPageQuery = flightConverter.toFlightPlanPageQuery(dto);

        flightPlanPageQuery.setUserType(UserTypeEnum.OPERATION.getCode());

        List<FlightPlanDO> flightPlanList = flightPlanRepository.getFlightPlanListByPage(flightPlanPageQuery);

        if (CollUtil.isEmpty(flightPlanList)) {
            return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), page.getPages(), page.getTotal(), Lists.newArrayList());
        }

        List<FlightPlanVO> flightPlanVOList = flightConverter.toFlightPlanVOList(flightPlanList);

        List<String> aerodromeIds = flightPlanList.stream()
                .map(FlightPlanDO::getLandingAerodromeId)
                .filter(Objects::nonNull)
                .flatMap(s -> Arrays.stream(s.split(",")))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .distinct()
                .collect(Collectors.toList());

        if (aerodromeIds.isEmpty()) {
            return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), page.getPages(), page.getTotal(), flightPlanVOList);
        }

        List<AerodromeVO> aerodromeLists = flightPlanQueryClient.batchQueryAerodrome(aerodromeIds);

        if (CollUtil.isEmpty(aerodromeLists)) {
            return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), page.getPages(), page.getTotal(), flightPlanVOList);
        }

        Map<String, AerodromeVO> aerodromeMap = aerodromeLists.stream()
                .collect(Collectors.toMap(AerodromeVO::getUavPortId, Function.identity(), (v1, v2) -> v1));

        flightPlanVOList.forEach(planVO -> setAerodromeInfo(aerodromeMap, planVO));

        return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), page.getPages(), page.getTotal(), flightPlanVOList);
    }

    private void setAerodromeInfo(Map<String, AerodromeVO> aerodromeMap, FlightPlanVO flightPlanVO) {
        flightPlanVO.setLandingAerodrome(new ArrayList<>(aerodromeMap.values()));
    }

    @Override
    public FlightPlanVO getFlightPlanDetail(String planId) {

        Assert.hasText(planId, "计划ID不能为空!");

        FlightPlanVO flightPlanVO = flightPlanQueryClient.queryFlightPlan(planId);

        if (ObjectUtil.isNull(flightPlanVO)) {
            LogUtil.info("当前计划ID:{}, 不存在", planId);
            return null;
        }

        List<String> users = Lists.newArrayList();

        if (StrUtil.equals(UserTypeEnum.CUSTOMER.getCode(), UserUtil.getUserType().getCode())) {
            users = getAvailableUsers();
        }

        // 客户情况下，查找当前计划的原始需求，并拼接该合并需求对应的该用户的原始需求的需求名称
        if (StrUtil.equals(UserTypeEnum.CUSTOMER.getCode(), UserUtil.getUserType().getCode()) && CollUtil.isNotEmpty(users)) {
            List<FlightMergeDemandRelationDO> relation = mergeDemandRelationRepository.list(Wrappers.<FlightMergeDemandRelationDO>lambdaQuery()
                    .eq(FlightMergeDemandRelationDO::getTargetDemandCode, flightPlanVO.getBizNo())
                    .in(FlightMergeDemandRelationDO::getPublisherNo, users));

            flightPlanVO.setRequirementName(CollUtil.isEmpty(relation) ?
                    flightPlanVO.getRequirementName()
                    : relation
                        .stream()
                        .map(FlightMergeDemandRelationDO::getOriginalDemandName)
                        .collect(Collectors.joining(",")));
            flightPlanVO.setBizNo(CollUtil.isEmpty(relation) ?
                    flightPlanVO.getRequirementName()
                    : relation
                    .stream()
                    .map(FlightMergeDemandRelationDO::getOriginalDemandCode)
                    .collect(Collectors.joining(",")));
        }

        // 根据当前计划找需求
        if (StrUtil.isBlank(flightPlanVO.getBizNo())) {
            return flightPlanVO;

        }

        List<FlightDemandDO> flightDemandDOs = queryDemandList(List.of(flightPlanVO.getBizNo()));

        setDemandProps(flightPlanVO, CollUtil.isEmpty(flightDemandDOs) ? null : flightDemandDOs.get(0));

        return flightPlanVO;
    }

    @Override
    public void updateOrderStatus(String bizNo, String code, String tenantId) {

        Assert.hasText(bizNo, "业务编号不能为空!");
        Assert.hasText(code, "业务状态不能为空!");
        Assert.hasText(tenantId, "租户ID不能为空!");

        OrderStatusEnum orderStatusEnum = OrderStatusEnum.getByCode(code);

        String approveCode = null;
        if (ObjectUtil.isNotNull(orderStatusEnum) && StrUtil.equals(orderStatusEnum.getCode(), OrderStatusEnum.CLOSED.getCode())) {
            approveCode = FlightOrderApproveStatusEnum.REJECTED.getStatusCode();
        }

        if (ObjectUtil.isNotNull(orderStatusEnum) && StrUtil.equals(orderStatusEnum.getCode(), OrderStatusEnum.IN_PROGRESS.getCode())) {
            approveCode = FlightOrderApproveStatusEnum.APPROVED.getStatusCode();
        }

        flightOrderRepository.update(Wrappers.<FlightOrderDO>lambdaUpdate()
                .eq(FlightOrderDO::getOrderNo, bizNo)
                .eq(FlightOrderDO::getTenantId, tenantId)
                .set(FlightOrderDO::getStatus, code)
                .set(StrUtil.isNotBlank(approveCode), FlightOrderDO::getApproveStatus, approveCode)
                .set(FlightOrderDO::getGmtModified, LocalDateTime.now()));

    }

    @Override
    public Boolean orderProductUsage(FlightOrderProductUsageDTO dto) {
        Assert.notNull(dto, "参数不能为空");
        Assert.notNull(dto.getRequirementNo(), "需求参数不能为空");

        List<FlightDemandDO> demandList = flightDemandRepository.list(Wrappers.<FlightDemandDO>lambdaQuery()
                .eq(FlightDemandDO::getDemandNo, dto.getRequirementNo()));
        
        if (CollUtil.isEmpty(demandList)) {
            LogUtil.warn("该需求编号:{}, 暂无对应的需求", dto.getRequirementNo());
            return false;
        }

        // fixme 查询需求关联表，查询 合并需求 对应的 原始需求

        FlightOrderDO order = flightOrderRepository.getOne(Wrappers.<FlightOrderDO>lambdaQuery()
                .eq(FlightOrderDO::getOrderNo, demandList.get(0).getFlightOrderNo()));
        
        if (ObjectUtil.isNull(order)) {
            LogUtil.warn("该需求编号:{}, 暂无对应的订单", dto.getRequirementNo());
            return false;
        }

        List<FlightOrderProductUsageDO> orderProductUsageList = flightOrderProductUsageRepository.list(Wrappers.<FlightOrderProductUsageDO>lambdaQuery()
                .eq(FlightOrderProductUsageDO::getOrderNo, order.getOrderNo()));

        if (CollUtil.isEmpty(orderProductUsageList)) {
            LogUtil.warn("该需求编号:{}, 对应的订单编号:{}, 暂无产品用量", dto.getRequirementNo(), order.getOrderNo());
            return false;
        }

        List<FlightDemandDO> orderDemandList = flightDemandRepository.list(Wrappers.<FlightDemandDO>lambdaQuery()
                .eq(FlightDemandDO::getFlightOrderNo, order.getOrderNo()));

        if (CollUtil.isEmpty(orderDemandList)) {
            LogUtil.warn("订单:{}, 未对应需求", order.getOrderNo());
            return false;
        }

        int useQuantity = queryUseQuantity(orderDemandList, order);

        List<FlightOrderProductUsageDO> mainProductList = orderProductUsageList.stream().filter(product ->
                StrUtil.equals(product.getProductType(), FlightProductTypeEnum.FLIGHT_UAV.getType())
                        || StrUtil.equals(product.getProductType(), FlightProductTypeEnum.FLIGHT_SCENARIO.getType())).collect(Collectors.toList());
        
        if (CollUtil.isEmpty(mainProductList)) {
            LogUtil.warn("该需求编号:{}, 对应的订单编号:{}, 该订单对应的产品:{}, 未查询到主产品类型", dto.getRequirementNo(), order.getOrderNo(), JSONObject.toJSONString(orderProductUsageList));
            return false;
        }

        try {

            transactionTemplate.executeWithoutResult(e -> {

                FlightOrderProductUsageDO mainProduct = mainProductList.get(0);

                if (StrUtil.equals(order.getStatus(), OrderStatusEnum.IN_PROGRESS.getCode())
                        && mainProduct.getTotalQuantity() != -1
                        && mainProduct.getTotalQuantity() <= useQuantity) {
                    flightOrderRepository.update(Wrappers.<FlightOrderDO>lambdaUpdate()
                            .eq(FlightOrderDO::getOrderNo, order.getOrderNo())
                            .set(FlightOrderDO::getStatus, OrderStatusEnum.FINISHED.getCode())
                            .set(FlightOrderDO::getGmtModified, LocalDateTime.now()));
                }

                flightOrderProductUsageRepository.update(Wrappers.<FlightOrderProductUsageDO>lambdaUpdate()
                        .eq(FlightOrderProductUsageDO::getOrderNo, order.getOrderNo())
                        .set(FlightOrderProductUsageDO::getUseQuantity, useQuantity)
                        .set(FlightOrderProductUsageDO::getGmtModified, LocalDateTime.now()));
                
            });
            
        } catch (Exception e) {
            LogUtil.error("该需求编号:{}, 对应的订单编号:{}, 订单产品用量变更失败, 错误内容:{}", dto.getRequirementNo(), order.getOrderNo(), e);
            throw new BizException(BizErrorCode.ORDER_USAGE_CHANGE_ERROR.getCode(), BizErrorCode.ORDER_USAGE_CHANGE_ERROR.getDesc());
        }

        return true;
    }

    @Override
    public Boolean deleteOrder(OrderQueryDTO dto) {
        // query order detail
        FlightOrderVO flightOrderByNo = getFlightOrderByNo(dto);

        if (!StrUtil.equals(flightOrderByNo.getStatus(), OrderStatusEnum.CLOSED.getCode())) {
            LogUtil.error("当前订单:{}, 状态为:{}, 无法删除");
            throw new BizException(BizErrorCode.ORDER_CAN_NOT_DELETE_ERROR.getCode(), BizErrorCode.ORDER_CAN_NOT_DELETE_ERROR.getDesc());
        }

        try {
            boolean removeFlag = flightOrderRepository.remove(Wrappers.<FlightOrderDO>lambdaQuery()
                    .eq(FlightOrderDO::getOrderNo, flightOrderByNo.getOrderNo())
                    .eq(FlightOrderDO::getTenantId, TenantIdUtil.getTenantId()));

            if (!removeFlag) {
                LogUtil.warn("当前订单:{}, 删除失败", dto.getOrderNo());
                return false;
            }

        } catch (Exception e) {
            LogUtil.error("当前订单:{}, 删除失败, 堆栈信息:{}", dto.getOrderNo(), e);
            throw new BizException(BizErrorCode.ORDER__DELETE_ERROR.getCode(), BizErrorCode.ORDER__DELETE_ERROR.getDesc());
        }

        return true;
    }

    @Override
    public CommonPage<FlightPlanVO> getFlightPlanListByOrderNo(FlightPlanPageQueryDTO dto) {

        Assert.notNull(dto, "查询参数不能为空");
        Assert.hasText(dto.getOrderNo(), "订单编号不能为空!");

        FlightOrderDO order = flightOrderRepository.getOne(Wrappers.<FlightOrderDO>lambdaQuery()
                .eq(FlightOrderDO::getOrderNo, dto.getOrderNo()));

        if (Objects.isNull(order)) {
            return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), 0, 0L, Lists.newArrayList());
        }

        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<FlightPlanDO> planDOList = flightPlanRepository.getFlightPlanListByOrderNo(dto.getOrderNo(), dto.getRequirementId());

        List<FlightPlanVO> flightPlanVOList = flightConverter.toFlightPlanVOList(planDOList);

        List<String> aerodromeIds = planDOList.stream()
                .map(FlightPlanDO::getLandingAerodromeId)
                .filter(Objects::nonNull)
                .flatMap(s -> Arrays.stream(s.split(",")))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .distinct()
                .collect(Collectors.toList());

        if (aerodromeIds.isEmpty()) {
            return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), page.getPages(), page.getTotal(), flightPlanVOList);
        }

        List<AerodromeVO> aerodromeLists = flightPlanQueryClient.batchQueryAerodrome(aerodromeIds);

        if (CollUtil.isEmpty(aerodromeLists)) {
            return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), page.getPages(), page.getTotal(), flightPlanVOList);
        }

        Map<String, AerodromeVO> aerodromeMap = aerodromeLists.stream()
                .collect(Collectors.toMap(AerodromeVO::getUavPortId, Function.identity(), (v1, v2) -> v1));

        flightPlanVOList.forEach(planVO -> setAerodromeInfo(aerodromeMap, planVO));

        return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), page.getPages(), page.getTotal(), flightPlanVOList);
    }

    @Override
    public CommonPage<FlightOrderVO> getOrderPlanListByPage(OrderPageQueryDTO dto) {
        Assert.notNull(dto, "query params not null!");

        WholeDepartmentDTO wholeDepartmentInfo = userRemoteClient.getWholeDepartmentInfo(UserUtil.getUserNo());

        List<String> userNos = Lists.newArrayList();
        if (ObjectUtil.isNotNull(wholeDepartmentInfo)) {
            userNos = wholeDepartmentInfo.getUserNos();
        }

        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        List<FlightOrderDO> flightOrderDOList = flightOrderRepository.list(
                Wrappers.lambdaQuery(FlightOrderDO.class)
                        .eq(StrUtil.isNotEmpty(dto.getOrderNo()), FlightOrderDO::getOrderNo, dto.getOrderNo())
                        .in(CollUtil.isNotEmpty(userNos), FlightOrderDO::getUserNo, userNos)
                        .eq(StrUtil.isNotEmpty(dto.getCustomerId()), FlightOrderDO::getCustomerId, dto.getCustomerId())
                        .eq(StrUtil.isNotEmpty(dto.getStatus()), FlightOrderDO::getStatus, dto.getStatus())
                        .eq(StrUtil.isNotEmpty(dto.getCategoryNo()), FlightOrderDO::getCategoryNo, dto.getCategoryNo())
                        .like(StrUtil.isNotEmpty(dto.getServiceName()), FlightOrderDO::getProductName, dto.getServiceName())
                        .like(StrUtil.isNotEmpty(dto.getOrganizationName()), FlightOrderDO::getOrganizationName, dto.getOrganizationName())
                        .like(StrUtil.isNotEmpty(dto.getCycleName()), FlightOrderDO::getCycleName, dto.getCycleName())
                        .eq(StrUtil.isNotEmpty(dto.getCycleNo()), FlightOrderDO::getCycleNo, dto.getCycleNo())
                        .eq(FlightOrderDO::getTenantId, dto.getTenantId())
                        .eq(FlightOrderDO::getOrderType, dto.getOrderType())
                        .orderByDesc(FlightOrderDO::getOrderTime)
                        .orderByDesc(FlightOrderDO::getId));

        List<FlightOrderVO> flightOrderList = Collections.emptyList();

        if (CollUtil.isEmpty(flightOrderDOList)) {
            return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), flightOrderList);
        }

        flightOrderList = flightOrderConvert.convertToFlightOrderVOList(flightOrderDOList);

        flightOrderProductUsageHelper.batchSetProductUsageAndProductInfo(flightOrderList, dto.getTenantId());

        List<UserDetailDTO> userDetailList = userRemoteClient.batchQueryUser(flightOrderList.stream().map(FlightOrderVO::getUserNo).collect(Collectors.toList()));

        if (CollUtil.isEmpty(userDetailList)) {
            return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), flightOrderList);
        }

        Map<String, UserDetailDTO> userDetailMap = userDetailList.stream().collect(Collectors.toMap(UserDetailDTO::getUserNo, Function.identity(), (k1, k2) -> k2));

        flightOrderList.forEach(e -> e.setUserName(ObjectUtil.isNull(userDetailMap.get(e.getUserNo())) ? null : userDetailMap.get(e.getUserNo()).getUserName()));

        List<String> orders = flightOrderList.stream().map(FlightOrderVO::getOrderNo).collect(Collectors.toList());
        // 判断当前用户是否有权限编辑
        List<ApprovalInstance> approvalInstanceList = approveInstanceService.getNeedApproveInstanceList(orders);

        if (CollUtil.isEmpty(approvalInstanceList)) {
            return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), flightOrderList);
        }

        Map<String, List<ApprovalNode>> approvalNodeMap = approveInstanceService
                .getCurrentNeedToApproveNodeMap(approvalInstanceList.stream()
                        .map(ApprovalInstance::getApprovalId).collect(Collectors.toList()));

        if (CollUtil.isEmpty(approvalNodeMap)) {
            return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), flightOrderList);
        }

        String currentUserNo = UserUtil.getUserNo();
        Set<String> allowedOrderNos = approvalInstanceList.stream()
                .filter(instance -> {
                    List<ApprovalNode> nodes = approvalNodeMap.get(instance.getApprovalId());
                    return CollUtil.isNotEmpty(nodes) && nodes.stream()
                            .anyMatch(n -> Objects.equals(n.getApproveUserId(), currentUserNo));
                })
                .map(ApprovalInstance::getBizId)
                .collect(Collectors.toSet());

        flightOrderList.forEach(order -> {
            boolean isEdit = StrUtil.equals(order.getApproveStatus(), FlightOrderApproveStatusEnum.REJECTED.getStatusCode())
                    && StrUtil.equals(UserUtil.getUserNo(), order.getUserNo());
            order.setAllowEdit(isEdit);
            order.setAllowApprove(allowedOrderNos.contains(order.getOrderNo()));
        });

        return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), flightOrderList);
    }

    @Override
    public List<FlightPositionVO> queryPlanPositions(String orderNo) {

        FlightOrderDO order = flightOrderRepository.getOne(Wrappers.<FlightOrderDO>lambdaQuery()
                .eq(FlightOrderDO::getOrderNo, orderNo));

        if (ObjectUtil.isNull(order)) {
            return Lists.newArrayList();
        }

        List<FlightPositionVO> flightPositions = flightOrderConvert.convertToFlightPositionVOList(flightPositionRepository
                .list(Wrappers.<FlightPositionDO>lambdaQuery()
                        .eq(FlightPositionDO::getOrderNo, orderNo)));

        if (CollUtil.isNotEmpty(flightPositions)) {
            flightPositions.forEach(e -> e.setOrderName(order.getProductName()));
        }

        return flightPositions;

    }

    private int queryUseQuantity(List<FlightDemandDO> orderDemandList, FlightOrderDO order) {
        int useQuantity;

        BatchQueryPlanDTO batchQueryPlanDTO = new BatchQueryPlanDTO();
        batchQueryPlanDTO.setBizNo(orderDemandList.stream().map(FlightDemandDO::getDemandNo).collect(Collectors.toList()));
        batchQueryPlanDTO.setTenantId(order.getTenantId());

        List<FlightCountVO> flightCountVOS = flightPlanQueryClient.planCountByBizNo(batchQueryPlanDTO);

        if (CollUtil.isNotEmpty(flightCountVOS)) {
            useQuantity = flightCountVOS.stream().mapToInt(FlightCountVO::getCount).sum();
        } else {
            useQuantity = 0;
        }
        return useQuantity;
    }
}