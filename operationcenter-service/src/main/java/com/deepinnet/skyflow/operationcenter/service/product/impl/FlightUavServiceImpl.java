package com.deepinnet.skyflow.operationcenter.service.product.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.infra.api.client.UserClient;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.DataAccessDTO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightUavDO;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightPlanPageQuery;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightPlanRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightUavRepository;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.enums.BizTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.client.FlightPlanQueryClient;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavBmService;
import com.deepinnet.skyflow.operationcenter.service.product.FlightUavService;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightUavConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.user.UserService;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.util.enums.UserTypeEnum;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavGroupVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavVO;
import com.deepinnet.spatiotemporalplatform.dto.PositionQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import com.deepinnet.spatiotemporalplatform.vo.RealTimeUavFlightVO;
import com.deepinnet.spatiotemporalplatform.vo.UavFlightTrackVO;
import com.deepinnet.tenant.TenantIdUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 飞行无人机服务实现类
 *
 * <AUTHOR>
 */
@Service
public class FlightUavServiceImpl implements FlightUavService {

    @Resource
    private FlightUavRepository flightUavRepository;

    @Resource
    private FlightUavConvert flightUavConvert;

    @Resource
    private FlightUavBmService flightUavBmService;

    @Resource
    private FlightPlanQueryClient flightPlanQueryClient;

    @Resource
    private FlightPlanRepository flightPlanRepository;

    @Resource
    private UserRemoteClient userRemoteClient;

    @Resource
    private UserService userService;

    public static final double SPEED_60_KMH = 16.67;


    @Override
    public String saveFlightUav(FlightUavDTO flightUavDTO) {
        // 参数校验
        validateFlightUavParams(flightUavDTO);

        // 生成飞行无人机编号
        String flightUavNo = IdGenerateUtil.getId(BizTypeEnum.FLIGHT_UAV.getType());
        flightUavDTO.setFlightUavNo(flightUavNo);

        FlightUavDO flightUavDO = flightUavConvert.convertToDO(flightUavDTO);
        flightUavDO.setGmtCreated(LocalDateTime.now());
        flightUavDO.setGmtModified(LocalDateTime.now());
        flightUavDO.setId(null);

        boolean success = flightUavRepository.save(flightUavDO);
        if (!success) {
            LogUtil.error("保存飞行无人机失败: {}", flightUavDTO.getFlightUavNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("保存飞行无人机成功: {}", flightUavDO.getId());
        return flightUavDO.getFlightUavNo();
    }

    @Override
    public boolean updateFlightUav(FlightUavDTO flightUavDTO) {
        if (flightUavDTO.getId() == null) {
            LogUtil.error("更新飞行无人机失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 参数校验
        validateFlightUavParams(flightUavDTO);

        // 检查飞行无人机是否存在
        FlightUavDO existFlightUav = flightUavRepository.getById(flightUavDTO.getId());
        if (existFlightUav == null) {
            LogUtil.error("更新飞行无人机失败，飞行无人机不存在: {}", flightUavDTO.getId());
            throw new BizException(BizErrorCode.FLIGHT_UAV_NOT_FOUND.getCode(), BizErrorCode.FLIGHT_UAV_NOT_FOUND.getDesc());
        }

        FlightUavDO flightUavDO = flightUavConvert.convertToDO(flightUavDTO);
        flightUavDO.setGmtModified(LocalDateTime.now());

        boolean success = flightUavRepository.updateById(flightUavDO);
        if (!success) {
            LogUtil.error("更新飞行无人机失败: {}", flightUavDTO.getId());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("更新飞行无人机成功: {}", flightUavDO.getId());
        return true;
    }

    @Override
    public FlightUavDTO getFlightUavById(Integer id) {
        if (id == null) {
            LogUtil.error("获取飞行无人机失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightUavDO flightUavDO = flightUavRepository.getById(id);
        if (flightUavDO == null) {
            LogUtil.error("获取飞行无人机失败，飞行无人机不存在: {}", id);
            return null;
        }

        return flightUavConvert.convert(flightUavDO);
    }

    @Override
    public FlightUavDTO getFlightUavByNo(String flightUavNo) {
        if (StringUtils.isBlank(flightUavNo)) {
            LogUtil.error("获取飞行无人机失败，无人机编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightUavDO flightUavDO = flightUavRepository.getOne(
                Wrappers.lambdaQuery(FlightUavDO.class)
                        .eq(FlightUavDO::getFlightUavNo, flightUavNo)
        );
        
        if (flightUavDO == null) {
            return null;
        }

        return flightUavConvert.convert(flightUavDO);
    }
    
    @Override
    public List<FlightUavDTO> getFlightUavListBySupplierUserNo(String supplierUserNo) {
        if (StringUtils.isBlank(supplierUserNo)) {
            LogUtil.error("获取飞行无人机列表失败，服务商编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        List<FlightUavDO> flightUavDOList = flightUavRepository.list(
                Wrappers.lambdaQuery(FlightUavDO.class)
                        .eq(FlightUavDO::getSupplierUserNo, supplierUserNo)
                        .orderByDesc(FlightUavDO::getGmtCreated)
        );
        
        return flightUavConvert.convertList(flightUavDOList);
    }

    @Override
    public List<FlightUavGroupVO> getUavGroupByModel(String supplierUserNo) {
        FlightUavGroupQueryDTO queryDTO = new FlightUavGroupQueryDTO();
        queryDTO.setSupplierUserNo(supplierUserNo);
        return getUavGroupByModel(queryDTO);
    }

    @Override
    public List<FlightUavGroupVO> getUavGroupByModel(FlightUavGroupQueryDTO queryDTO) {
        if (queryDTO == null || StringUtils.isBlank(queryDTO.getSupplierUserNo())) {
            LogUtil.error("获取按机型分组的飞行无人机列表失败，服务商编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        // 1. 获取该服务商下的所有飞行无人机
        List<FlightUavDTO> flightUavDTOList = getFlightUavListBySupplierUserNo(queryDTO.getSupplierUserNo());
        if (CollectionUtils.isEmpty(flightUavDTOList)) {
            return new ArrayList<>();
        }
        
        // 2. 转换成VO对象
        List<FlightUavVO> flightUavVOList = flightUavConvert.convertToVOList(flightUavDTOList);
        
        // 3. 获取所有涉及到的机型编号
        List<String> modelNoList = flightUavDTOList.stream()
                .map(FlightUavDTO::getFlightUavBmNo)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        
        if (modelNoList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 4. 查询所有涉及到的机型信息
        List<FlightUavBmDTO> modelList = flightUavBmService.listFlightUavBm(modelNoList);
        
        // 5. 根据查询条件筛选机型
        if (!CollectionUtils.isEmpty(modelList)) {
            if (StringUtils.isNotBlank(queryDTO.getFlightUavBmName())) {
                modelList = modelList.stream()
                        .filter(model -> model.getFlightUavBmName() != null && 
                                model.getFlightUavBmName().contains(queryDTO.getFlightUavBmName()))
                        .collect(Collectors.toList());
            }
            
            if (StringUtils.isNotBlank(queryDTO.getFlightUavBmModelNo())) {
                modelList = modelList.stream()
                        .filter(model -> model.getFlightUavBmModelNo() != null && 
                                model.getFlightUavBmModelNo().contains(queryDTO.getFlightUavBmModelNo()))
                        .collect(Collectors.toList());
            }
            
            // 获取满足条件的机型编号
            List<String> filteredModelNos = modelList.stream()
                    .map(FlightUavBmDTO::getFlightUavBmNo)
                    .collect(Collectors.toList());
            
            // 如果过滤后列表为空，直接返回空结果
            if (filteredModelNos.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 筛选出满足条件的无人机
            flightUavVOList = flightUavVOList.stream()
                    .filter(uav -> filteredModelNos.contains(uav.getFlightUavBmNo()))
                    .collect(Collectors.toList());
        }
        
        Map<String, FlightUavBmDTO> modelMap;
        if (!CollectionUtils.isEmpty(modelList)) {
            modelMap = modelList.stream()
                    .collect(Collectors.toMap(FlightUavBmDTO::getFlightUavBmNo, model -> model, (a, b) -> a));
        } else {
            modelMap = new HashMap<>();
        }

        // 6. 按机型分组飞行无人机
        Map<String, List<FlightUavVO>> groupedMap = flightUavVOList.stream()
                .filter(uav -> StringUtils.isNotBlank(uav.getFlightUavBmNo()))
                .collect(Collectors.groupingBy(FlightUavVO::getFlightUavBmNo));
        
        // 7. 构建分组结果
        List<FlightUavGroupVO> result = new ArrayList<>();
        groupedMap.forEach((modelNo, uavList) -> {
            FlightUavGroupVO groupVO = new FlightUavGroupVO();
            groupVO.setFlightUavBmNo(modelNo);
            groupVO.setUavList(uavList);
            
            // 设置所有机型信息
            FlightUavBmDTO modelDTO = modelMap.get(modelNo);
            if (modelDTO != null) {
                groupVO.setId(modelDTO.getId());
                groupVO.setFlightUavBmNo(modelDTO.getFlightUavBmNo());
                groupVO.setFlightUavBmDesc(modelDTO.getFlightUavBmDesc());
                groupVO.setFlightUavBmName(modelDTO.getFlightUavBmName());
                groupVO.setFlightUavBmModelNo(modelDTO.getFlightUavBmModelNo());
                groupVO.setFlightUavCrewType(modelDTO.getFlightUavCrewType());
                groupVO.setFlightUavWeightClassfication(modelDTO.getFlightUavWeightClassfication());
                groupVO.setFlightUavFlyType(modelDTO.getFlightUavFlyType());
                groupVO.setFlightUavMaxCarrierWeight(modelDTO.getFlightUavMaxCarrierWeight());
                groupVO.setFlightUavMaxFlyMinute(modelDTO.getFlightUavMaxFlyMinute());
                groupVO.setFlightUavMaxFlyRange(modelDTO.getFlightUavMaxFlyRange());
                groupVO.setFlightUavMaxFlyHeight(modelDTO.getFlightUavMaxFlyHeight());
                groupVO.setFlightUavRadius(modelDTO.getFlightUavRadius());
                groupVO.setFlightUavSupportVedio(modelDTO.getFlightUavSupportVedio());
                groupVO.setFlightUavCameraPixel(modelDTO.getFlightUavCameraPixel());
                groupVO.setFlightUavAppliedScenarios(modelDTO.getFlightUavAppliedScenarios());
                groupVO.setFlightUavPictures(modelDTO.getFlightUavPictures());
                groupVO.setTenantId(modelDTO.getTenantId());
            }
            
            result.add(groupVO);
        });
        
        return result;
    }

    @Override
    public CommonPage<FlightUavDTO> pageQueryFlightUav(FlightUavQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<FlightUavDO> queryWrapper = Wrappers.lambdaQuery(FlightUavDO.class)
                .eq(StringUtils.isNotBlank(queryDTO.getFlightUavNo()), FlightUavDO::getFlightUavNo, queryDTO.getFlightUavNo())
                .in(!CollectionUtils.isEmpty(queryDTO.getFlightUavNoList()), FlightUavDO::getFlightUavNo, queryDTO.getFlightUavNoList())
                .like(StringUtils.isNotBlank(queryDTO.getFlightUavName()), FlightUavDO::getFlightUavName, queryDTO.getFlightUavName())
                .eq(StringUtils.isNotBlank(queryDTO.getFlightStationNo()), FlightUavDO::getFlightStationNo, queryDTO.getFlightStationNo())
                .eq(StringUtils.isNotBlank(queryDTO.getFlightUavSn()), FlightUavDO::getFlightUavSn, queryDTO.getFlightUavSn())
                .eq(StringUtils.isNotBlank(queryDTO.getFlightUavBmNo()), FlightUavDO::getFlightUavBmNo, queryDTO.getFlightUavBmNo())
                .eq(StringUtils.isNotBlank(queryDTO.getSupplierUserNo()), FlightUavDO::getSupplierUserNo, queryDTO.getSupplierUserNo())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantId()), FlightUavDO::getTenantId, queryDTO.getTenantId())
                .orderByDesc(FlightUavDO::getGmtCreated);

        List<FlightUavDO> flightUavDOList = flightUavRepository.list(queryWrapper);
        if (CollectionUtils.isEmpty(flightUavDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<FlightUavDTO> flightUavDTOList = flightUavConvert.convertList(flightUavDOList);
        PageInfo<FlightUavDTO> pageInfo = new PageInfo<>(flightUavDTOList);
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), pageInfo.getPages(), pageInfo.getTotal(), flightUavDTOList);
    }

    @Override
    public boolean deleteFlightUav(Integer id) {
        if (id == null) {
            LogUtil.error("删除飞行无人机失败，缺少ID");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        // 检查飞行无人机是否存在
        FlightUavDO existFlightUav = flightUavRepository.getById(id);
        if (existFlightUav == null) {
            LogUtil.error("删除飞行无人机失败，飞行无人机不存在: {}", id);
            return false;
        }

        boolean success = flightUavRepository.removeById(id);
        if (!success) {
            LogUtil.error("删除飞行无人机失败: {}", id);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        LogUtil.info("删除飞行无人机成功: {}", id);
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchOperateFlightUavs(FlightUavBatchOperationDTO batchOperationDTO) {
        if (batchOperationDTO == null) {
            LogUtil.error("批量操作飞行无人机失败，参数为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        String supplierUserNo = batchOperationDTO.getSupplierUserNo();
        if (StringUtils.isBlank(supplierUserNo)) {
            LogUtil.error("批量操作飞行无人机失败，服务商编号为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }
        
        // 处理删除操作
        List<Integer> deleteIds = batchOperationDTO.getDeleteIds();
        if (!CollectionUtils.isEmpty(deleteIds)) {
            for (Integer id : deleteIds) {
                try {
                    // 检查是否为当前服务商的无人机
                    FlightUavDO flightUavDO = flightUavRepository.getById(id);
                    if (flightUavDO != null && supplierUserNo.equals(flightUavDO.getSupplierUserNo())) {
                        boolean success = flightUavRepository.removeById(id);
                        if (!success) {
                            LogUtil.error("批量删除飞行无人机失败，ID: {}", id);
                            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "删除飞行无人机失败，ID: " + id);
                        }
                    } else {
                        LogUtil.error("批量删除飞行无人机失败，无权限或不存在，ID: {}", id);
                    }
                } catch (Exception e) {
                    LogUtil.error("批量删除飞行无人机出错，ID: {}, 错误: {}", id, e.getMessage());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "删除飞行无人机失败，ID: " + id);
                }
            }
        }
        
        // 处理更新操作
        List<FlightUavDTO> updateItems = batchOperationDTO.getUpdateItems();
        if (!CollectionUtils.isEmpty(updateItems)) {
            // 收集所有SN码用于检查
            List<String> snList = new ArrayList<>();
            Map<String, Integer> snIdMap = new HashMap<>();
            
            for (FlightUavDTO item : updateItems) {
                if (item.getId() == null) {
                    LogUtil.error("批量更新飞行无人机失败，缺少ID: {}", item.getFlightUavSn());
                    throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "更新飞行无人机缺少ID");
                }
                
                // 检查是否为当前服务商的无人机
                FlightUavDO existFlightUav = flightUavRepository.getById(item.getId());
                if (existFlightUav == null) {
                    LogUtil.error("批量更新飞行无人机失败，无人机不存在: {}", item.getId());
                    throw new BizException(BizErrorCode.NOT_FOUND.getCode(), "飞行无人机不存在，ID: " + item.getId());
                }
                
                if (!supplierUserNo.equals(existFlightUav.getSupplierUserNo())) {
                    LogUtil.error("批量更新飞行无人机失败，无权限: {}", item.getId());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "无权限更新该飞行无人机，ID: " + item.getId());
                }
                
                // 如果更新了SN码，需要检查重复
                String sn = item.getFlightUavSn();
                if (StringUtils.isNotBlank(sn) && !sn.equals(existFlightUav.getFlightUavSn())) {
                    if (snList.contains(sn)) {
                        LogUtil.error("批量更新飞行无人机失败，请求中存在重复的SN码: {}", sn);
                        throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请求中存在重复的SN码: " + sn);
                    }
                    snList.add(sn);
                    snIdMap.put(sn, item.getId());
                }
            }
            
            // 检查SN码是否已存在于数据库（排除自身ID）
            if (!snList.isEmpty()) {
                List<FlightUavDO> existingUavs = flightUavRepository.list(
                        Wrappers.lambdaQuery(FlightUavDO.class)
                                .in(FlightUavDO::getFlightUavSn, snList)
                );
                
                if (!CollectionUtils.isEmpty(existingUavs)) {
                    for (FlightUavDO existingUav : existingUavs) {
                        String existingSn = existingUav.getFlightUavSn();
                        Integer updateId = snIdMap.get(existingSn);
                        
                        // 如果存在SN码相同但ID不同的记录，说明SN码冲突
                        if (!existingUav.getId().equals(updateId)) {
                            LogUtil.error("批量更新飞行无人机失败，SN码已存在: {}", existingSn);
                            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "SN码已存在: " + existingSn);
                        }
                    }
                }
            }
            
            // 执行更新操作
            for (FlightUavDTO item : updateItems) {
                try {
                    // 确保设置了服务商编号
                    item.setSupplierUserNo(supplierUserNo);
                    boolean success = updateFlightUav(item);
                    if (!success) {
                        LogUtil.error("批量更新飞行无人机失败，ID: {}", item.getId());
                        throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "更新飞行无人机失败，ID: " + item.getId());
                    }
                } catch (Exception e) {
                    LogUtil.error("批量更新飞行无人机出错，ID: {}, 错误: {}", item.getId(), e.getMessage());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "更新飞行无人机失败，ID: " + item.getId());
                }
            }
        }
        
        // 处理新增操作
        List<FlightUavDTO> createItems = batchOperationDTO.getCreateItems();
        if (!CollectionUtils.isEmpty(createItems)) {
            List<String> snList = new ArrayList<>();
            
            // 先检查SN码是否重复
            for (FlightUavDTO item : createItems) {
                // 设置租户id
                item.setTenantId(TenantIdUtil.getTenantId());
                String sn = item.getFlightUavSn();
                if (StringUtils.isNotBlank(sn)) {
                    if (snList.contains(sn)) {
                        LogUtil.error("批量新增飞行无人机失败，请求中存在重复的SN码: {}", sn);
                        throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请求中存在重复的SN码: " + sn);
                    }
                    snList.add(sn);
                }
            }
            
            // 检查SN码是否已存在于数据库
            if (!snList.isEmpty()) {
                List<FlightUavDO> existingUavs = flightUavRepository.list(
                        Wrappers.lambdaQuery(FlightUavDO.class)
                                .eq(FlightUavDO::getSupplierUserNo, supplierUserNo)
                                .in(FlightUavDO::getFlightUavSn, snList)
                );
                
                if (!CollectionUtils.isEmpty(existingUavs)) {
                    String existingSn = existingUavs.get(0).getFlightUavSn();
                    LogUtil.error("批量新增飞行无人机失败，SN码已存在: {}", existingSn);
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "SN码已存在: " + existingSn);
                }
            }
            
            // 执行新增操作
            for (FlightUavDTO item : createItems) {
                try {
                    // 确保设置了服务商编号
                    item.setSupplierUserNo(supplierUserNo);
                    saveFlightUav(item);
                } catch (Exception e) {
                    LogUtil.error("批量新增飞行无人机出错，SN: {}, 错误: {}", item.getFlightUavSn(), e.getMessage());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "新增飞行无人机失败，SN: " + item.getFlightUavSn());
                }
            }
        }
        
        return true;
    }

    @Override
    public List<UavFlightTrackVO> getUavTrajectory(PositionWebQueryDTO queryDTO, boolean queryPlan) {

        PositionQueryDTO positionQueryDTO = new PositionQueryDTO();
        positionQueryDTO.setStatus(queryDTO.getStatus());

        if(queryPlan) {

            FlightPlanPageQuery flightPlanPageQuery = new FlightPlanPageQuery();

            if(BooleanUtils.isTrue(queryDTO.getNeedQueryByDepartment())) {
                flightPlanPageQuery.setUserNo(userService.getAvailableUserNoByDepartmentId(queryDTO.getDepartmentId()));
            } else {
                if (ObjectUtil.equals(UserTypeEnum.CUSTOMER, UserUtil.getUserType())) {
                    List<String> users = getAvailableUsers();
                    flightPlanPageQuery.setUserNo(users);
                }

                if (ObjectUtil.equals(UserTypeEnum.SUPPLIER, UserUtil.getUserType())) {
                    flightPlanPageQuery.setFlightUnitId(UserUtil.getUserNo());
                }
            }

            // 按照服务商的用户类型查询可以保证需求列表不重复
            flightPlanPageQuery.setUserType(UserTypeEnum.SUPPLIER.getCode());
            List<FlightPlanDO> flightPlanList = flightPlanRepository.getFlightPlanListByPage(flightPlanPageQuery);

            positionQueryDTO.setPlanIds(CollStreamUtil.toList(flightPlanList, FlightPlanDO::getPlanId));

            if(CollectionUtils.isEmpty(flightPlanList)) {
                return Collections.emptyList();
            }
        } else {
            // 没传计划 id 不去查询
            if(StringUtils.isBlank(queryDTO.getPlanId())) return List.of();

            positionQueryDTO.setPlanId(queryDTO.getPlanId());
        }


        return flightPlanQueryClient.getUavTrajectory(positionQueryDTO);
    }

    @Override
    public RealTimeUavFlightPositionDTO getUavFlightStatus(FlightUavRealtimePositionQueryDTO positionQueryDTO) {
        if(StringUtils.isBlank(positionQueryDTO.getPlanId())) {
            return null;
        }

        PositionQueryDTO queryDTO = new PositionQueryDTO();
        queryDTO.setPlanId(positionQueryDTO.getPlanId());
        queryDTO.setStatus(positionQueryDTO.getPlanStatus());
        List<UavFlightTrackVO> trackVOList = flightPlanQueryClient.getUavTrajectory(queryDTO);

        List<RealTimeUavFlightPositionDTO.UavFlightPositionMeta> trackList = new ArrayList<>();
        LineString track = null;
        if(!CollectionUtils.isEmpty(trackVOList) && !CollectionUtils.isEmpty(trackVOList.get(0).getUavFlightList())) {
            track = WktUtil.pointListToLineString(trackVOList.get(0).getUavFlightList().stream()
                    .filter(r -> StringUtils.isNotBlank(r.getUavPosition()))
                    .map(r -> {
                        Point tmp = WktUtil.toPoint(r.getUavPosition());
                        var meta = new RealTimeUavFlightPositionDTO.UavFlightPositionMeta();
                        meta.setLng(String.valueOf(tmp.getX()));
                        meta.setLat(String.valueOf(tmp.getY()));
                        meta.setAlt(r.getElevation());
                        trackList.add(meta);

                        return tmp;
                    })
                    .collect(Collectors.toList()));
        }

        List<RealTimeUavFlightVO> realTimeUavFlightVOS = flightPlanQueryClient.getUavPosition(queryDTO);
        RealTimeUavFlightVO realTimeUavFlightVO = CollectionUtils.isEmpty(realTimeUavFlightVOS) ? null : realTimeUavFlightVOS.get(0);
        Point currentPoint = WktUtil.toPoint(Objects.requireNonNullElse(realTimeUavFlightVO, new RealTimeUavFlightVO()).getUavPosition());

        if(currentPoint == null) {
            return null;
        }


        Polygon targetRange;
        if(StringUtils.isNotBlank(positionQueryDTO.getAreaWkt())) {
            targetRange = WktUtil.toPolygon(positionQueryDTO.getAreaWkt());
        } else {
            targetRange = (Polygon) WktUtil.buffer(WktUtil.toPoint(positionQueryDTO.getCenterPointLng(),
                    positionQueryDTO.getCenterPointLat()), 500);
        }

        RealTimeUavFlightPositionDTO res = new RealTimeUavFlightPositionDTO();
        if(BooleanUtils.isTrue(positionQueryDTO.getNeedFlownTrajectoryWkt())) {
            res.setFlownTrajectory(trackList);
        }
        res.setCurrentLng(String.valueOf(currentPoint.getX()));
        res.setCurrentLat(String.valueOf(currentPoint.getY()));
        res.setCurrentAlt(Objects.requireNonNullElse(realTimeUavFlightVO, new RealTimeUavFlightVO()).getElevation());

        if(track != null) {
            boolean areaAlreadyFlown = track.intersects(targetRange);
            if(areaAlreadyFlown) {
                res.setArrived(true);
                return res;
            }
        }

        double distance = WktUtil.calculateDistanceInMeters(currentPoint, targetRange);
        double timeToArrive = distance / SPEED_60_KMH;
        double timeMinToArrive = timeToArrive / 60;

        res.setArrived(false);
        res.setDistanceToTarget(String.format("%.2f", distance));
        res.setTimeMinToArrival(String.format("%.1f", timeMinToArrive));
        return res;
    }

    @Override
    public FlightUavDTO getFlightUavBySn(String flightUavSn) {
        if (StringUtils.isBlank(flightUavSn)) {
            LogUtil.error("获取飞行无人机失败，无人机 sn 码为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), BizErrorCode.ILLEGAL_PARAMS.getDesc());
        }

        FlightUavDO flightUavDO = flightUavRepository.getOne(
                Wrappers.lambdaQuery(FlightUavDO.class)
                        .eq(FlightUavDO::getFlightUavSn, flightUavSn)
        );

        if (flightUavDO == null) {
            return null;
        }

        return flightUavConvert.convert(flightUavDO);
    }

    @Override
    public List<RealTimeUavFlightVO> getUavPosition(PositionWebQueryDTO queryDTO) {
        PositionQueryDTO positionQueryDTO = new PositionQueryDTO();
        positionQueryDTO.setStatus(queryDTO.getStatus());

        FlightPlanPageQuery flightPlanPageQuery = new FlightPlanPageQuery();

        if(BooleanUtils.isTrue(queryDTO.getNeedQueryByDepartment())) {
            flightPlanPageQuery.setUserNo(userService.getAvailableUserNoByDepartmentId(queryDTO.getDepartmentId()));
        } else {
            if (ObjectUtil.equals(UserTypeEnum.CUSTOMER, UserUtil.getUserType())) {
                List<String> users = getAvailableUsers();
                flightPlanPageQuery.setUserNo(users);
            }

            if (ObjectUtil.equals(UserTypeEnum.SUPPLIER, UserUtil.getUserType())) {
                flightPlanPageQuery.setFlightUnitId(UserUtil.getUserNo());
            }
        }


        // 按照服务商的用户类型查询可以保证需求列表不重复
        flightPlanPageQuery.setUserType(UserTypeEnum.SUPPLIER.getCode());
        List<FlightPlanDO> flightPlanList = flightPlanRepository.getFlightPlanListByPage(flightPlanPageQuery);
        positionQueryDTO.setPlanIds(CollStreamUtil.toList(flightPlanList, FlightPlanDO::getPlanId));

        if(CollectionUtils.isEmpty(flightPlanList)) {
            return Collections.emptyList();
        }

        return flightPlanQueryClient.getUavPosition(positionQueryDTO);
    }

    private List<String> getAvailableUsers() {
        DataAccessDTO availableQueryData = userRemoteClient.getAvailableQueryData();

        if (ObjectUtil.isNull(availableQueryData)) {
            LogUtil.warn("当前暂无可用用户");
            return Lists.newArrayList();
        }

        List<String> userNos = Lists.newArrayList();

        // 需求计划订单塞入 用户编号 ｜ 部门编号
        if (CollUtil.isNotEmpty(availableQueryData.getSupportQueryUserNos())
                && !availableQueryData.getSupportQueryUserNos().contains(DataScopeConstants.SUPER_ADMIN)) {
            userNos.addAll(availableQueryData.getSupportQueryUserNos());
        }

        return userNos;
    }

    /**
     * 校验飞行无人机参数
     *
     * @param flightUavDTO 飞行无人机DTO
     */
    private void validateFlightUavParams(FlightUavDTO flightUavDTO) {
        // UAV06: 校验机型是否选择
        if (StringUtils.isBlank(flightUavDTO.getFlightUavBmNo())) {
            LogUtil.error("飞行无人机校验失败，未选择机型");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请选择一个机型");
        }
        
        // UAV12: 校验飞行器名称
        if (StringUtils.isBlank(flightUavDTO.getFlightUavName())) {
            LogUtil.error("飞行无人机校验失败，飞行器名称为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入飞行器名称");
        }
        
        // UAV13: 校验飞行器名称长度
        if (flightUavDTO.getFlightUavName().length() > 30) {
            LogUtil.error("飞行无人机校验失败，飞行器名称过长: {}", flightUavDTO.getFlightUavName());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "飞行器名称不能超过30字符");
        }
        
        // UAV15: 校验唯一产品识别码
        if (StringUtils.isBlank(flightUavDTO.getFlightUavSn())) {
            LogUtil.error("飞行无人机校验失败，唯一识别码为空");
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "请输入唯一识别码");
        }
        
        // UAV17: 校验唯一产品识别码长度
        if (flightUavDTO.getFlightUavSn().length() > 30) {
            LogUtil.error("飞行无人机校验失败，唯一识别码过长: {}", flightUavDTO.getFlightUavSn());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "识别码不能超过30字符");
        }
        
        // UAV16: 校验唯一产品识别码是否重复
        FlightUavDO existFlightUav = flightUavRepository.getOne(
                Wrappers.lambdaQuery(FlightUavDO.class)
                        .eq(FlightUavDO::getFlightUavSn, flightUavDTO.getFlightUavSn())
                        .eq(FlightUavDO::getIsDeleted, false)
        );
        
        // 如果是更新操作，需要排除自身ID
        if (existFlightUav != null && 
            (flightUavDTO.getId() == null || !flightUavDTO.getId().equals(existFlightUav.getId()))) {
            LogUtil.error("飞行无人机校验失败，唯一识别码已存在: {}", flightUavDTO.getFlightUavSn());
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "该识别码已存在！");
        }
    }
} 