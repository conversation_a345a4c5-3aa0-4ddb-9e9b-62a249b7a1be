package com.deepinnet.skyflow.operationcenter.service.norepeat;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 * Date: 2025/7/30
 * Author: lijunheng
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface NoRepeatSubmit {
    RepeatMode mode() default RepeatMode.PARAM_HASH;

    /****************参数Hash模式**********************/
    /**
     * 参与计算的参数表达式
     *
     * @return
     */
    String[] includeFields() default {};

    String[] excludeFields() default {};

    BizKey[] bizKeys() default {};

    /**
     * 超时时间
     *
     * @return
     */
    long timeout() default 10L;

    /**
     * 时间单位
     *
     * @return
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /****************Token模式**********************/
    /**
     * 单次执行的唯一标识Key，需要设置在请求头中
     *
     * @return
     */
    String tokenKey() default "only-one-token";


    @Retention(RetentionPolicy.RUNTIME)
    @Target({})
    @interface BizKey {
        BizKeyType type();

        String name() default "";
    }
}