package com.deepinnet.skyflow.operationcenter.service.approval;

import com.deepinnet.skyflow.operationcenter.service.approval.entity.*;

import java.util.List;
import java.util.Map;

/**
 * Description:
 * Date: 2025/7/11
 * Author: lijunheng
 */
public interface ApproveInstanceService {

    /**
     * 创建审批
     *
     * @param instance 审批实例
     * @return 审批实例id
     */
    String createApprove(ApprovalInstance instance);

    /**
     * 创建自动通过审批
     *
     * @param submitEntity 自动审批提交数据
     * @return 审批实例id
     */
    String createAutoPassApprove(ApproveSubmitEntity submitEntity);

    /**
     * 终止本次审批
     *
     * @param approvalId
     */
    void stopApprove(String approvalId);

    /**
     * 获取审批实例，通过审批id
     *
     * @param approvalId 审批实例id
     * @return 审批实例
     */
    ApprovalInstance getApprovalInstance(String approvalId);

    /**
     * 获取审批实例列表，通过审批id
     *
     * @param approvalIdList
     * @return
     */
    List<ApprovalInstance> getApprovalInstanceListByApprovalIdList(List<String> approvalIdList);

    /**
     * 获取审批实例列表，通过业务id
     *
     * @param bizIdList
     * @return
     */
    List<ApprovalInstance> getApprovalInstanceListByBizIdList(List<String> bizIdList);

    /**
     * 审批
     *
     * @param approvalEntity 审批信息
     * @return 审批完后审批单的状态
     */
    ApproveStatusEnum doApprove(ApprovalEntity approvalEntity);

    /**
     * 获取审批流程中当前审批层级的审批节点情况
     *
     * @param approvalId
     * @return
     */
    List<ApprovalNode> getCurrentNeedToApproveNodeList(String approvalId);

    /**
     * 获取审批流程中当前审批层级的审批节点情况
     * @param approvalIdList
     * @return
     */
    Map<String, List<ApprovalNode>> getCurrentNeedToApproveNodeMap(List<String> approvalIdList);

    /**
     * 获取审批记录，按时间线倒序排列
     *
     * @param bizId
     * @return
     */
    List<ApprovalRecord> getApprovalRecordMap(String bizId);

    /**
     * 获取审批记录，按时间线倒序排列
     *
     * @param bizIdList
     * @return
     */
    Map<String, List<ApprovalRecord>> getApprovalRecordMap(List<String> bizIdList);

    /**
     * 获取待审批的审批记录
     * @param bizIdList
     * @return
     */
    List<ApprovalInstance> getNeedApproveInstanceList(List<String> bizIdList);
}
