package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 * Date: 2025/7/24
 * Author: lijunheng
 */
@Data
public class ApproveSubmitEntity implements Serializable {

    /**
     * 审批业务类型，比如需求审核、撮合订单审核、需求规划审核
     */
    private String bizType;

    /**
     * 业务ID
     */
    private String bizId;

    /**
     * 审批发起人ID
     */
    private String submitUserId;

    /**
     * 审批完成后的回调参数
     */
    private String callbackParams;

    private String tenantId;

    /**
     * 申请原因
     */
    private String applyReason;
}
