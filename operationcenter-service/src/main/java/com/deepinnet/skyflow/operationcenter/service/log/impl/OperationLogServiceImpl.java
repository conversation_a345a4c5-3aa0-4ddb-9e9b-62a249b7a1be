package com.deepinnet.skyflow.operationcenter.service.log.impl;

import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.OperationLogDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.OperationLogRepository;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogContext;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogService;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.tenant.TenantIdUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 操作日志服务实现类
 *
 * <AUTHOR>
 */
@Service
public class OperationLogServiceImpl implements OperationLogService {

    @Resource
    private OperationLogRepository operationLogRepository;

    /**
     * 模板变量匹配正则表达式
     */
    private static final Pattern TEMPLATE_PATTERN = Pattern.compile("\\{([^}]+)\\}");

    @Override
    public void recordOperationLog(OperationLogContext context, String requestUri, String requestMethod,
                                   String ipAddress, String userAgent) {
        try {
            // 如果上下文为空或者禁用了日志记录，则不记录
            if (context == null || !context.isEnabled() || context.getTemplate() == null) {
                return;
            }

            // 渲染操作详情
            String operationDetail = renderTemplate(context);

            // 构建操作日志实体
            OperationLogDO operationLogDO = buildOperationDO(context, requestUri, requestMethod, ipAddress, userAgent, operationDetail);

            // 保存操作日志
            operationLogRepository.save(operationLogDO);

            LogUtil.info("操作日志记录成功: 用户[{}] 在模块[{}] 执行操作: {}",
                    operationLogDO.getUserNo(), operationLogDO.getModule(), operationDetail);

        } catch (Exception e) {
            LogUtil.error("记录操作日志失败", e);
        }
    }

    @Override
    public String renderTemplate(OperationLogContext context) {
        if (context == null || context.getTemplate() == null) {
            return "";
        }

        String template = context.getTemplate().getTemplate();
        Map<String, Object> variables = context.getVariables();

        if (variables == null || variables.isEmpty()) {
            return template;
        }

        // 使用正则表达式替换模板变量
        Matcher matcher = TEMPLATE_PATTERN.matcher(template);
        StringBuilder result = new StringBuilder();

        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object variableValue = variables.get(variableName);
            String replacement = variableValue != null ? variableValue.toString() : "{" + variableName + "}";
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);

        return result.toString();
    }

    private OperationLogDO buildOperationDO(OperationLogContext context, String requestUri, String requestMethod, String ipAddress, String userAgent, String operationDetail) {
        OperationLogDO operationLogDO = new OperationLogDO();
        operationLogDO.setUserNo(UserUtil.getUserNo());
        operationLogDO.setModule(context.getTemplate().getModule());
        operationLogDO.setOperationDetail(operationDetail);
        operationLogDO.setOperationTime(LocalDateTime.now());
        operationLogDO.setRequestUri(requestUri);
        operationLogDO.setRequestMethod(requestMethod);
        operationLogDO.setIpAddress(ipAddress);
        operationLogDO.setUserAgent(userAgent);
        // 设置请求参数，如果为空则设置为null
        operationLogDO.setRequestParams(context.getRequestParams());
        operationLogDO.setTenantId(TenantIdUtil.getTenantId());
        operationLogDO.setGmtCreated(LocalDateTime.now());
        operationLogDO.setGmtModified(LocalDateTime.now());
        return operationLogDO;
    }
}
