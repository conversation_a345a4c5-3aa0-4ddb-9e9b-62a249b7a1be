package com.deepinnet.skyflow.operationcenter.service.log.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.OperationLogDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.OperationLogRepository;
import com.deepinnet.skyflow.operationcenter.dto.OperationLogDTO;
import com.deepinnet.skyflow.operationcenter.dto.OperationLogQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.OperationTypeDTO;
import com.deepinnet.skyflow.operationcenter.enums.OperationLogTemplateEnum;
import com.deepinnet.skyflow.operationcenter.service.convert.OperationLogConvert;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogContext;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogService;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.tenant.TenantIdUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 操作日志服务实现类
 *
 * <AUTHOR>
 */
@Service
public class OperationLogServiceImpl implements OperationLogService {

    @Resource
    private OperationLogRepository operationLogRepository;

    @Resource
    private OperationLogConvert operationLogConvert;

    /**
     * 模板变量匹配正则表达式
     */
    private static final Pattern TEMPLATE_PATTERN = Pattern.compile("\\{([^}]+)\\}");

    @Override
    public void recordOperationLog(OperationLogContext context, String requestUri, String requestMethod,
                                   String ipAddress, String userAgent) {
        try {
            // 如果上下文为空或者禁用了日志记录，则不记录
            if (context == null || !context.isEnabled() || context.getTemplate() == null) {
                return;
            }

            // 渲染操作详情
            String operationDetail = renderTemplate(context);

            // 构建操作日志实体
            OperationLogDO operationLogDO = buildOperationDO(context, requestUri, requestMethod, ipAddress, userAgent, operationDetail);

            // 保存操作日志
            operationLogRepository.save(operationLogDO);

            LogUtil.info("操作日志记录成功: 用户[{}] 在模块[{}] 执行操作: {}",
                    operationLogDO.getUserName(), operationLogDO.getModule(), operationDetail);

        } catch (Exception e) {
            LogUtil.error("记录操作日志失败", e);
        }
    }

    @Override
    public CommonPage<OperationLogDTO> pageQueryOperationLog(OperationLogQueryDTO queryDTO) {
        Page<OperationLogDO> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        LambdaQueryWrapper<OperationLogDO> queryWrapper = Wrappers.lambdaQuery(OperationLogDO.class)
                .eq(StrUtil.isNotBlank(queryDTO.getUserNo()), OperationLogDO::getUserNo, queryDTO.getUserNo())
                .like(StrUtil.isNotBlank(queryDTO.getUserName()), OperationLogDO::getUserName, queryDTO.getUserName())
                .eq(StrUtil.isNotBlank(queryDTO.getModule()), OperationLogDO::getModule, queryDTO.getModule())
                .like(StrUtil.isNotBlank(queryDTO.getOperationDetail()), OperationLogDO::getOperationDetail, queryDTO.getOperationDetail())
                .eq(StrUtil.isNotBlank(queryDTO.getOperationResult()), OperationLogDO::getOperationResult, queryDTO.getOperationResult())
                .ge(queryDTO.getStartTime() != null, OperationLogDO::getOperationTime, queryDTO.getStartTime())
                .le(queryDTO.getEndTime() != null, OperationLogDO::getOperationTime, queryDTO.getEndTime())
                .eq(OperationLogDO::getTenantId, TenantIdUtil.getTenantId())
                .orderByDesc(OperationLogDO::getOperationTime);

        operationLogRepository.list(queryWrapper);
        return CommonPage.buildPage(page.getPageNum(), page.getPageSize(), page.getPages(), page.getTotal(), operationLogConvert.convertToDTO(page.getResult()));
    }

    @Override
    public OperationLogDTO getOperationLogById(Long id) {
        OperationLogDO operationLogDO = operationLogRepository.getById(id);
        return operationLogConvert.convertToDTO(operationLogDO);
    }

    @Override
    public String renderTemplate(OperationLogContext context) {
        if (context == null || context.getTemplate() == null) {
            return "";
        }

        String template = context.getTemplate().getTemplate();
        Map<String, Object> variables = context.getVariables();

        if (variables == null || variables.isEmpty()) {
            return template;
        }

        // 使用正则表达式替换模板变量
        Matcher matcher = TEMPLATE_PATTERN.matcher(template);
        StringBuilder result = new StringBuilder();

        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object variableValue = variables.get(variableName);
            String replacement = variableValue != null ? variableValue.toString() : "{" + variableName + "}";
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);

        return result.toString();
    }

    private OperationLogDO buildOperationDO(OperationLogContext context, String requestUri, String requestMethod, String ipAddress, String userAgent, String operationDetail) {
        OperationLogDO operationLogDO = new OperationLogDO();
        operationLogDO.setUserNo(UserUtil.getUserNo());
        operationLogDO.setUserName(UserUtil.getUserName());
        operationLogDO.setModule(context.getTemplate().getModule());
        operationLogDO.setOperationDetail(operationDetail);
        operationLogDO.setOperationTime(LocalDateTime.now());
        operationLogDO.setRequestUri(requestUri);
        operationLogDO.setRequestMethod(requestMethod);
        operationLogDO.setIpAddress(ipAddress);
        operationLogDO.setUserAgent(userAgent);
        operationLogDO.setOperationResult(context.getOperationResult());
        operationLogDO.setTenantId(TenantIdUtil.getTenantId());
        operationLogDO.setGmtCreated(LocalDateTime.now());
        operationLogDO.setGmtModified(LocalDateTime.now());
        return operationLogDO;
    }
}
