package com.deepinnet.skyflow.operationcenter.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanCycleDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ScheduleTaskDO;
import com.deepinnet.skyflow.operationcenter.dal.enums.ScheduleTaskTypeEnum;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightPlanCycleRepository;
import com.deepinnet.skyflow.operationcenter.dto.FlightPlanCycleDTO;
import com.deepinnet.skyflow.operationcenter.dto.PageQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.PlanCycleQueryDTO;
import com.deepinnet.skyflow.operationcenter.enums.ScheduleTaskStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightCycleConvert;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.order.FlightCycleService;
import com.deepinnet.skyflow.operationcenter.service.task.ScheduleTaskService;
import com.deepinnet.skyflow.operationcenter.service.util.LocalDateToTimestampUtil;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.vo.FlightPlanCycleVO;
import com.deepinnet.tenant.TenantIdUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/14
 */

@Service
@RequiredArgsConstructor
public class FlightCycleServiceImpl implements FlightCycleService {

    private final static String CYCLE_FLAG = "CYCLE_";

    private final FlightPlanCycleRepository flightPlanCycleRepository;

    private final FlightCycleConvert flightCycleConvert;

    private final ScheduleTaskService taskService;
    
    private final FlightOrderRepository flightOrderRepository;

    private final Environment env;

    private final TransactionTemplate transactionTemplate;

    @Override
    public CommonPage<FlightPlanCycleVO> queryList(PlanCycleQueryDTO dto) {

        Assert.notNull(dto, "pageDto can not be null");

        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());

        List<FlightPlanCycleDO> cycleDOList = flightPlanCycleRepository.list(Wrappers.<FlightPlanCycleDO>lambdaQuery()
                .eq(ObjectUtil.isNotNull(dto.getIsActive()), FlightPlanCycleDO::getIsActive, dto.getIsActive())
                .orderByDesc(FlightPlanCycleDO::getGmtCreated)
                .orderByDesc(FlightPlanCycleDO::getId));

        if (CollUtil.isEmpty(cycleDOList)) {
            return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), 0, 0L, Lists.newArrayList());
        }

        List<FlightPlanCycleVO> result = flightCycleConvert.toVOList(cycleDOList);

        List<String> cycleNos = cycleDOList.stream().map(FlightPlanCycleDO::getCycleNo).collect(Collectors.toList());

        List<FlightOrderDO> orderList = flightOrderRepository.list(Wrappers.<FlightOrderDO>lambdaQuery()
                .in(FlightOrderDO::getCycleNo, cycleNos));
        
        if (CollUtil.isNotEmpty(orderList)) {
            Map<String, List<FlightOrderDO>> orderMap = orderList
                    .stream()
                    .collect(Collectors.groupingBy(FlightOrderDO::getCycleNo));

            result.forEach(e -> {
                List<FlightOrderDO> flightOrderDOS = orderMap.get(e.getCycleNo());
                e.setPlanNums(CollUtil.isNotEmpty(flightOrderDOS) ? flightOrderDOS.size() : 0);
            });
        }

        return CommonPage.buildPage(dto.getPageNum(), dto.getPageSize(), page.getPages(), page.getTotal(), result);
    }

    @Override
    public FlightPlanCycleVO queryDetail(String cycleNo) {

        Assert.hasText(cycleNo, "cycleNo can not be null!");

        FlightPlanCycleDO cycleDO = flightPlanCycleRepository.getOne(Wrappers.<FlightPlanCycleDO>lambdaQuery()
                .eq(FlightPlanCycleDO::getCycleNo, cycleNo));

        return ObjectUtil.isNull(cycleDO) ? null : flightCycleConvert.toVO(cycleDO);
    }

    @Override
    public Boolean save(FlightPlanCycleDTO dto) {

        Assert.notNull(dto, "dto can not be null!");
        Assert.hasText(dto.getCycleName(), "cycleName can not be null!");
        Assert.notNull(dto.getCycleType(), "cycleType can not be null!");
        Assert.notNull(dto.getCycleStart(), "cycleStart can not be null!");
        Assert.notNull(dto.getCycleEnd(), "cycleEnd can not be null!");

        FlightPlanCycleDO cycleDO = buildPlanCycleDO(dto);

        dto.setCycleNo(cycleDO.getCycleNo());
        List<ScheduleTaskDO> tasks = buildScheduleTasks(dto);

        FlightPlanCycleDO existPlanCycle = flightPlanCycleRepository.getOne(Wrappers.<FlightPlanCycleDO>lambdaQuery()
                .eq(FlightPlanCycleDO::getCycleType, cycleDO.getCycleType())
                .eq(FlightPlanCycleDO::getCycleStart, cycleDO.getCycleStart())
                .eq(FlightPlanCycleDO::getCycleEnd, cycleDO.getCycleEnd()));

        if (ObjectUtil.isNotNull(existPlanCycle)) {
            throw new BizException(BizErrorCode.EXIST_CYCLE_PLAN_ERROR.getCode()
                    , BizErrorCode.EXIST_CYCLE_PLAN_ERROR.getDesc());
        }

        try {

            transactionTemplate.executeWithoutResult(e -> {
                flightPlanCycleRepository.save(cycleDO);

                // 定时任务
                taskService.batchInsertScheduleTasks(tasks);
            });

            return true;
        } catch (Exception e) {
            LogUtil.error("周期计划新增异常, 异常信息", e);
            throw new BizException(BizErrorCode.CYCLE_PLAN_SAVE_ERROR.getCode(), BizErrorCode.CYCLE_PLAN_SAVE_ERROR.getDesc());
        }
    }

    @Override
    public Boolean update(FlightPlanCycleDTO dto) {

        Assert.notNull(dto, "dto can not be null!");
        Assert.hasText(dto.getCycleNo(), "cycleNo can not be null!");
        Assert.hasText(dto.getCycleName(), "cycleName can not be null!");
        Assert.notNull(dto.getCycleType(), "cycleType can not be null!");
        Assert.notNull(dto.getCycleStart(), "cycleStart can not be null!");
        Assert.notNull(dto.getCycleEnd(), "cycleEnd can not be null!");

        FlightPlanCycleDO cycleDO = Optional.ofNullable(
                flightPlanCycleRepository
                        .getOne(Wrappers.<FlightPlanCycleDO>lambdaQuery()
                                .eq(FlightPlanCycleDO::getCycleNo, dto.getCycleNo())))
                .orElseThrow(() -> new BizException(BizErrorCode.CYCLE_PLAN_NOT_EXIST_ERROR.getCode()
                        , BizErrorCode.CYCLE_PLAN_NOT_EXIST_ERROR.getDesc()));

        if (!cycleDO.getIsActive()) {
            LogUtil.error("当前规划周期:{}, 状态已过期, 不可修改!", dto.getCycleNo());
            throw new BizException(BizErrorCode.CYCLE_PLAN_EXPIRED_ERROR.getCode()
                    , BizErrorCode.CYCLE_PLAN_EXPIRED_ERROR.getDesc());
        }

        List<FlightOrderDO> orderDOList = flightOrderRepository.list(Wrappers.<FlightOrderDO>lambdaQuery()
                .eq(FlightOrderDO::getCycleNo, dto.getCycleNo()));

        if (CollUtil.isNotEmpty(orderDOList)) {
            LogUtil.error("当前规划周期:{}, 已存在规划", dto.getCycleNo());
            throw new BizException(BizErrorCode.CYCLE_PLAN_EXIST_ORDER_ERROR.getCode()
                    , BizErrorCode.CYCLE_PLAN_EXIST_ORDER_ERROR.getDesc());
        }

        FlightPlanCycleDO updateCycleDO = buildPlanCycleDO(dto);
        updateCycleDO.setId(cycleDO.getId());

        List<ScheduleTaskDO> taskDOS = buildScheduleTasks(dto);

        try {

            transactionTemplate.executeWithoutResult(e -> {
                // 删除定时任务
                taskService.deleteTask(dto.getCycleNo()
                        , ScheduleTaskTypeEnum.CLOSE_CYCLE_PLAN.getType()
                        , TenantIdUtil.getTenantId());

                flightPlanCycleRepository.updateById(updateCycleDO);

                taskService.batchInsertScheduleTasks(taskDOS);
            });

            return true;

        } catch (Exception e) {
            LogUtil.error("规划周期:{}, 更新失败, 数据如下:{}"
                    ,  dto.getCycleNo()
                    , JSONObject.toJSONString(dto)
                    , e);
            throw new BizException(BizErrorCode.CYCLE_PLAN_UPDATE_ERROR.getCode()
                    , BizErrorCode.CYCLE_PLAN_UPDATE_ERROR.getDesc());
        }

    }

    @Override
    public Boolean delete(String cycleNo) {

        Assert.hasText(cycleNo, "cycleNo can not be null!");

        FlightPlanCycleDO cycleDO = Optional.ofNullable(
                        flightPlanCycleRepository
                                .getOne(Wrappers.<FlightPlanCycleDO>lambdaQuery()
                                        .eq(FlightPlanCycleDO::getCycleNo, cycleNo)))
                .orElseThrow(() -> new BizException(BizErrorCode.CYCLE_PLAN_NOT_EXIST_ERROR.getCode()
                        , BizErrorCode.CYCLE_PLAN_NOT_EXIST_ERROR.getDesc()));

        if (!cycleDO.getIsActive()) {
            LogUtil.error("当前规划周期:{}, 状态已过期, 无法删除!", cycleDO);
            throw new BizException(BizErrorCode.CYCLE_PLAN_EXPIRED_ERROR.getCode()
                    , BizErrorCode.CYCLE_PLAN_EXPIRED_ERROR.getDesc());
        }

        List<FlightOrderDO> orderDOList = flightOrderRepository.list(Wrappers.<FlightOrderDO>lambdaQuery()
                .eq(FlightOrderDO::getCycleNo, cycleNo));

        if (CollUtil.isNotEmpty(orderDOList)) {
            LogUtil.error("当前规划周期:{}, 已存在规划, 无法删除", cycleNo);
            throw new BizException(BizErrorCode.CYCLE_PLAN_EXIST_ORDER_ERROR.getCode()
                    , BizErrorCode.CYCLE_PLAN_EXIST_ORDER_ERROR.getDesc());
        }

        try {

            transactionTemplate.executeWithoutResult(e ->{
                // 删除规划
                flightPlanCycleRepository.removeById(cycleDO.getId());

                // 删除定时任务
                taskService.deleteTask(cycleNo
                        , ScheduleTaskTypeEnum.CLOSE_CYCLE_PLAN.getType()
                        , TenantIdUtil.getTenantId());
            });

            return true;
        } catch (Exception e) {
            LogUtil.error("当前规划周期:{}, 删除失败!", cycleNo, e);
            throw new BizException(BizErrorCode.CYCLE_PLAN_DELETE_ERROR.getCode()
                    , BizErrorCode.CYCLE_PLAN_DELETE_ERROR.getDesc());
        }
    }

    private List<ScheduleTaskDO> buildScheduleTasks(FlightPlanCycleDTO dto) {

        List<ScheduleTaskDO> list = Lists.newArrayList();

        ScheduleTaskDO taskDO = new ScheduleTaskDO();
        taskDO.setBizNo(dto.getCycleNo());
        taskDO.setTaskType(ScheduleTaskTypeEnum.CLOSE_CYCLE_PLAN.getType());
        taskDO.setStatus(ScheduleTaskStatusEnum.WAIT_EXECUTE.getStatus());
        String[] active = env.getActiveProfiles();
        String profile = active.length > 0
                ? active[0]
                : env.getDefaultProfiles()[0];
        taskDO.setEnv(profile);
        taskDO.setTriggerTime(LocalDateToTimestampUtil.toEndOfDayTimestamp(dto.getCycleEnd()));
        taskDO.setTenantId(TenantIdUtil.getTenantId());
        list.add(taskDO);

        return list;
    }

    private static FlightPlanCycleDO buildPlanCycleDO(FlightPlanCycleDTO dto) {
        FlightPlanCycleDO cycleDO = new FlightPlanCycleDO();
        if (StrUtil.isEmpty(dto.getCycleNo())) {
            cycleDO.setCycleNo(CYCLE_FLAG + IdUtil.getSnowflakeNextIdStr());
        } else {
            cycleDO.setCycleNo(dto.getCycleNo());
        }
        cycleDO.setCycleName(dto.getCycleName());
        cycleDO.setCycleType(dto.getCycleType().getCode());
        cycleDO.setCycleStart(dto.getCycleStart());
        cycleDO.setCycleEnd(dto.getCycleEnd());
        cycleDO.setCreatorName(UserUtil.getUserName());
        cycleDO.setIsActive(true);
        cycleDO.setTenantId(TenantIdUtil.getTenantId());
        return cycleDO;
    }
}
