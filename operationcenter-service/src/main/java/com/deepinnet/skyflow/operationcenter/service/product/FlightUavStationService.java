package com.deepinnet.skyflow.operationcenter.service.product;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavStationDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavStationQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.StationDistanceQueryDTO;
import com.deepinnet.skyflow.operationcenter.vo.StationDistanceVO;

import java.util.List;

/**
 * 飞行无人机站点服务接口
 *
 * <AUTHOR>
 */
public interface FlightUavStationService {

    /**
     * 保存飞行无人机站点
     *
     * @param flightUavStationDTO 飞行无人机站点数据
     * @return 已保存的飞行无人机站点ID
     */
    String saveFlightUavStation(FlightUavStationDTO flightUavStationDTO);

    /**
     * 更新飞行无人机站点
     *
     * @param flightUavStationDTO 飞行无人机站点数据
     * @return 更新是否成功
     */
    boolean updateFlightUavStation(FlightUavStationDTO flightUavStationDTO);

    /**
     * 根据ID获取飞行无人机站点
     *
     * @param id 飞行无人机站点ID
     * @return 飞行无人机站点数据
     */
    FlightUavStationDTO getFlightUavStationById(Integer id);

    /**
     * 根据站点编号获取飞行无人机站点
     *
     * @param flightStationNo 站点编号
     * @return 飞行无人机站点数据
     */
    FlightUavStationDTO getFlightUavStationByNo(String flightStationNo);

    /**
     * 分页查询飞行无人机站点
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    CommonPage<FlightUavStationDTO> pageQueryFlightUavStation(FlightUavStationQueryDTO queryDTO);

    /**
     * 获取服务商的所有站点
     *
     * @param companyUserNo 服务商编号
     * @return 站点列表
     */
    List<FlightUavStationDTO> getFlightUavStationsByCompanyUserNo(String companyUserNo);

    /**
     * 删除飞行无人机站点
     *
     * @param id 飞行无人机站点ID
     * @return 删除是否成功
     */
    boolean deleteFlightUavStation(Integer id);
    
    /**
     * 按区域查询站点距离并排序
     * 
     * @param queryDTO 查询条件，包含区域多边形WKT
     * @return 分页查询结果，按距离排序
     */
    CommonPage<StationDistanceVO> pageQueryStationsByPolygon(StationDistanceQueryDTO queryDTO);

    /**
     * 根据无人机 no 查询机巢
     * Params:
     * id – 飞行无人机站点ID
     Returns:
     */
    FlightUavStationDTO getFlightUavNo(String flightUavNo);
} 