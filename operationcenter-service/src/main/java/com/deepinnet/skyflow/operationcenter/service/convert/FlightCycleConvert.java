package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanCycleDO;
import com.deepinnet.skyflow.operationcenter.enums.FlightPlanCycleEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightPositionTypeEnum;
import com.deepinnet.skyflow.operationcenter.vo.FlightPlanCycleVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/14
 */

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface FlightCycleConvert {

    List<FlightPlanCycleVO> toVOList(List<FlightPlanCycleDO> doList);

    @Mapping(target = "planNums", ignore = true)
    @Mapping(source = "cycleType", target = "cycleType", qualifiedByName = "cycleTypeConvert")
    @Mapping(source = "gmtCreated", target = "gmtCreated", qualifiedByName = "localDateTimeToLong")
    FlightPlanCycleVO toVO(FlightPlanCycleDO cycleDO);

    @Named("localDateTimeToLong")
    default Long localDateTimeToLong(LocalDateTime time) {
        return time == null ? null : time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    @Named("cycleTypeConvert")
    default FlightPlanCycleEnum cycleTypeConvert(String code) {
        return FlightPlanCycleEnum.getEnumByCode(code);
    }

}
