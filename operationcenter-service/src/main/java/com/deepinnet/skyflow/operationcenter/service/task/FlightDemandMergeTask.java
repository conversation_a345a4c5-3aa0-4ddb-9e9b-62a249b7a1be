package com.deepinnet.skyflow.operationcenter.service.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandMergeHandleDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandMergeHandleRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandRepository;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.enums.MergeStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.context.TenantContext;
import com.deepinnet.skyflow.operationcenter.service.demand.DemandLockHelper;
import com.deepinnet.skyflow.operationcenter.service.demand.FlightDemandService;
import com.deepinnet.skyflow.operationcenter.service.demand.impl.FlightDemandServiceImpl;
import com.deepinnet.skyflow.operationcenter.service.demand.rule.DemandGroupWithSchedule;
import com.deepinnet.skyflow.operationcenter.service.demand.rule.DemandMergeRuleResult;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleContext;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleEngine;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.util.AntColonyTSP;
import com.deepinnet.skyflow.operationcenter.service.util.MergedScheduleTimeEntity;
import com.deepinnet.skyflow.operationcenter.util.GISUtil;
import com.deepinnet.trace.common.CommonFilter;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Coordinate;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Slf4j
@Component
public class FlightDemandMergeTask {

    @Resource
    private RuleEngine ruleEngine;

    @Resource
    private FlightDemandService flightDemandService;

    @Resource
    private FlightDemandRepository flightDemandRepository;

    @Resource
    private FlightDemandMergeHandleRepository flightDemandMergeHandleRepository;

    @Resource
    private DemandLockHelper demandLockHelper;

    //每天凌晨3点执行
    @Scheduled(cron = "0 0 3 * * ?")
    public void execute() {
        log.info("开始执行需求合并任务");

        try {
            TenantContext.disableTenantLine();
            MDC.remove(CommonFilter.TENANT_ID);

            Boolean canExec = null;
            do {
                // 如果获取不到就等10秒后再重试
                if (canExec != null) {
                    ThreadUtil.sleep(10, TimeUnit.SECONDS);
                }
                canExec = demandLockHelper.tryLockMergeDemandTask();
            }
            while(!canExec);

            // 先取消掉那些待合并的合并需求
            cancelPendingMergeDemands();

            // 查询昨天晚上12点前未分配并且未合并的原始需求
            // 待合并的原始需求没有执行合并操作的也是可以重新进入合并筛选流程的
            List<FlightDemandDO> unmergedDemands = queryUnmergedDemands();

            if (CollectionUtils.isEmpty(unmergedDemands)) {
                log.info("没有找到待合并的需求");
                return;
            }

            log.info("找到{}个待合并的需求", unmergedDemands.size());

            // 转换为DTO
            List<FlightDemandDTO> demandDTOList = unmergedDemands.stream()
                    .map(demandDO -> {
                        // 为了规则处理，需要加载完整的需求信息（包括区域、详情等）
                        return flightDemandService.getFlightDemandByNo(demandDO.getDemandNo());
                    })
                    .collect(Collectors.toList());

            //需要按租户分组处理
            Map<String, List<FlightDemandDTO>> tenantDemandListMap = demandDTOList.stream().collect(Collectors.groupingBy(FlightDemandDTO::getTenantId));

            tenantDemandListMap.forEach((tenantId, tenantDemandDTOList) -> {
                // 使用规则引擎执行合并规则
                RuleContext<DemandMergeRuleResult> context = ruleEngine.execute(RuleTypeEnum.DEMAND_MERGE_TYPE, tenantDemandDTOList, tenantId);
                DemandMergeRuleResult ruleResult = context.getPreviousRuleExecResult();
                List<List<FlightDemandDTO>> filterDemandGroup = ruleResult.getFilterDemandGroup();

                if (CollectionUtils.isEmpty(filterDemandGroup)) {
                    log.info("规则执行后，没有找到可合并的需求分组");
                    return;
                }

                log.info("规则执行完成，找到{}个可合并的需求分组", filterDemandGroup.size());

                // 检查是否有时间周期信息
                List<List<DemandGroupWithSchedule>> groupWithSameDemandList = groupWithSameDemand(ruleResult.getDemandGroupsWithSchedule());
                log.info("使用带时间周期信息需求分组解析合并需求建议完成，找到{}个合并需求建议", groupWithSameDemandList.size());

                groupWithSameDemandList.forEach(advise -> {
                    // 创建新的合并需求建议
                    List<MergeDemandCreateDTO> createDTOList = advise.stream().map(schedule -> {
                        MergeDemandCreateDTO createDTO = new MergeDemandCreateDTO();
                        createDTO.setOriginDemandDTOList(schedule.getDemandGroup());
                        createDTO.setTenantId(schedule.getDemandGroup().get(0).getTenantId());

                        // 设置合并后的时间周期信息
                        if (schedule.getMergedScheduleTime() != null) {
                            DemandMergeScheduleDTO scheduleDTO = convertToScheduleDTO(schedule.getMergedScheduleTime());
                            createDTO.setMergeScheduleDTO(scheduleDTO);
                        }

                        //计算飞行轨迹、长度
                        createDTO.setFlightRoutePlan(createFlightRoutePlan(schedule.getDemandGroup()));
                        return createDTO;
                    }).collect(Collectors.toList());
                    flightDemandService.createMergeDemand(createDTOList);
                });
                log.info("合并需求任务执行成功，共生成{}个合并建议，{}个合并需求", groupWithSameDemandList.size(), filterDemandGroup.size());
            });

        } catch (Exception e) {
            log.error("执行需求合并任务失败", e);
        } finally {
            demandLockHelper.unlockMergeDemandTask();
            TenantContext.clear();
        }
    }

    /**
     * 根据是否有相同需求分组
     *
     * @param demandGroupsWithScheduleList
     * @return
     */
    private static List<List<DemandGroupWithSchedule>> groupWithSameDemand(List<DemandGroupWithSchedule> demandGroupsWithScheduleList) {
        int n = demandGroupsWithScheduleList.size();
        UnionFind uf = new UnionFind(n);

        // Map<demandNo, 对应的DemandGroupWithSchedule索引列表>
        Map<String, List<Integer>> demandIdToGroupIndices = new HashMap<>();

        for (int i = 0; i < n; i++) {
            List<FlightDemandDTO> demands = demandGroupsWithScheduleList.get(i).getDemandGroup();
            for (FlightDemandDTO demand : demands) {
                String demandNo = demand.getDemandNo(); // 根据 id 判断相同需求
                demandIdToGroupIndices
                        .computeIfAbsent(demandNo, k -> new ArrayList<>())
                        .add(i);
            }
        }

        // 将同一个需求出现过的 DemandGroupWithSchedule 联通
        for (List<Integer> groupIndices : demandIdToGroupIndices.values()) {
            for (int i = 1; i < groupIndices.size(); i++) {
                uf.union(groupIndices.get(0), groupIndices.get(i));
            }
        }

        // 聚合结果
        Map<Integer, List<DemandGroupWithSchedule>> groupMap = new HashMap<>();
        for (int i = 0; i < n; i++) {
            int root = uf.find(i);
            groupMap
                    .computeIfAbsent(root, k -> new ArrayList<>())
                    .add(demandGroupsWithScheduleList.get(i));
        }

        return new ArrayList<>(groupMap.values());
    }

    // 并查集
    static class UnionFind {
        private final int[] parent;

        public UnionFind(int size) {
            parent = new int[size];
            for (int i = 0; i < size; i++) parent[i] = i;
        }

        public int find(int x) {
            if (parent[x] != x) {
                // 路径压缩，减少每次都要递归遍历到顶层节点
                parent[x] = find(parent[x]);
            }
            return parent[x];
        }

        public void union(int x, int y) {
            int fx = find(x);
            int fy = find(y);
            if (fx != fy) {
                parent[fx] = fy;
            }
        }
    }

    private FlightRoutePlan createFlightRoutePlan(List<FlightDemandDTO> demandGroup) {
        if (CollectionUtils.isEmpty(demandGroup)) {
            return new FlightRoutePlan();
        }

        // 收集所有需求的所有区域
        List<FlightDemandAreaDTO> allAreas = new ArrayList<>();
        for (FlightDemandDTO demand : demandGroup) {
            allAreas.addAll(demand.getAreaList());
        }

        if (allAreas.isEmpty()) {
            return new FlightRoutePlan();
        }

        // 使用蚁群算法求解TSP
        FlightRoutePlan flightRoutePlan = calculateFlightRoutePlan(allAreas);
        return flightRoutePlan;
    }

    /**
     * 计算最优飞行路径
     *
     * @param allAreas
     * @return
     */
    private FlightRoutePlan calculateFlightRoutePlan(List<FlightDemandAreaDTO> allAreas) {
        // 构建距离矩阵
        int n = allAreas.size();
        double[][] dist = new double[n][n];

        for (int i = 0; i < n; i++) {
            FlightDemandAreaDTO area1 = allAreas.get(i);
            Coordinate coord1 = getAreaCenterCoordinate(area1);

            for (int j = 0; j < n; j++) {
                if (i == j) {
                    dist[i][j] = 0.0;
                } else {
                    FlightDemandAreaDTO area2 = allAreas.get(j);
                    Coordinate coord2 = getAreaCenterCoordinate(area2);

                    if (coord1 != null && coord2 != null) {
                        dist[i][j] = GISUtil.calculateDistanceInMeters(coord1, coord2);
                    } else {
                        dist[i][j] = Double.MAX_VALUE / 2; // 避免溢出
                    }
                }
            }
        }
        AntColonyTSP aco = new AntColonyTSP(dist,
                50,     // 蚂蚁数
                200,    // 迭代次数
                1.0,    // alpha：信息素重要性
                5.0,    // beta：距离重要性
                0.5,    // rho：信息素蒸发率
                100.0   // Q：信息素释放常数
        );
        aco.run();

        // 构建飞行路线计划
        FlightRoutePlan flightRoutePlan = new FlightRoutePlan();
        flightRoutePlan.setDistance(aco.getBestLength());

        // 根据最优路径构建飞行路线列表
        List<FlightRoute> flightRouteList = new ArrayList<>();
        int[] bestTour = aco.getBestTour();
        List<Double> stepDistances = aco.getStepDistances(); // 使用TSP提供的每一步距离

        if (bestTour != null && bestTour.length > 0) {
            for (int i = 0; i < bestTour.length; i++) {
                int areaIndex = bestTour[i];
                FlightDemandAreaDTO area = allAreas.get(areaIndex);
                FlightRoute flightRoute = new FlightRoute();
                flightRoute.setOrder(i + 1);
                flightRoute.setDemandAreaDTO(area);
                flightRoute.setDistance(stepDistances.get(i));
                flightRouteList.add(flightRoute);
            }
        }

        flightRoutePlan.setFlightRouteList(flightRouteList);
        return flightRoutePlan;
    }

    /**
     * 获取区域的中心点坐标
     */
    private Coordinate getAreaCenterCoordinate(FlightDemandAreaDTO area) {
        try {
            if (area.getCenterPointLongitude() != null && area.getCenterPointLatitude() != null) {
                double longitude = Double.parseDouble(area.getCenterPointLongitude());
                double latitude = Double.parseDouble(area.getCenterPointLatitude());
                return new Coordinate(longitude, latitude);
            }
        } catch (NumberFormatException e) {
            log.warn("坐标格式错误，需求编号: {}", area.getDemandCode());
        }
        return null;
    }

    /**
     * 查询未合并的需求
     * 过滤条件：
     * 1. 是原始需求（不是合并需求）
     * 2. 未分配给服务商
     * 3. 合并状态为未合并
     * 4. 距离起飞时间大于72小时（通过联表查询flight_demand_routine_inspection获取demand_start_time）
     *
     * @return 未合并的需求列表
     */
    private List<FlightDemandDO> queryUnmergedDemands() {
        List<FlightDemandDO> allDemands = new ArrayList<>();
        Long lastId = 0L;  // 从ID为0开始查询
        int batchSize = 100;  // 每批处理100条数据
        
        while (true) {
            // 使用游标分页查询方法
            List<FlightDemandDO> batchDemands = flightDemandRepository.selectPendingMergeDemands(lastId, batchSize);
            
            if (CollectionUtil.isEmpty(batchDemands)) {
                break;  // 没有更多数据，退出循环
            }
            
            log.debug("本批次找到{}个待合并的需求，lastId: {}", batchDemands.size(), lastId);
            
            allDemands.addAll(batchDemands);
            
            // 更新lastId为本批次最后一个记录的ID
            lastId = batchDemands.get(batchDemands.size() - 1).getId();
            
            if (batchDemands.size() < batchSize) {
                break;  // 最后一批数据，退出循环
            }
        }
        
        log.debug("总共找到{}个待合并的需求", allDemands.size());
        return allDemands;
    }

    /**
     * 取消待合并的合并需求
     */
    private void cancelPendingMergeDemands() {
        // 查询出待合并的合并建议，从flight_demand_merge_handle表查询出merge_handle_code
        List<FlightDemandMergeHandleDO> pendingMergeHandles = flightDemandMergeHandleRepository.list(
                Wrappers.lambdaQuery(FlightDemandMergeHandleDO.class)
                        .eq(FlightDemandMergeHandleDO::getMergeStatus, MergeStatusEnum.PENDING.getCode())
        );

        if (CollectionUtils.isEmpty(pendingMergeHandles)) {
            log.info("没有找到需要取消的待合并的合并建议");
            return;
        }

        // 循环执行取消操作
        for (FlightDemandMergeHandleDO mergeHandle : pendingMergeHandles) {
            try {
                CancelMergeDTO cancelMergeDTO = new CancelMergeDTO();
                cancelMergeDTO.setMergeHandleCode(mergeHandle.getMergeHandleCode());
                cancelMergeDTO.setOperatorUserNo(FlightDemandServiceImpl.SYSTEM_USER_NO);
                cancelMergeDTO.setOperatorUserName(FlightDemandServiceImpl.SYSTEM_USER_NAME);

                flightDemandService.cancelMerge(cancelMergeDTO);
                log.info("成功取消合并建议: {}", mergeHandle.getMergeHandleCode());
            } catch (Exception e) {
                log.error("取消合并建议失败，合并处理编号: {}", mergeHandle.getMergeHandleCode(), e);
            }
        }
    }

    private DemandMergeScheduleDTO convertToScheduleDTO(MergedScheduleTimeEntity mergedScheduleTime) {
        if (mergedScheduleTime == null) {
            return null;
        }

        DemandMergeScheduleDTO scheduleDTO = new DemandMergeScheduleDTO();
        scheduleDTO.setMergeScheduleStartDate(mergedScheduleTime.getStartDate());
        scheduleDTO.setMergeScheduleEndDate(mergedScheduleTime.getEndDate());
        scheduleDTO.setMergeScheduleStartTime(mergedScheduleTime.getEarliestStartTime());
        scheduleDTO.setMergeScheduleEndTime(mergedScheduleTime.getLatestEndTime());
        scheduleDTO.setContinuousDays(mergedScheduleTime.getContinuousDays());
        return scheduleDTO;
    }

    public static void main(String[] args) {
        FlightDemandDTO a = new FlightDemandDTO();
        a.setDemandNo("A");
        FlightDemandDTO b = new FlightDemandDTO();
        b.setDemandNo("B");
        FlightDemandDTO c = new FlightDemandDTO();
        c.setDemandNo("C");
        FlightDemandDTO d = new FlightDemandDTO();
        d.setDemandNo("D");
        FlightDemandDTO e = new FlightDemandDTO();
        e.setDemandNo("E");

        List<DemandGroupWithSchedule> demandGroupsWithScheduleList = ListUtil.toList(
                new DemandGroupWithSchedule(List.of(a), null),
                new DemandGroupWithSchedule(List.of(d, a), null),
                new DemandGroupWithSchedule(List.of(c), null),
                new DemandGroupWithSchedule(List.of(a, b), null),
                new DemandGroupWithSchedule(List.of(d, e), null)
        );
        List<List<DemandGroupWithSchedule>> lists = groupWithSameDemand(demandGroupsWithScheduleList);
        System.out.println(lists);
    }
}
