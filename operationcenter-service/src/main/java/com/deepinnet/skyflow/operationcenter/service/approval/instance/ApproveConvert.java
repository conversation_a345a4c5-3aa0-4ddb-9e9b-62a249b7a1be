package com.deepinnet.skyflow.operationcenter.service.approval.instance;

import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalConfigRuleDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalInstanceDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalNodeDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ApprovalStepDO;
import com.deepinnet.skyflow.operationcenter.dto.ApproveSubmitDTO;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.*;
import com.deepinnet.skyflow.operationcenter.vo.ApprovalChooseConfigRuleVO;
import com.deepinnet.skyflow.operationcenter.vo.ApprovalConfigRuleVO;
import com.deepinnet.skyflow.operationcenter.vo.ApprovalRecordVO;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * ApprovalConvert
 * Author: chenkaiyang
 * Date: 2024/7/31
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface ApproveConvert {

    ApprovalEntity toApprovalEntity(ApproveSubmitDTO submitDTO);

    List<ApprovalRecordVO> toApprovalRecordVO(List<ApprovalRecord> approvalRecordList);

    @Mapping(target = "status", source = "status", qualifiedByName = "statusEnumToString")
    ApprovalRecordVO toApprovalRecordVO(ApprovalRecord approvalRecord);

    ApprovalInstanceDO toInstanceDO(ApprovalInstance instance);

    ApprovalStepDO toStepDO(ApprovalStep step);

    ApprovalNodeDO toNodeDO(ApprovalNode node);

    List<ApprovalInstance> toInstanceList(List<ApprovalInstanceDO> instanceDOList);

    ApprovalInstance toInstance(ApprovalInstanceDO instanceDO);

    ApprovalStep toStep(ApprovalStepDO approvalStepDO);

    ApprovalNode toNode(ApprovalNodeDO approvalNodeDO);

    List<ApprovalNode> toNodeList(List<ApprovalNodeDO> approvalNodeDOList);

    @Mapping(target = "approvalChooseConfigRuleList", source = "approvalChooseConfigRuleList", qualifiedByName = "jsonToList")
    @Mapping(target = "approveTimeoutConfig", source = "approveTimeoutConfig", qualifiedByName = "jsonToApproveTimeoutConfig")
    @Mapping(target = "approveOrgUserConfig", source = "approveOrgUserConfig", qualifiedByName = "jsonToApproveOrgUserConfig")
    ApprovalInstanceConfigRule toEntity(ApprovalConfigRuleDO approvalConfigRuleDO);

    @Mapping(target = "approvalChooseConfigRuleList", source = "approvalChooseConfigRuleList", qualifiedByName = "listToJson")
    @Mapping(target = "approveTimeoutConfig", source = "approveTimeoutConfig", qualifiedByName = "approveTimeoutConfigToJson")
    @Mapping(target = "approveOrgUserConfig", source = "approveOrgUserConfig", qualifiedByName = "approveOrgUserConfigToJson")
    ApprovalConfigRuleDO toDO(ApprovalInstanceConfigRule approvalInstanceConfigRule);

    List<ApprovalInstanceConfigRule> toEntityList(List<ApprovalConfigRuleDO> approvalConfigRuleDOList);

    ApprovalConfigRuleVO toVO(ApprovalInstanceConfigRule approvalInstanceConfigRule);

    List<ApprovalConfigRuleVO> toVOList(List<ApprovalInstanceConfigRule> approvalInstanceConfigRuleList);

    @Named("jsonToApproveTimeoutConfig")
    default ApproveTimeoutConfig jsonToApproveTimeoutConfig(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtil.parseJson(json, ApproveTimeoutConfig.class);
    }

    @Named("approveTimeoutConfigToJson")
    default String approveTimeoutConfigToJson(ApproveTimeoutConfig approveTimeoutConfig) {
        if (approveTimeoutConfig == null) {
            return null;
        }
        return JsonUtil.toJsonStr(approveTimeoutConfig);
    }

    @Named("jsonToApproveOrgUserConfig")
    default ApproveOrgUserConfig jsonToApproveOrgUserConfig(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtil.parseJson(json, ApproveOrgUserConfig.class);
    }

    @Named("approveOrgUserConfigToJson")
    default String approveOrgUserConfigToJson(ApproveOrgUserConfig approveOrgUserConfig) {
        if (approveOrgUserConfig == null) {
            return null;
        }
        return JsonUtil.toJsonStr(approveOrgUserConfig);
    }

    @Named("jsonToList")
    default List<ApprovalChooseConfigRule> jsonToList(String json) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        return JsonUtil.parseToList(json, ApprovalChooseConfigRule.class);
    }

    @Named("listToJson")
    default String listToJson(List<ApprovalChooseConfigRule> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return JsonUtil.toJsonStr(list);
    }

    @AfterMapping
    default void afterMappingInstanceDO(@MappingTarget ApprovalInstanceDO target, ApprovalInstance source) {
        if (source.getStatus() != null) {
            target.setStatus(source.getStatus().name());
        }
    }

    @AfterMapping
    default void afterMappingInstance(@MappingTarget ApprovalInstance target, ApprovalInstanceDO source) {
        if (source.getStatus() != null) {
            target.setStatus(stringToStatusEnum(source.getStatus()));
        }
    }

    @AfterMapping
    default void afterMappingStepDO(@MappingTarget ApprovalStepDO target, ApprovalStep source) {
        if (source.getStatus() != null) {
            target.setStatus(source.getStatus().name());
        }
        if (source.getApproveMode() != null) {
            target.setApproveMode(source.getApproveMode().name());
        }
    }

    @AfterMapping
    default void afterMappingStep(@MappingTarget ApprovalStep target, ApprovalStepDO source) {
        if (source.getStatus() != null) {
            target.setStatus(stringToStatusEnum(source.getStatus()));
        }
        if (source.getApproveMode() != null) {
            target.setApproveMode(stringToApproveModeEnum(source.getApproveMode()));
        }
    }

    @AfterMapping
    default void afterMappingNodeDO(@MappingTarget ApprovalNodeDO target, ApprovalNode source) {
        if (source.getStatus() != null) {
            target.setStatus(source.getStatus().name());
        }
    }

    @AfterMapping
    default void afterMappingNode(@MappingTarget ApprovalNode target, ApprovalNodeDO source) {
        if (source.getStatus() != null) {
            target.setStatus(stringToStatusEnum(source.getStatus()));
        }
    }

    /**
     * 字符串转状态枚举
     */
    default ApproveStatusEnum stringToStatusEnum(String status) {
        if (status == null || status.trim().isEmpty()) {
            return null;
        }
        try {
            return ApproveStatusEnum.valueOf(status);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("无效的审批状态: " + status, e);
        }
    }

    @Named("statusEnumToString")
    default String statusEnumToString(ApproveStatusEnum status) {
        if (status == null) {
            return null;
        }
        return status.name();
    }

    /**
     * 字符串转审批模式枚举
     */
    default ApproveModeEnum stringToApproveModeEnum(String approveMode) {
        if (approveMode == null || approveMode.trim().isEmpty()) {
            return null;
        }
        try {
            return ApproveModeEnum.valueOf(approveMode);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("无效的审批模式: " + approveMode, e);
        }
    }
}
