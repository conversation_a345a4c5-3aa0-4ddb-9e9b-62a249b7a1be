package com.deepinnet.skyflow.operationcenter.service.util;

import com.deepinnet.skyflow.operationcenter.vo.FlightStatisticsBasicVO;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/18
 */
public final class StatCalcUtil {

    /**
     * 默认保留 2 位小数, 舍去后续位数
     */
    private static final int SCALE = 2;
    private static final RoundingMode DOWN = RoundingMode.DOWN;

    private StatCalcUtil() {}

    /**
     * 只计算「数量」相关的四个字段：value / baseValue / delta / deltaRate
     * @param current 当期数量 (今天、当前月等)
     * @param base    基准数量 (昨天、上月等)
     */
    public static FlightStatisticsBasicVO buildBasic(int current, int base) {

        BigDecimal cur  = BigDecimal.valueOf(current);
        BigDecimal baseBd = BigDecimal.valueOf(base == 0 ? 1 : base);

        BigDecimal delta = cur.subtract(BigDecimal.valueOf(base))
                .setScale(0, DOWN);

        BigDecimal rate = delta.multiply(BigDecimal.valueOf(100))
                .divide(baseBd, SCALE, DOWN);

        // 字符串格式化
        String deltaStr = delta.signum() > 0 ? "+" + delta : delta.toPlainString();
        String rateStr  = rate.toPlainString();
        String signedRateStr = rate.signum() > 0 ? "+" + rateStr + "%" : rateStr + "%";

        FlightStatisticsBasicVO vo = new FlightStatisticsBasicVO();
        vo.setValue(current);
        vo.setBaseValue(base);
        vo.setDelta(delta.intValue());
        vo.setDeltaRate(rateStr);
        vo.setSignedDeltaRate(signedRateStr);
        return vo;
    }

    /**
     * 如果还需要完成率，可调用此重载。
     * @param current           当期数量
     * @param base              基准数量
     * @param finishedCurrent   当期已完成数量
     * @param finishedBase      基准期已完成数量
     */
    public static FlightStatisticsBasicVO buildWithFinish(int current, int base, int finishedCurrent, int finishedBase) {

        FlightStatisticsBasicVO vo = buildBasic(current, base);

        BigDecimal finCurRate = rate(finishedCurrent, current);
        BigDecimal finBaseRate = rate(finishedBase, base);

        vo.setFinishRate(finCurRate.toPlainString() + "%");
        vo.setBaseFinishRate(finBaseRate.toPlainString() + "%");
        return vo;
    }

    /**
     * 内部工具：完成率 = finished / total × 100
     */
    private static BigDecimal rate(int finished, int total) {
        if (total == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(finished)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(total), SCALE, DOWN);
    }
}