package com.deepinnet.skyflow.operationcenter.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightMergeDemandRelationDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightMergeDemandRelationRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightPlanRepository;
import com.deepinnet.skyflow.operationcenter.enums.FlightPlanApplyTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightPlanStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.order.FlightPlanService;
import com.deepinnet.spatiotemporalplatform.dto.BatchQueryPlanDTO;
import com.deepinnet.spatiotemporalplatform.vo.FlightCountVO;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 飞行计划服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
@Service
public class FlightPlanServiceImpl implements FlightPlanService {

    @Resource
    private FlightMergeDemandRelationRepository mergeDemandRelationRepository;

    @Resource
    private FlightPlanRepository flightPlanRepository;
    
    @Override
    public List<FlightCountVO> batchQueryFlightCountByCustomer(BatchQueryPlanDTO dto) {

        Assert.notNull(dto, "查询参数不能为空");
        if (CollectionUtil.isEmpty(dto.getBizNo())) {
            return new ArrayList<>();
        }

        List<FlightMergeDemandRelationDO> demandQueryHelperList = mergeDemandRelationRepository.list(Wrappers.<FlightMergeDemandRelationDO>lambdaQuery()
                .in(FlightMergeDemandRelationDO::getOriginalDemandCode, dto.getBizNo()));

        if (CollUtil.isEmpty(demandQueryHelperList)) {
            return new ArrayList<>();
        }

        List<String> targetDemandNoList = demandQueryHelperList.stream()
            .map(FlightMergeDemandRelationDO::getTargetDemandCode)
            .distinct()
            .collect(Collectors.toList());

        List<FlightPlanDO> flightPlanList = flightPlanRepository.list(
            Wrappers.<FlightPlanDO>lambdaQuery()
                    .in(FlightPlanDO::getBizNo, targetDemandNoList)
                    .in(FlightPlanDO::getApplyType, FlightPlanApplyTypeEnum.SHORT_TERM)
                    .in(FlightPlanDO::getStatus, FlightPlanStatusEnum.FLIGHT_STATUS)
        );

        if (CollUtil.isEmpty(flightPlanList)) {
            return dto.getBizNo().stream()
                .map(originalDemandNo -> {
                    FlightCountVO flightCountVO = new FlightCountVO();
                    flightCountVO.setBizNo(originalDemandNo);
                    flightCountVO.setCount(0);
                    return flightCountVO;
                })
                .collect(Collectors.toList());
        }

        Map<String, Integer> targetDemandFlightCountMap = flightPlanList.stream()
            .collect(Collectors.groupingBy(
                FlightPlanDO::getBizNo,
                Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
            ));

        Map<String, List<String>> targetToOriginalMap = demandQueryHelperList.stream()
            .collect(Collectors.groupingBy(
                    FlightMergeDemandRelationDO::getTargetDemandCode,
                Collectors.mapping(FlightMergeDemandRelationDO::getOriginalDemandCode, Collectors.toList())
            ));

        List<FlightCountVO> result = new ArrayList<>();
        
        for (String originalDemandNo : dto.getBizNo()) {
            FlightCountVO flightCountVO = new FlightCountVO();
            flightCountVO.setBizNo(originalDemandNo);
            
            List<String> targetDemandNo = demandQueryHelperList.stream()
                .filter(helper -> originalDemandNo.equals(helper.getOriginalDemandCode()))
                .map(FlightMergeDemandRelationDO::getTargetDemandCode)
                .collect(Collectors.toList());
            
            if (CollUtil.isNotEmpty(targetDemandNo)) {
                AtomicInteger totalFlightCount = new AtomicInteger();
                targetDemandNo.forEach(target -> {
                    int count = Optional.ofNullable(targetDemandFlightCountMap.get(target)).orElse(0);
                    List<String> originalDemandList = targetToOriginalMap.get(target);

                    if (CollUtil.isNotEmpty(originalDemandList)) {
                        totalFlightCount.addAndGet(count);
                    }
                });

                flightCountVO.setCount(totalFlightCount.get());
            } else {
                flightCountVO.setCount(0);
            }
            
            result.add(flightCountVO);
        }

        return result;
    }
}
