package com.deepinnet.skyflow.operationcenter.service.util;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/18
 */
public final class DateTimeRange {

    public static ZoneId zoneId = ZoneId.of("Asia/Shanghai");

    /**
     * 不可变数据结构，保存开始/结束时间
     */
    @Getter
    @Setter
    public static final class Range {
        private final LocalDateTime start;
        private final LocalDateTime end;

        private Range(LocalDateTime start, LocalDateTime end) {
            this.start = start;
            this.end = end;
        }

        @Override
        public String toString() {
            return "Range{start=" + start + ", end=" + end + '}';
        }
    }

    /**
     * 禁止实例化
     */
    private DateTimeRange() {}

    /**
     * 任意一天的开始 & 结束（00:00:00 – 23:59:59.999999999）
     * @param date 日期
     * @return Range对象
     */
    public static Range of(LocalDate date) {
        return new Range(date.atStartOfDay(), date.atTime(LocalTime.MAX));
    }

    /**
     * 今天
     * @return 今天的开始和结束时间
     */
    public static Range today() {
        return of(LocalDate.now());
    }

    /**
     * 昨天
     * @return 昨天的开始时间和结束时间
     */
    public static Range yesterday() {
        return of(LocalDate.now().minusDays(1));
    }

    public static LocalDate todayDate() {
        return LocalDate.now();
    }

    public static LocalDate yesterdayDate() {
        return LocalDate.now().minusDays(1);
    }

}
