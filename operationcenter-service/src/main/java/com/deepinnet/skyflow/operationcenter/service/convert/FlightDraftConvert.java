package com.deepinnet.skyflow.operationcenter.service.convert;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDraftDO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDraftDTO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * <p>
 * 飞行数据转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface FlightDraftConvert {


    FlightDraftDTO convertToDTO(FlightDraftDO flightDraftDO);

    FlightDraftDO convertToEntity(FlightDraftDTO flightDraftDTO);
} 