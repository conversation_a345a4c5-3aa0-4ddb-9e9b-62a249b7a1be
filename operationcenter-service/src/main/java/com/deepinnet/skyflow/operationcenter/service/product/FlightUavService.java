package com.deepinnet.skyflow.operationcenter.service.product;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.vo.FlightUavGroupVO;
import com.deepinnet.spatiotemporalplatform.dto.PositionQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.RealTimeUavFlightVO;
import com.deepinnet.spatiotemporalplatform.vo.UavFlightTrackVO;

import java.util.List;

/**
 * 飞行无人机服务接口
 *
 * <AUTHOR>
 */
public interface FlightUavService {

    /**
     * 保存飞行无人机
     *
     * @param flightUavDTO 飞行无人机数据
     * @return 已保存的飞行无人机编号
     */
    String saveFlightUav(FlightUavDTO flightUavDTO);

    /**
     * 更新飞行无人机
     *
     * @param flightUavDTO 飞行无人机数据
     * @return 更新是否成功
     */
    boolean updateFlightUav(FlightUavDTO flightUavDTO);

    /**
     * 根据ID获取飞行无人机
     *
     * @param id 飞行无人机ID
     * @return 飞行无人机数据
     */
    FlightUavDTO getFlightUavById(Integer id);

    /**
     * 根据编号获取飞行无人机
     *
     * @param flightUavNo 飞行无人机编号
     * @return 飞行无人机数据
     */
    FlightUavDTO getFlightUavByNo(String flightUavNo);
    
    /**
     * 根据服务商编号获取飞行无人机列表
     *
     * @param supplierUserNo 服务商编号
     * @return 飞行无人机数据列表
     */
    List<FlightUavDTO> getFlightUavListBySupplierUserNo(String supplierUserNo);
    
    /**
     * 根据服务商编号获取按机型分组的飞行无人机列表
     * 
     * @param supplierUserNo 服务商编号
     * @return 按机型分组的飞行无人机列表
     * @deprecated 使用 {@link #getUavGroupByModel(FlightUavGroupQueryDTO)} 代替
     */
    @Deprecated
    List<FlightUavGroupVO> getUavGroupByModel(String supplierUserNo);

    /**
     * 根据查询条件获取按机型分组的飞行无人机列表
     * 
     * @param queryDTO 查询条件
     * @return 按机型分组的飞行无人机列表
     */
    List<FlightUavGroupVO> getUavGroupByModel(FlightUavGroupQueryDTO queryDTO);

    /**
     * 分页查询飞行无人机
     *
     * @param queryDTO 查询条件
     * @return 分页查询结果
     */
    CommonPage<FlightUavDTO> pageQueryFlightUav(FlightUavQueryDTO queryDTO);

    /**
     * 删除飞行无人机
     *
     * @param id 飞行无人机ID
     * @return 删除是否成功
     */
    boolean deleteFlightUav(Integer id);
    
    /**
     * 批量操作飞行无人机（删除、新增和更新）
     *
     * @param batchOperationDTO 批量操作数据
     * @return 操作是否成功
     */
    boolean batchOperateFlightUavs(FlightUavBatchOperationDTO batchOperationDTO);

    List<UavFlightTrackVO> getUavTrajectory(PositionWebQueryDTO positionQueryDTO, boolean queryPlan);

    RealTimeUavFlightPositionDTO getUavFlightStatus(FlightUavRealtimePositionQueryDTO positionQueryDTO);

    FlightUavDTO getFlightUavBySn(String flightUavSn);

    List<RealTimeUavFlightVO> getUavPosition(PositionWebQueryDTO queryDTO);
} 