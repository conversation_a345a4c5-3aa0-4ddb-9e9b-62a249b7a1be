package com.deepinnet.skyflow.operationcenter.service.demand.impl.matching;

import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.enums.FlightUavStationStatusEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:
 * Date: 2025/4/19
 * Author: lijunheng
 */
@Component
public class NestProviderMatchingRule {

    @Resource
    private DynamicSqlInvokeHelper dynamicSqlInvokeHelper;

    /**
     * 获取最匹配的服务商
     *
     * @param demandDTO
     * @param flightUavBmList
     * @return
     */
    public String bestMatchProvider(FlightDemandDTO demandDTO, List<String> flightUavBmList) {
        // 先判断是否有任意符合机巢
        DynamicSqlEntity queryAllMatchNestSql = queryAllMatchNest(demandDTO, flightUavBmList);
        boolean exist = dynamicSqlInvokeHelper.exist(queryAllMatchNestSql);
        if (!exist) {
            return null;
        }

        DynamicSqlEntity filterWhiteSql = filterWhiteListProviders(queryAllMatchNestSql, demandDTO);
        String bestMatchProvider = dynamicSqlInvokeHelper.queryForObject(filterWhiteSql, String.class);

        if (bestMatchProvider == null) {
            //直接找非白名单内的
            DynamicSqlEntity bestMatchProviderSql = findBestMatchingProvider(queryAllMatchNestSql, demandDTO);
            bestMatchProvider = dynamicSqlInvokeHelper.queryForObject(bestMatchProviderSql, String.class);
        }
        return bestMatchProvider;
    }

    /**
     * 获取匹配的所有服务商
     *
     * @param demandDTO
     * @param flightUavBmList
     * @return
     */
    public List<String> matchProviderList(FlightDemandDTO demandDTO, List<String> flightUavBmList) {
        // 先判断是否有任意符合机巢
        DynamicSqlEntity queryAllMatchNestSql = queryAllMatchNest(demandDTO, flightUavBmList);
        return dynamicSqlInvokeHelper.queryForList(queryAllMatchNestSql, String.class);
    }

    /**
     * 判定是否有符合需求的机巢（同时满足以下条件）
     * 有机巢肯定会有对应的飞行器
     * <p>
     * 一、机巢状态 = "正常"
     * 二、机巢飞行器机型 符合 订单要求机型
     * 机型服务：机巢飞行器机型==机型服务所属机型
     * 场景服务：机巢飞行器机型属于场景服务所属机型
     * <p>
     * 1. 如果是**日常巡检**需求，则判断机巢位置离用户给定的监控区域中心点位置距离不超过飞行器最大飞行半径
     * 2. 如果是**应急巡检**需求，则判断机巢离事件发生地中心位置的距离不超过飞行器最大飞行半径
     *
     * @param demand 飞行需求
     * @return 是否存在符合的机巢
     */
    private DynamicSqlEntity queryAllMatchNest(FlightDemandDTO demand, List<String> flightUavBmList) {
        //把这段sql作为子查询，不然就会需要两次计算距离
        DynamicSqlEntity dynamicSqlEntity = new DynamicSqlEntity();
        dynamicSqlEntity.setSelect("select distinct station.company_user_no");
        dynamicSqlEntity.setFrom("from flight_uav_station station");
        List<String> joinOnList = dynamicSqlEntity.getJoinOnList();
        joinOnList.add("join flight_uav uav on station.flight_uav_no = uav.flight_uav_no");
        joinOnList.add("join flight_uav_bm uav_bm on uav_bm.flight_uav_bm_no = uav.flight_uav_bm_no");
        joinOnList.add("join product_service_supplier pss on pss.user_no = station.company_user_no");
        List<String> whereList = dynamicSqlEntity.getWhereList();

        // 使用更安全的IN查询格式，并添加参数
        whereList.add("uav.flight_uav_bm_no in (:flightUavBmList) and station.status = :status");
        whereList.add("station.tenant_id = :tenantId and uav.tenant_id = :tenantId and uav_bm.tenant_id = :tenantId and pss.tenant_id = :tenantId");
        whereList.add("station.is_deleted = :deleted and uav.is_deleted = :deleted and uav_bm.is_deleted = :deleted and pss.is_deleted = :deleted");
        whereList.add("pss.approval_status = 1");

        // 添加参数值到预处理参数列表
        dynamicSqlEntity.addParameter("flightUavBmList", flightUavBmList);
        dynamicSqlEntity.addParameter("status", FlightUavStationStatusEnum.NORMAL.getStatus());
        dynamicSqlEntity.addParameter("tenantId", demand.getTenantId());
        dynamicSqlEntity.addParameter("deleted", false);

        whereList.add("ST_DWithin(station.coordinate::geography, ST_SetSRID(ST_MakePoint(:lng, :lat), 4326)::geography, uav_bm.flight_uav_radius * 1000)");
        dynamicSqlEntity.addParameter("lng", Double.valueOf(demand.getCenterPointLongitude()));
        dynamicSqlEntity.addParameter("lat", Double.valueOf(demand.getCenterPointLatitude()));
        return dynamicSqlEntity;
    }

    /**
     * 找到白名单中最满足需求的服务商
     *
     * @return
     */
    private DynamicSqlEntity filterWhiteListProviders(DynamicSqlEntity dynamicSqlEntity, FlightDemandDTO demand) {
        DynamicSqlEntity filterWhiteSqlEntity = new DynamicSqlEntity(dynamicSqlEntity);
        //将第一步查询出来的所有满足条件的服务商于白名单进行对比
        List<String> joinOnList = filterWhiteSqlEntity.getJoinOnList();
        joinOnList.add("join white_list white on white.supplier_user_no = station.company_user_no");
        List<String> whereList = filterWhiteSqlEntity.getWhereList();

        // 使用预处理参数
        whereList.add("white.customer_user_no = :customer_user_no");
        filterWhiteSqlEntity.addParameter("customer_user_no", demand.getPublisherNo());

        whereList.add("white.tenant_id = :tenantId and white.is_deleted = :deleted");

        return findBestMatchingProvider(filterWhiteSqlEntity, demand);
    }

    /**
     * 有多个服务商，选择机巢最近的那个
     *
     * @return
     */
    private DynamicSqlEntity findBestMatchingProvider(DynamicSqlEntity dynamicSqlEntity, FlightDemandDTO demand) {
        DynamicSqlEntity findBestMatchingSqlEntity = new DynamicSqlEntity(dynamicSqlEntity);
        // 使用已计算的distance字段排序
        findBestMatchingSqlEntity.setOrderBy("order by ST_distance(station.coordinate::geography, ST_SetSRID(ST_MakePoint(:orderLng, :orderLat), 4326)::geography) asc");
        findBestMatchingSqlEntity.addParameter("orderLng", Double.valueOf(demand.getCenterPointLongitude()));
        findBestMatchingSqlEntity.addParameter("orderLat", Double.valueOf(demand.getCenterPointLatitude()));
        findBestMatchingSqlEntity.setLimit("limit 1");
        findBestMatchingSqlEntity.setSelect("select uav.supplier_user_no");
        return findBestMatchingSqlEntity;
    }

}
