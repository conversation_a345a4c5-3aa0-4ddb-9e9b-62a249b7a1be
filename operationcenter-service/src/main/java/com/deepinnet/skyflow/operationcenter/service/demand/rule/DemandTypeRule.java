package com.deepinnet.skyflow.operationcenter.service.demand.rule;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.helper.DemandMergeRuleHelper;
import com.deepinnet.skyflow.operationcenter.service.helper.DemandTypeDistanceConfigItem;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Component
public class DemandTypeRule extends AbstractDemandMergeRule {

    @Resource
    private DemandMergeRuleHelper demandMergeRuleHelper;

    @Override
    protected DemandMergeRuleResult execute(RuleContext<DemandMergeRuleResult> context) {
        List<DemandTypeDistanceConfigItem> demandTypeAndDistanceConfigItem = demandMergeRuleHelper.getDemandTypeAndDistanceConfigItem(context.getTenantId());
        // 获取合并规则设置的允许合并的类型
        List<FlightDemandTypeEnum> allowedDemandTypes = demandMergeRuleHelper.getAllowDemandTypeList(demandTypeAndDistanceConfigItem).stream().map(FlightDemandTypeEnum::valueOf).collect(Collectors.toList());
        DemandMergeRuleResult previousRuleExecResult = context.getPreviousRuleExecResult();
        List<List<FlightDemandDTO>> fromFilterDemandGroup = previousRuleExecResult.getFilterDemandGroup();

        List<List<FlightDemandDTO>> toFilterDemandGroup = new ArrayList<>();
        
        // 遍历每个需求分组
        for (List<FlightDemandDTO> demandGroup : fromFilterDemandGroup) {
            if (CollectionUtils.isEmpty(demandGroup)) {
                continue;
            }
            
            // 按需求类型分组
            Map<FlightDemandTypeEnum, List<FlightDemandDTO>> typeGroupMap = demandGroup.stream()
                .collect(Collectors.groupingBy(FlightDemandDTO::getType));
            
            // 只保留允许合并的类型，并且同一类型的需求才能合并
            for (Map.Entry<FlightDemandTypeEnum, List<FlightDemandDTO>> entry : typeGroupMap.entrySet()) {
                FlightDemandTypeEnum demandType = entry.getKey();
                List<FlightDemandDTO> sameTypeDemands = entry.getValue();
                
                // 检查该类型是否在允许合并的类型列表中
                if (allowedDemandTypes.contains(demandType)) {
                    // 同一类型的需求可以合并，但需要至少有2个需求才有意义
                    if (sameTypeDemands.size() >= 2) {
                        toFilterDemandGroup.add(sameTypeDemands);
                    }
                }
                // 如果类型不在允许列表中，该分组被过滤掉，不会进入下一个规则
            }
        }

        DemandMergeRuleResult ruleResult = new DemandMergeRuleResult();
        ruleResult.setFilterDemandGroup(toFilterDemandGroup);
        return ruleResult;
    }

    @Override
    public int order() {
        return 100;
    }

}
