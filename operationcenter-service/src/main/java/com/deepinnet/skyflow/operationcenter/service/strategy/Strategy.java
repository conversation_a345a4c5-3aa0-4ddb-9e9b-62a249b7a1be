package com.deepinnet.skyflow.operationcenter.service.strategy;

/**
 * 通用策略接口
 * 
 * <AUTHOR>
 */
public interface Strategy {
    
    /**
     * 是否支持该策略类型和需求类型
     * 
     * @param strategyType 策略类型
     * @param bizEnum 业务类型
     * @return 是否支持
     */
    boolean supports(StrategyTypeEnum strategyType, Enum<?> bizEnum);
    
    /**
     * 执行策略
     * 
     * @param context 执行上下文
     * @return 执行结果
     */
    Object execute(StrategyContext context);
} 