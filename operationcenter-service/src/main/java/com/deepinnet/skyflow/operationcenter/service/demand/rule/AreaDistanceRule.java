package com.deepinnet.skyflow.operationcenter.service.demand.rule;

import cn.hutool.core.collection.CollectionUtil;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandAreaDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.service.helper.DemandMergeRuleHelper;
import com.deepinnet.skyflow.operationcenter.service.helper.DemandTypeDistanceConfigItem;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleContext;
import com.deepinnet.skyflow.operationcenter.service.util.GraphPartitioner;
import com.deepinnet.skyflow.operationcenter.util.GISUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Coordinate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description:
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Component
public class AreaDistanceRule extends AbstractDemandMergeRule {

    @Resource
    private DemandMergeRuleHelper demandMergeRuleHelper;

    @Override
    protected DemandMergeRuleResult execute(RuleContext<DemandMergeRuleResult> context) {
        List<DemandTypeDistanceConfigItem> demandTypeAndDistanceConfigItem = demandMergeRuleHelper.getDemandTypeAndDistanceConfigItem(context.getTenantId());

        DemandMergeRuleResult previousRuleExecResult = context.getPreviousRuleExecResult();
        List<List<FlightDemandDTO>> fromFilterDemandGroup = previousRuleExecResult.getFilterDemandGroup();
        
        List<List<FlightDemandDTO>> toFilterDemandGroup = new ArrayList<>();
        
        for (List<FlightDemandDTO> demandGroup : fromFilterDemandGroup) {
            if (CollectionUtils.isEmpty(demandGroup) || demandGroup.size() < 2) {
                // 单个需求或空分组不需要距离规则处理
                continue;
            }
            
            // 收集所有区域信息，记录每个区域属于哪个需求
            List<AreaWithDemand> allAreas = collectAllAreas(demandGroup);
            
            if (allAreas.size() < 2) {
                // 区域数量少于2个，无法进行分组
                continue;
            }
            
            // 计算每个区域之间的距离矩阵
            double[][] distances = calculateAreaDistanceMatrix(allAreas);
            
            // 使用图分割算法进行分组
            Double areaDistanceRuleKm = demandMergeRuleHelper.getDemandTypeDistanceLimit(demandTypeAndDistanceConfigItem, demandGroup.get(0).getType().getType());
            double areaDistanceRuleMeters = areaDistanceRuleKm * 1000; // 转换为米
            GraphPartitioner graphPartitioner = new GraphPartitioner(distances, areaDistanceRuleMeters);
            List<List<Integer>> areaPartitions = graphPartitioner.partitionIntoCliques();

            // 分析每个分区，找出满足条件的需求组合
            List<List<FlightDemandDTO>> validDemandGroups = processAreaPartitions(areaPartitions, allAreas, demandGroup);
            toFilterDemandGroup.addAll(validDemandGroups);
        }

        DemandMergeRuleResult ruleResult = new DemandMergeRuleResult();
        ruleResult.setFilterDemandGroup(toFilterDemandGroup);
        return ruleResult;
    }

    @Override
    public int order() {
        return 400;
    }
    
    /**
     * 收集所有需求的所有区域，并记录每个区域属于哪个需求
     */
    private List<AreaWithDemand> collectAllAreas(List<FlightDemandDTO> demandGroup) {
        List<AreaWithDemand> allAreas = new ArrayList<>();
        
        for (int i = 0; i < demandGroup.size(); i++) {
            FlightDemandDTO demand = demandGroup.get(i);
            if (CollectionUtil.isNotEmpty(demand.getAreaList())) {
                for (FlightDemandAreaDTO area : demand.getAreaList()) {
                    allAreas.add(new AreaWithDemand(area, demand, i));
                }
            }
        }
        
        return allAreas;
    }
    
    /**
     * 计算区域列表中每两个区域中心点之间的距离矩阵
     * @param areaList 区域列表
     * @return 距离矩阵（单位：米）
     */
    private double[][] calculateAreaDistanceMatrix(List<AreaWithDemand> areaList) {
        int size = areaList.size();
        double[][] distances = new double[size][size];
        
        for (int i = 0; i < size; i++) {
            AreaWithDemand area1 = areaList.get(i);
            Coordinate coord1 = getAreaCenterCoordinate(area1.getArea());
            
            for (int j = 0; j < size; j++) {
                if (i == j) {
                    distances[i][j] = 0; // 自己到自己的距离为0
                } else {
                    AreaWithDemand area2 = areaList.get(j);
                    Coordinate coord2 = getAreaCenterCoordinate(area2.getArea());
                    
                    if (coord1 != null && coord2 != null) {
                        distances[i][j] = GISUtil.calculateDistanceInMeters(coord1, coord2);
                    } else {
                        // 如果坐标为空，设置为最大值，表示无法合并
                        distances[i][j] = Double.MAX_VALUE;
                    }
                }
            }
        }
        
        return distances;
    }
    
    /**
     * 处理区域分区，找出满足条件的需求组合
     */
    private List<List<FlightDemandDTO>> processAreaPartitions(List<List<Integer>> areaPartitions, 
                                                              List<AreaWithDemand> allAreas, 
                                                              List<FlightDemandDTO> originalDemandGroup) {
        List<List<FlightDemandDTO>> validDemandGroups = new ArrayList<>();
        
        for (List<Integer> areaPartition : areaPartitions) {
            if (areaPartition.size() < 2) {
                // 分区中区域数量少于2个，跳过
                continue;
            }
            
            // 统计每个需求在当前分区中有多少个区域
            Map<String, Integer> demandAreaCountInPartition = new HashMap<>();
            Map<String, FlightDemandDTO> demandMap = new HashMap<>();
            
            for (Integer areaIndex : areaPartition) {
                AreaWithDemand areaWithDemand = allAreas.get(areaIndex);
                String demandNo = areaWithDemand.getDemand().getDemandNo();
                demandAreaCountInPartition.put(demandNo, 
                    demandAreaCountInPartition.getOrDefault(demandNo, 0) + 1);
                demandMap.put(demandNo, areaWithDemand.getDemand());
            }
            
            // 找出在当前分区中包含全部区域的需求
            List<FlightDemandDTO> qualifiedDemands = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : demandAreaCountInPartition.entrySet()) {
                String demandNo = entry.getKey();
                int areaCountInPartition = entry.getValue();
                FlightDemandDTO demand = demandMap.get(demandNo);
                
                // 检查该需求的所有区域是否都在当前分区中
                if (demand.getAreaList() != null && areaCountInPartition == demand.getAreaList().size()) {
                    qualifiedDemands.add(demand);
                }
            }
            
            // 如果有2个或以上需求的所有区域都在这个分区中，则保留这些需求
            if (qualifiedDemands.size() >= 2) {
                validDemandGroups.add(qualifiedDemands);
            }
            // 如果只有1个需求满足条件，则过滤掉（不添加到结果中）
        }
        
        return validDemandGroups;
    }
    
    /**
     * 获取区域的中心点坐标
     * @param area 区域对象
     * @return 中心点坐标，如果坐标无效则返回null
     */
    private Coordinate getAreaCenterCoordinate(FlightDemandAreaDTO area) {
        try {
            if (area.getCenterPointLongitude() != null && area.getCenterPointLatitude() != null) {
                double longitude = Double.parseDouble(area.getCenterPointLongitude());
                double latitude = Double.parseDouble(area.getCenterPointLatitude());
                return new Coordinate(longitude, latitude);
            }
        } catch (NumberFormatException e) {
            // 坐标格式错误，返回null
        }
        return null;
    }

    /**
     * 区域信息包装类，记录区域及其所属需求
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class AreaWithDemand {

        private FlightDemandAreaDTO area;

        private FlightDemandDTO demand;

        // 需求在原始列表中的索引
        private int demandIndex;
    }

}
