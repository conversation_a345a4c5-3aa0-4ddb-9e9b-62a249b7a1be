package com.deepinnet.skyflow.operationcenter.service.log.example;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightOrderCreateDTO;
import com.deepinnet.skyflow.operationcenter.enums.OperationLogTemplateEnum;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogContext;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogContextHolder;
import com.deepinnet.skyflow.operationcenter.service.log.annotation.OperationLog;
import org.springframework.stereotype.Service;

/**
 * 操作日志使用示例
 * 展示如何在业务代码中使用操作日志功能
 *
 * <AUTHOR>
 */
@Service
public class OperationLogExample {

    /**
     * 示例1：使用注解指定模板（适用于简单场景）
     */
    @OperationLog(template = OperationLogTemplateEnum.LOGIN_SUCCESS, useTemplate = true)
    public void loginExample() {
        // 业务逻辑
        System.out.println("用户登录成功");
    }

    /**
     * 示例2：在业务方法中设置上下文（推荐方式）
     */
    @OperationLog
    public String createFlightDemandExample(FlightDemandDTO demandDTO) {
        // 设置操作日志上下文
        OperationLogContextHolder.setContext(
            OperationLogContext.demandPublish(demandDTO.getName())
        );

        // 业务逻辑
        String demandNo = "DEMAND_" + System.currentTimeMillis();
        
        // 如果需要更新上下文信息
        OperationLogContext context = OperationLogContextHolder.getContext();
        if (context != null) {
            context.setVariable("demandNo", demandNo);
        }

        return demandNo;
    }

    /**
     * 示例3：复杂场景 - 成员管理
     */
    @OperationLog
    public void addMemberExample(String organizationName, String memberName) {
        // 设置操作日志上下文
        OperationLogContextHolder.setContext(
            OperationLogContext.memberAdd(organizationName, memberName)
        );

        try {
            // 业务逻辑
            System.out.println("添加成员: " + memberName + " 到组织: " + organizationName);
            
            // 模拟业务异常
            if ("error".equals(memberName)) {
                throw new RuntimeException("添加成员失败");
            }
            
        } catch (Exception e) {
            // 异常会被切面自动捕获并记录到操作日志中
            throw e;
        }
    }

    /**
     * 示例4：动态设置模板和变量
     */
    @OperationLog
    public void dynamicTemplateExample(String action, String resourceName) {
        OperationLogTemplateEnum template;
        
        // 根据业务逻辑动态选择模板
        switch (action) {
            case "publish":
                template = OperationLogTemplateEnum.DEMAND_PUBLISH;
                break;
            case "edit":
                template = OperationLogTemplateEnum.DEMAND_EDIT;
                break;
            case "delete":
                template = OperationLogTemplateEnum.DEMAND_DELETE;
                break;
            default:
                // 如果不需要记录日志，可以禁用
                OperationLogContextHolder.setContext(new OperationLogContext().disable());
                return;
        }

        // 设置上下文
        OperationLogContext context = new OperationLogContext(template)
                .setVariable("demandName", resourceName);
        
        OperationLogContextHolder.setContext(context);

        // 业务逻辑
        System.out.println("执行操作: " + action + " 资源: " + resourceName);
    }

    /**
     * 示例5：账号管理操作
     */
    @OperationLog
    public void accountManagementExample(String memberName, String accountNo, String operation) {
        OperationLogContext context;
        
        switch (operation) {
            case "add":
                context = OperationLogContext.accountAdd(memberName, accountNo);
                break;
            case "delete":
                context = new OperationLogContext(OperationLogTemplateEnum.ACCOUNT_DELETE)
                        .setVariable("memberName", memberName)
                        .setVariable("accountNo", accountNo);
                break;
            case "freeze":
                context = new OperationLogContext(OperationLogTemplateEnum.ACCOUNT_FREEZE)
                        .setVariable("memberName", memberName)
                        .setVariable("accountNo", accountNo);
                break;
            case "edit":
                context = new OperationLogContext(OperationLogTemplateEnum.ACCOUNT_EDIT)
                        .setVariable("memberName", memberName)
                        .setVariable("accountNo", accountNo);
                break;
            default:
                return;
        }

        OperationLogContextHolder.setContext(context);

        // 业务逻辑
        System.out.println("账号管理操作: " + operation);
    }

    /**
     * 示例6：飞行规划操作
     */
    @OperationLog
    public void flightPlanExample(String planName, String action) {
        OperationLogTemplateEnum template;
        
        switch (action) {
            case "publish":
                template = OperationLogTemplateEnum.FLIGHT_PLAN_PUBLISH;
                break;
            case "edit":
                template = OperationLogTemplateEnum.FLIGHT_PLAN_EDIT;
                break;
            case "delete":
                template = OperationLogTemplateEnum.FLIGHT_PLAN_DELETE;
                break;
            case "approve":
                template = OperationLogTemplateEnum.FLIGHT_PLAN_APPROVE;
                break;
            case "reject":
                template = OperationLogTemplateEnum.FLIGHT_PLAN_REJECT;
                break;
            default:
                return;
        }

        OperationLogContext context = new OperationLogContext(template)
                .setVariable("planName", planName);
        
        OperationLogContextHolder.setContext(context);

        // 业务逻辑
        System.out.println("飞行规划操作: " + action + " 计划: " + planName);
    }

    /**
     * 示例7：条件性记录日志
     */
    @OperationLog
    public void conditionalLoggingExample(boolean shouldLog, String organizationName) {
        if (shouldLog) {
            OperationLogContextHolder.setContext(
                new OperationLogContext(OperationLogTemplateEnum.ORGANIZATION_ADD)
                    .setVariable("organizationName", organizationName)
            );
        } else {
            // 禁用日志记录
            OperationLogContextHolder.setContext(new OperationLogContext().disable());
        }

        // 业务逻辑
        System.out.println("组织操作");
    }
}
