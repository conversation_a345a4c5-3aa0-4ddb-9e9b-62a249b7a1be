package com.deepinnet.skyflow.operationcenter.service.client;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.client.camera.SurveillanceCameraFeignClient;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCamera;
import com.deepinnet.spatiotemporalplatform.model.camera.SurveillanceCameraCondition;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Creator zengjuerui
 * Date 2025-05-27
 **/

@Service
public class SurveillanceCameraClient {

    @Resource
    private SurveillanceCameraFeignClient surveillanceCameraFeignClient;

    public List<SurveillanceCamera> queryList(SurveillanceCameraCondition condition) {
        Result<List<SurveillanceCamera>> result = surveillanceCameraFeignClient.queryAreaNearCamera(condition);

        if (!result.isSuccess()) {
            LogUtil.error("SurveillanceCameraClient.queryList: condition = {}", condition);
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }

    public SurveillanceCamera getByCodeAndType(String code, String type) {
        Result<SurveillanceCamera> result = surveillanceCameraFeignClient.getByCodeAndType(code, type);

        if (!result.isSuccess()) {
            LogUtil.error("SurveillanceCameraClient.getByCodeAndType: code = {}, type = {}", code, type);
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }
}
