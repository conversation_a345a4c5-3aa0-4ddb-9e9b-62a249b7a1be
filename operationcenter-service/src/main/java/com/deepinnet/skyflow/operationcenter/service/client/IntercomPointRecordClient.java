package com.deepinnet.skyflow.operationcenter.service.client;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.spatiotemporalplatform.client.intercom.IntercomClient;
import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomGeomWkt;
import com.deepinnet.spatiotemporalplatform.model.intercom.IntercomPointRecord;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Creator zengjuerui
 * Date 2025-05-27
 **/

@Service
public class IntercomPointRecordClient {

    @Resource
    private IntercomClient intercomClient;

    public List<IntercomPointRecord> list(IntercomGeomWkt intercomGeomWkt) {
        Result<List<IntercomPointRecord>> result = intercomClient.queryPolygonContainsWithIn5Min(intercomGeomWkt);
        if (!result.isSuccess()) {
            LogUtil.error("IntercomPointRecordClient.list: intercomGeomWkt = {}", intercomGeomWkt);
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }
}
