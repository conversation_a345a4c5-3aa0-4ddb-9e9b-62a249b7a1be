package com.deepinnet.skyflow.operationcenter.service.approval.instance;

import com.deepinnet.skyflow.operationcenter.service.approval.ApprovalCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description:
 * Date: 2025/7/11
 * Author: lijunheng
 */
@Component
public class CallbackRegistry {

    private final Map<String, ApprovalCallback> registry = new HashMap<>();

    public CallbackRegistry(@Autowired List<ApprovalCallback> callbacks) {
        callbacks.forEach(cb -> {
            registry.put(cb.supportBizType(), cb);
        });
    }

    public ApprovalCallback get(String bizType) {
        return registry.get(bizType);
    }
}
