package com.deepinnet.skyflow.operationcenter.service.flow.manager;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.dto.UserInfoDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightOrderCreateDTO;
import com.deepinnet.skyflow.operationcenter.dto.OrderApproveDTO;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.skyflow.operationcenter.service.flow.constants.LiteFlowConstant;
import com.deepinnet.skyflow.operationcenter.service.flow.context.FlightOrderCreateContext;
import com.deepinnet.skyflow.operationcenter.service.flow.context.OrderApproveContext;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@Service
public class LiteFlowManager {

    @Resource
    private FlowExecutor flowExecutor;

    @Resource
    private UserRemoteClient userRemoteClient;

    /**
     * 创建订单流程
     * 用户校验->下单参数校验/参数补充完整->创建订单
     * @param dto 订单dto
     * @return 是否创建成功
     */
    public String createOrder(FlightOrderCreateDTO dto) {

        UserInfoDTO userInfo = userRemoteClient.getUserInfo();

        FlightOrderCreateContext context = FlightOrderCreateContext.builder()
                .flightOrderCreateDTO(dto)
                .userInfo(userInfo).build();

        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp(LiteFlowConstant.CREATE_ORDER, null, context);

        checkResponseResult(liteflowResponse);

        return dto.getOrderNo();
    }

    public Boolean approveOrder(OrderApproveDTO orderApproveDTO) {
        OrderApproveContext context = OrderApproveContext.builder()
                .orderNo(orderApproveDTO.getOrderNo())
                .orderType(orderApproveDTO.getOrderType())
                .approveStatus(orderApproveDTO.getApproveStatus())
                .remark(orderApproveDTO.getRemark())
                .approvalUserNo(UserUtil.getUserNo())
                .approvalUserName(UserUtil.getUserName())
                .tenantId(orderApproveDTO.getTenantId()).build();

        UserInfoDTO userInfo = userRemoteClient.getUserInfo();

        context.setUserInfo(userInfo);

        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp(LiteFlowConstant.APPROVE_ORDER, null, context);

        checkResponseResult(liteflowResponse);
        return true;
    }

    private void checkResponseResult(LiteflowResponse response) {
        if (!response.isSuccess()) {
            Exception cause = response.getCause();
            LogUtil.error("异常堆栈信息：", cause);
            if (cause instanceof BizException) {
                throw (BizException) cause;
            }
        }
    }
}
