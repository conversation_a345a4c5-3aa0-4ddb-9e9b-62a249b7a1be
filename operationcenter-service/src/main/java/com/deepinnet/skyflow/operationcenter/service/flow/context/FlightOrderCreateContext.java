package com.deepinnet.skyflow.operationcenter.service.flow.context;

import com.deepinnet.infra.api.dto.UserInfoDTO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderDO;
import com.deepinnet.skyflow.operationcenter.dto.FlightOrderCreateDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/19
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightOrderCreateContext {

    private FlightOrderCreateDTO flightOrderCreateDTO;

    private UserInfoDTO userInfo;

    private FlightOrderDO existOrder;

}
