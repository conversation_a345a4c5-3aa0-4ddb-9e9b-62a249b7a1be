package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 都是针对审批发起人的选择审核人
 * Date: 2025/7/21
 * Author: lijunheng
 */
@AllArgsConstructor
@Getter
public enum ApproveChooseStrategyEnum {

    CONTINUOUS_MULTI_LEVEL_SUPERVISOR("CONTINUOUS_MULTI_LEVEL_SUPERVISOR", "连续多级主管", null),
    DESIGNATED_APPROVES("DESIGNATED_APPROVES", "指定审批人", null),

    ;

    private String type;
    private String desc;
    private Class clazz;
}
