package com.deepinnet.skyflow.operationcenter.service.flow.order;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.dto.UserDepartmentDTO;
import com.deepinnet.infra.api.dto.UserInfoDTO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderApprovalDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderApprovalRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderRepository;
import com.deepinnet.skyflow.operationcenter.enums.FlightOrderApproveStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.flow.context.OrderApproveContext;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.yomahub.liteflow.annotation.LiteflowCmpDefine;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@LiteflowComponent(value = "flightOrderApproveNode", name = "撮合订单审核节点")
@LiteflowCmpDefine
public class FlightOrderApproveNode {

    @Resource
    private FlightOrderRepository flightOrderRepository;

    @Resource
    private FlightOrderApprovalRepository flightOrderApprovalRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    @LiteflowMethod(LiteFlowMethodEnum.PROCESS)
    public void process(NodeComponent bindCmp) {
        OrderApproveContext contextBean = bindCmp.getContextBean(OrderApproveContext.class);

        FlightOrderVO flightOrder = contextBean.getFlightOrder();

        FlightOrderApproveStatusEnum approveStatusEnum = FlightOrderApproveStatusEnum.getEnumByStatusName(flightOrder.getApproveStatus());

        if (ObjectUtil.isNull(approveStatusEnum)) {
            LogUtil.error("订单:{}, 审核状态:{}, 不存在", flightOrder.getOrderNo(), flightOrder.getApproveStatus());
            throw new BizException(BizErrorCode.ORDER_APPROVE_STATUS_NOT_EXIST.getCode(), BizErrorCode.ORDER_APPROVE_STATUS_NOT_EXIST.getDesc());
        }

        if (StrUtil.equals(flightOrder.getApproveStatus(), FlightOrderApproveStatusEnum.APPROVED.getStatusCode())) {
            LogUtil.error("订单:{}, 审核状态:{}, 已审核", flightOrder.getOrderNo(), flightOrder.getApproveStatus());
            throw new BizException(BizErrorCode.ORDER_APPROVE_STATUS_ALREADY_APPROVED.getCode(), BizErrorCode.ORDER_APPROVE_STATUS_ALREADY_APPROVED.getDesc());
        }

        if (StrUtil.equals(flightOrder.getApproveStatus(), FlightOrderApproveStatusEnum.REJECTED.getStatusCode())) {
            LogUtil.error("订单:{}, 审核状态:{}, 已拒绝", flightOrder.getOrderNo(), flightOrder.getApproveStatus());
            throw new BizException(BizErrorCode.ORDER_APPROVE_STATUS_ALREADY_REFUSED.getCode(), BizErrorCode.ORDER_APPROVE_STATUS_ALREADY_REFUSED.getDesc());
        }

        try{

            transactionTemplate.executeWithoutResult(e -> {
                flightOrderRepository.update(Wrappers.<FlightOrderDO>lambdaUpdate()
                        .eq(FlightOrderDO::getOrderNo, flightOrder.getOrderNo())
                        .set(FlightOrderDO::getApproveStatus, contextBean.getApproveStatus())
                        .set(FlightOrderDO::getStatus, StrUtil.equals(contextBean.getApproveStatus(), FlightOrderApproveStatusEnum.APPROVED.getStatusCode())
                                ? OrderStatusEnum.IN_PROGRESS.getCode() : OrderStatusEnum.CLOSED.getCode())
                        .set(FlightOrderDO::getGmtModified, LocalDateTime.now()));

                flightOrderApprovalRepository.save(buildFlightApprovalDO(contextBean));
            });
        } catch (Exception e) {
            LogUtil.error("订单:{}, 审核状态:{}, 审核失败, 堆栈信息:{}", flightOrder.getOrderNo(), contextBean.getApproveStatus(), e);
            throw new BizException(BizErrorCode.ORDER_APPROVAL_ERROR.getCode(), BizErrorCode.ORDER_APPROVAL_ERROR.getCode());
        }

    }

    private FlightOrderApprovalDO buildFlightApprovalDO(OrderApproveContext contextBean) {
        UserInfoDTO userInfo = contextBean.getUserInfo();
        UserDepartmentDTO userDepartment = userInfo.getUserDepartment();
        FlightOrderApprovalDO flightOrderApprovalDO = new FlightOrderApprovalDO();
        flightOrderApprovalDO.setOrderNo(contextBean.getOrderNo());
        flightOrderApprovalDO.setApprovalStatus(contextBean.getApproveStatus());
        flightOrderApprovalDO.setApprovalUserNo(contextBean.getApprovalUserNo());
        flightOrderApprovalDO.setApprovalUserName(contextBean.getApprovalUserName());
        flightOrderApprovalDO.setApprovalTime(DateUtil.current());
        flightOrderApprovalDO.setRemark(contextBean.getRemark());
        flightOrderApprovalDO.setRole(userInfo.getRoles().get(0).getRoleName());
        flightOrderApprovalDO.setPhone(userInfo.getPhone());
        flightOrderApprovalDO.setOrganizationId(String.valueOf(userDepartment.getDepartmentId()));
        flightOrderApprovalDO.setOrganizationName(userDepartment.getDepartmentName());
        flightOrderApprovalDO.setTenantId(contextBean.getTenantId());
        return flightOrderApprovalDO;
    }
}
