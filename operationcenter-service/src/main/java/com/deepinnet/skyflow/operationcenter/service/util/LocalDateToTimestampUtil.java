package com.deepinnet.skyflow.operationcenter.service.util;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
public class LocalDateToTimestampUtil {

    private static final ZoneId SHANGHAI_ZONE = ZoneId.of("Asia/Shanghai");

    /**
     * 将 LocalDate 转为当天 23:59:59 的毫秒级时间戳
     *
     * @param date   目标日期
     * @return       毫秒级时间戳
     */
    public static long toEndOfDayTimestamp(LocalDate date) {
        return date
                .atTime(LocalTime.of(23, 59, 59))
                .atZone(SHANGHAI_ZONE)
                .toInstant()
                .toEpochMilli();
    }
}
