package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: 审批人选择配置规则
 * Date: 2025/7/21
 * Author: lijunheng
 */
@Data
public class ApprovalChooseConfigRule implements Serializable {

    /**
     * 审批人选择策略
     */
    private ApproveChooseStrategyEnum chooseStrategy;

    /**
     * 审批人选择策略的配置json
     */
    private String approveChooseConfigJson;

    /**
     * 审批方式
     */
    private ApproveModeEnum approveMode = ApproveModeEnum.ALL;

    /**
     * 没找到审批人时的处理策略
     */
    private EmptyApproverStrategyEnum ifEmptyStrategy = EmptyApproverStrategyEnum.AUTO_PASS;

    /**
     * 没找到审批人时的处理策略的配置json
     */
    private String emptyApproveConfigJson;

    /**
     * 审批人超时配置
     */
    private ApproveTimeoutConfig approveTimeoutConfig;
}
