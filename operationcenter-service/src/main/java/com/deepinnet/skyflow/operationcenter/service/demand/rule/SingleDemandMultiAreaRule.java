package com.deepinnet.skyflow.operationcenter.service.demand.rule;

import cn.hutool.core.collection.ListUtil;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandAreaDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.service.helper.DemandMergeRuleHelper;
import com.deepinnet.skyflow.operationcenter.service.helper.DemandTypeDistanceConfigItem;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleContext;
import com.deepinnet.skyflow.operationcenter.util.GISUtil;
import org.locationtech.jts.geom.Coordinate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Component
public class SingleDemandMultiAreaRule extends AbstractDemandMergeRule {

    @Resource
    private DemandMergeRuleHelper demandMergeRuleHelper;

    @Override
    protected DemandMergeRuleResult execute(RuleContext<DemandMergeRuleResult> context) {
        List<DemandTypeDistanceConfigItem> demandTypeAndDistanceConfigItem = demandMergeRuleHelper.getDemandTypeAndDistanceConfigItem(context.getTenantId());
        //原始需求多个区域的情况下，需要两两之间的距离是否都小于预定值，按中心点算
        List<FlightDemandDTO> fromFilterDemandList = (List<FlightDemandDTO>) context.getInputParam();
        List<FlightDemandDTO> toFilterDemandList = fromFilterDemandList.stream().filter(demandDTO -> {
            boolean sizeCond = demandDTO.getAreaList().size() > 1;
            boolean distanceCond = isOverDistance(demandDTO.getAreaList(), demandMergeRuleHelper.getDemandTypeDistanceLimit(demandTypeAndDistanceConfigItem, demandDTO.getType().getType()));
            if (sizeCond && distanceCond) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        DemandMergeRuleResult result = new DemandMergeRuleResult();
        result.setFilterDemandGroup(ListUtil.toList(Collections.singleton(toFilterDemandList)));
        return result;
    }

    @Override
    public int order() {
        return 0;
    }

    private boolean isOverDistance(List<FlightDemandAreaDTO> areaList, double distanceKm) {
        // 将距离限制从公里转换为米
        double distanceMeters = distanceKm * 1000;
        
        // 检查任意两个区域之间的距离
        for (int i = 0; i < areaList.size(); i++) {
            FlightDemandAreaDTO area1 = areaList.get(i);
            if (area1.getCenterPointLongitude() == null || area1.getCenterPointLatitude() == null) {
                continue;
            }
            
            Coordinate coord1 = new Coordinate(
                Double.parseDouble(area1.getCenterPointLongitude()),
                Double.parseDouble(area1.getCenterPointLatitude())
            );
            
            for (int j = i + 1; j < areaList.size(); j++) {
                FlightDemandAreaDTO area2 = areaList.get(j);
                if (area2.getCenterPointLongitude() == null || area2.getCenterPointLatitude() == null) {
                    continue;
                }
                
                Coordinate coord2 = new Coordinate(
                    Double.parseDouble(area2.getCenterPointLongitude()),
                    Double.parseDouble(area2.getCenterPointLatitude())
                );
                
                // 计算两点间的距离（米）
                double distance = GISUtil.calculateDistanceInMeters(coord1, coord2);
                
                // 如果任意两个区域之间的距离超过设定值，返回true
                if (distance > distanceMeters) {
                    return true;
                }
            }
        }
        
        // 所有区域之间的距离都在设定值内
        return false;
    }

}
