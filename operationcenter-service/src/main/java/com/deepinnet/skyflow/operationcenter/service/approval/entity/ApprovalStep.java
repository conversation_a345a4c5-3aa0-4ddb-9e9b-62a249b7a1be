package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description:
 * Date: 2025/7/11
 * Author: lijunheng
 */
@Data
public class ApprovalStep implements Serializable {

    private Long id;

    private String approvalId;

    /**
     * 步骤顺序，从0开始
     */
    private Integer stepOrder;

    /**
     * 审批人列表
     */
    private List<ApprovalNode> approvalNodeList;

    /**
     * 审批状态
     */
    private ApproveStatusEnum status;

    /**
     * 审批方式
     */
    private ApproveModeEnum approveMode = ApproveModeEnum.ALL;
}
