package com.deepinnet.skyflow.operationcenter.service.util;

import lombok.Data;

import java.util.*;

/**
 * Description:
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Data
public class AntColonyTSP {

    private int n;
    private int antCount;
    private int maxIter;
    private double alpha, beta, rho, Q;

    private double[][] dist;
    private double[][] pheromone;
    private Random rand = new Random();

    private int[] bestTour;
    private double bestLength = Double.MAX_VALUE;

    public AntColonyTSP(double[][] dist, int antCount, int maxIter, double alpha, double beta, double rho, double Q) {
        this.n = dist.length;
        this.dist = new double[n][n];
        for (int i = 0; i < n; i++)
            for (int j = 0; j < n; j++)
                this.dist[i][j] = dist[i][j];

        this.antCount = antCount;
        this.maxIter = maxIter;
        this.alpha = alpha;
        this.beta = beta;
        this.rho = rho;
        this.Q = Q;
        this.pheromone = new double[n][n];
        for (double[] row : pheromone) Arrays.fill(row, 1.0); // 初始信息素
    }

    public void run() {
        for (int iter = 0; iter < maxIter; iter++) {
            List<Ant> ants = new ArrayList<>();
            for (int k = 0; k < antCount; k++) {
                Ant ant = new Ant(n);
                int start = rand.nextInt(n);
                ant.tour[0] = start;
                ant.visited[start] = true;

                for (int step = 1; step < n; step++) {
                    int current = ant.tour[step - 1];
                    int next = selectNext(ant, current);
                    ant.tour[step] = next;
                    ant.visited[next] = true;
                }

                ant.tourLength = calcTourLength(ant.tour);
                if (ant.tourLength < bestLength) {
                    bestLength = ant.tourLength;
                    bestTour = Arrays.copyOf(ant.tour, n);
                }
                ants.add(ant);
            }

            // 更新信息素
            evaporate();
            for (Ant ant : ants) {
                deposit(ant);
            }
        }
    }

    private int selectNext(Ant ant, int current) {
        double[] prob = new double[n];
        double sum = 0.0;

        for (int j = 0; j < n; j++) {
            if (!ant.visited[j]) {
                prob[j] = Math.pow(pheromone[current][j], alpha) * Math.pow(1.0 / (dist[current][j] + 1e-10), beta);
                sum += prob[j];
            }
        }

        double r = rand.nextDouble() * sum;
        double total = 0.0;
        for (int j = 0; j < n; j++) {
            if (!ant.visited[j]) {
                total += prob[j];
                if (total >= r) return j;
            }
        }

        // fallback
        for (int j = 0; j < n; j++) if (!ant.visited[j]) return j;
        return -1; // 不会到达
    }

    private double calcTourLength(int[] tour) {
        double length = 0;
        for (int i = 0; i < n - 1; i++) {
            length += dist[tour[i]][tour[i + 1]];
        }
        return length;
    }

    private void evaporate() {
        for (int i = 0; i < n; i++)
            for (int j = 0; j < n; j++)
                pheromone[i][j] *= (1 - rho);
    }

    private void deposit(Ant ant) {
        double delta = Q / ant.tourLength;
        for (int i = 0; i < n - 1; i++) {
            int u = ant.tour[i], v = ant.tour[i + 1];
            pheromone[u][v] += delta;
            pheromone[v][u] += delta;
        }
    }

    public List<Double> getStepDistances() {
        List<Double> stepDistances = new ArrayList<>();
        stepDistances.add(0.0);
        for (int i = 0; i < bestTour.length - 1; i++) {
            int from = bestTour[i];
            int to = bestTour[i + 1];
            stepDistances.add(dist[from][to]);
        }
        return stepDistances;
    }

    public void printResult() {
        System.out.println("最短路径总长度: " + bestLength);
        System.out.println("路径节点顺序: " + Arrays.toString(bestTour));
        System.out.println("每一步的路径长度:");

        for (int i = 0; i < bestTour.length - 1; i++) {
            int from = bestTour[i];
            int to = bestTour[i + 1];
            double d = dist[from][to];
            System.out.printf("  %d -> %d : %.2f\n", from, to, d);
        }
    }


    static class Ant {
        int[] tour;
        boolean[] visited;
        double tourLength;

        Ant(int n) {
            tour = new int[n];
            visited = new boolean[n];
            tourLength = 0;
        }
    }

    public static void main(String[] args) {
        // 示例：30个节点的对称距离矩阵（这里用随机值模拟）
        int n = 30;
        double[][] dist = new double[n][n];
        Random r = new Random();

        for (int i = 0; i < n; i++)
            for (int j = i + 1; j < n; j++) {
                dist[i][j] = dist[j][i] = r.nextInt(100) + 1;
            }

        AntColonyTSP aco = new AntColonyTSP(dist,
                50,     // 蚂蚁数
                200,    // 迭代次数
                1.0,    // alpha：信息素重要性
                5.0,    // beta：距离重要性
                0.5,    // rho：信息素蒸发率
                100.0   // Q：信息素释放常数
        );

        aco.run();
        aco.printResult();
    }
}


