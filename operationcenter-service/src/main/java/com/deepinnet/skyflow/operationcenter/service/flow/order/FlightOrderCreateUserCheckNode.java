package com.deepinnet.skyflow.operationcenter.service.flow.order;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.flow.context.FlightOrderCreateContext;
import com.deepinnet.skyflow.operationcenter.service.user.UserService;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.util.enums.UserTypeEnum;
import com.yomahub.liteflow.annotation.LiteflowCmpDefine;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;

import javax.annotation.Resource;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@LiteflowComponent(value = "userCheckNode", name = "用户校验节点")
@LiteflowCmpDefine
public class FlightOrderCreateUserCheckNode {

    @Resource
    private UserService userService;

    @LiteflowMethod(LiteFlowMethodEnum.PROCESS)
    public void process(NodeComponent bindCmp) {

        FlightOrderCreateContext contextBean = bindCmp.getContextBean(FlightOrderCreateContext.class);

        if (ObjectUtil.notEqual(UserTypeEnum.CUSTOMER, UserUtil.getUserType())) {
            LogUtil.error("下单功能仅用户类型为CUSTOMER的用户可用, 当前用户:{}, 类型为:{}", UserUtil.getUserNo(), UserUtil.getUserType());
            throw new BizException(BizErrorCode.CURRENT_USER_CAN_NOT_CREATE_ORDER.getCode(), BizErrorCode.CURRENT_USER_CAN_NOT_CREATE_ORDER.getDesc());
        }

        try {

            Boolean checkStatus = userService.checkUserInfoStatus();

            if (!checkStatus
                    && StrUtil.equals(OrderTypeEnum.NORMAL.getCode(), contextBean.getFlightOrderCreateDTO().getOrderType())) {
                LogUtil.error("用户信息不完善, 当前用户:{}, 类型为:{}", UserUtil.getUserNo(), UserUtil.getUserType());
                throw new BizException(BizErrorCode.USER_INFO_NOT_COMPLETE.getCode(), BizErrorCode.USER_INFO_NOT_COMPLETE.getDesc());
            }
        } catch (Exception e) {
            LogUtil.error("用户信息校验失败, 当前用户:{}, 类型为:{}, 异常信息:{}", UserUtil.getUserNo(), UserUtil.getUserType(), e);
            throw new BizException(BizErrorCode.USER_CHECK_ERROR.getCode(), BizErrorCode.USER_CHECK_ERROR.getDesc());
        }

    }
}
