package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/18
 */

@Getter
@AllArgsConstructor
public enum ApproveStatusEnum {

    /**
     * 待处理
     */
    PENDING("PENDING", "待处理"),

    /**
     * 审核中
     */
    APPROVING("APPROVING", "审核中"),

    /**
     * 审核通过
     */
    APPROVED("APPROVED", "审核通过"),

    /**
     * 审核不通过
     */
    REJECTED("REJECTED", "审核不通过"),

    /**
     * 撤销
     */
    CANCELED("CANCELED", "撤销"),


    ;


    private final String statusCode;

    private final String statusName;


    /**
     * 终态code列表
     */
    public static final List<String> FINAL_STATE_CODES = java.util.Arrays.asList(APPROVED.getStatusCode(), REJECTED.getStatusCode(), CANCELED.getStatusCode());

    public static ApproveStatusEnum getEnumByStatusName(String statusCode) {
        for (ApproveStatusEnum statusEnum : ApproveStatusEnum.values()) {
            if (statusEnum.getStatusCode().equals(statusCode)) {
                return statusEnum;
            }
        }
        return null;
    }
}
