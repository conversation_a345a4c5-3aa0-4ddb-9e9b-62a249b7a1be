package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Description: 审批超时配置
 * Date: 2025/8/8
 * Author: lijunheng
 */
@Data
public class ApproveTimeoutConfig {

    /**
     * 超时时间
     */
    private Long timeout;

    /**
     * 超时单位
     */
    private TimeUnit timeUnit;

    /**
     * 具体超时触发时间点
     */
    private LocalDateTime triggerTime;

    /**
     * 超时处理类型
     */
    private TimeoutActionTypeEnum type = TimeoutActionTypeEnum.AUTO_REJECT;

    /**
     * 动作参数（灵活扩展）
     */
    private Map<String, Object> params;
}
