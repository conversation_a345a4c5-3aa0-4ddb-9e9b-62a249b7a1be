package com.deepinnet.skyflow.operationcenter.service.demand.impl.matching;

import cn.hutool.core.map.MapUtil;
import com.deepinnet.infra.api.enums.FlightServiceTypeEnum;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Description:
 * Date: 2025/4/19
 * Author: lijunheng
 */
@Component
public class PilotProviderMatchingRule {

    @Resource
    private DynamicSqlInvokeHelper dynamicSqlInvokeHelper;

    /**
     * 获取最匹配的服务商
     *
     * @param demand
     * @param flightUavBmList
     * @return
     */
    public String bestMatchProvider(FlightDemandDTO demand, List<String> flightUavBmList) {
        //判断是否有符合条件的服务商
        DynamicSqlEntity queryAllMatchSqlEntity = queryAllMatchingProviders(demand, flightUavBmList);
        boolean exist = dynamicSqlInvokeHelper.exist(queryAllMatchSqlEntity);
        if (!exist) {
            return null;
        }

        DynamicSqlEntity filterWhiteSql = filterWhiteListProviders(queryAllMatchSqlEntity, demand);
        DynamicSqlEntity bestMatchProviderSql = findDemandLeastProvider(filterWhiteSql);
        Map bestMatchProvider = dynamicSqlInvokeHelper.queryForObject(bestMatchProviderSql, Map.class);
        if (MapUtil.isEmpty(bestMatchProvider)) {
            //直接找非白名单内的
            DynamicSqlEntity demandLeastProvider = findDemandLeastProvider(queryAllMatchSqlEntity);
            bestMatchProvider = dynamicSqlInvokeHelper.queryForObject(demandLeastProvider, Map.class);
        }
        return (String) bestMatchProvider.get("serviceProviderNo");
    }

    /**
     * 获取匹配的所有服务商
     *
     * @param demand
     * @param flightUavBmList
     * @return
     */
    public List<String> matchProviderList(FlightDemandDTO demand, List<String> flightUavBmList) {
        //判断是否有符合条件的服务商
        DynamicSqlEntity queryAllMatchSqlEntity = queryAllMatchingProviders(demand, flightUavBmList);
        return dynamicSqlInvokeHelper.queryForList(queryAllMatchSqlEntity, String.class);
    }

    /**
     * 判断是否有符合条件的服务商（同时满足以下条件）
     * <p>
     * 一、服务商提供飞手服务（后台管理中配置）
     * 二、服务商能提供订单要求的机型
     * <p>
     * 1. 如果是**机型服务**，则服务商能提供"机型服务所用机型"飞行器
     * 2. 如果是**场景服务**，则服务商能提供"场景服务所规定机型（任意一种即可）"飞行器
     *
     * @param demand 飞行需求
     * @return 是否存在符合的服务商
     */
    private DynamicSqlEntity queryAllMatchingProviders(FlightDemandDTO demand, List<String> flightUavBmList) {
        DynamicSqlEntity dynamicSqlEntity = new DynamicSqlEntity();
        dynamicSqlEntity.setSelect("select distinct uav.supplier_user_no");
        dynamicSqlEntity.setFrom("from flight_uav uav");
        List<String> joinOnList = dynamicSqlEntity.getJoinOnList();
        joinOnList.add("join supplier_flight_service supplier on supplier.user_no = uav.supplier_user_no");
        joinOnList.add("join product_service_supplier pss on pss.user_no = uav.supplier_user_no");

        List<String> whereList = dynamicSqlEntity.getWhereList();
        whereList.add("uav.flight_uav_bm_no in (:flightUavBmList)");
        whereList.add("supplier.service_code = :serviceCode");
        whereList.add("pss.approval_status = 1");
        whereList.add("uav.tenant_id = :tenantId and supplier.tenant_id = :tenantId and pss.tenant_id = :tenantId");
        whereList.add("uav.is_deleted = :deleted and pss.is_deleted = :deleted");

        // 添加参数值
        dynamicSqlEntity.addParameter("flightUavBmList", flightUavBmList);
        dynamicSqlEntity.addParameter("serviceCode", FlightServiceTypeEnum.DRONE_PILOT.getCode());
        dynamicSqlEntity.addParameter("tenantId", demand.getTenantId());
        dynamicSqlEntity.addParameter("deleted", false);

        return dynamicSqlEntity;
    }

    /**
     * 找到白名单中的服务商列表
     *
     * @return 白名单服务商列表
     */
    private DynamicSqlEntity filterWhiteListProviders(DynamicSqlEntity dynamicSqlEntity, FlightDemandDTO demand) {
        //将第一步查询出来的所有满足条件的服务商于白名单进行对比
        DynamicSqlEntity filterWhiteSqlEntity = new DynamicSqlEntity(dynamicSqlEntity);
        List<String> joinOnList = filterWhiteSqlEntity.getJoinOnList();
        joinOnList.add("join white_list white on white.supplier_user_no = uav.supplier_user_no");
        List<String> whereList = filterWhiteSqlEntity.getWhereList();

        // 使用预处理参数
        whereList.add("white.customer_user_no = :publisherNo");
        filterWhiteSqlEntity.addParameter("publisherNo", demand.getPublisherNo());

        whereList.add("white.tenant_id = :tenantId and white.is_deleted = :deleted");

        filterWhiteSqlEntity.setSelect("select distinct white.supplier_user_no");
        return filterWhiteSqlEntity;
    }

    /**
     * 有多个服务商，选择需求数量最少的服务商进行需求匹配
     * 
     * @param dynamicSqlEntity 基础查询实体
     * @return 查询语句
     */
    private DynamicSqlEntity findDemandLeastProvider(DynamicSqlEntity dynamicSqlEntity) {
        //选择需求数量最少的服务商
        DynamicSqlEntity findBestMatchingSqlEntity = new DynamicSqlEntity(dynamicSqlEntity);
        
        // 使用左连接防止没有需求记录的服务商被过滤掉
        List<String> joinOnList = findBestMatchingSqlEntity.getJoinOnList();
        joinOnList.add("left join flight_demand_match_service_provider fdmsp on fdmsp.service_provider_no = uav.supplier_user_no and fdmsp.tenant_id = :tenantId");
        
        findBestMatchingSqlEntity.setSelect("select uav.supplier_user_no as serviceProviderNo, count(distinct fdmsp.id) as num");
        findBestMatchingSqlEntity.setGroupBy("group by uav.supplier_user_no");
        findBestMatchingSqlEntity.setOrderBy("order by num asc");
        findBestMatchingSqlEntity.setLimit("limit 1");
        
        return findBestMatchingSqlEntity;
    }
}
