package com.deepinnet.skyflow.operationcenter.service.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.dto.QueryUserInfoDTO;
import com.deepinnet.infra.api.dto.SimpleUserInfoDTO;
import com.deepinnet.infra.api.dto.UserInfoDTO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandPlanStatDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanDO;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightPlanPageQuery;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandPlanStatRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightPlanRepository;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightPlanStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApproveStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.skyflow.operationcenter.service.util.DateTimeRange;
import com.deepinnet.skyflow.operationcenter.util.enums.UserTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 每天1点统计截止前一天为止的飞行数据
 * Date: 2025/7/19
 * Author: lijunheng
 */
@Slf4j
@Component
public class FlightDemandPlanStatTask {

    @Resource
    private FlightDemandRepository flightDemandRepository;

    @Resource
    private FlightOrderRepository flightOrderRepository;

    @Resource
    private FlightPlanRepository flightPlanRepository;

    @Resource
    private FlightDemandPlanStatRepository flightDemandPlanStatRepository;

    @Resource
    private UserRemoteClient userRemoteClient;

    @Scheduled(cron = "0 0 1 * * ? ")
    public void execute() {

        QueryUserInfoDTO queryUserInfoDTO = new QueryUserInfoDTO();
        queryUserInfoDTO.setUserType(UserTypeEnum.CUSTOMER.getCode());

        List<SimpleUserInfoDTO> customerUsers = userRemoteClient.getAllUserInfoWithoutTenantId(queryUserInfoDTO);

        if (CollUtil.isEmpty(customerUsers)) {
            return;
        }

        LocalDate todayDate = LocalDate.now();
        LocalDate yesterdayDate = todayDate.minusDays(1);
        LocalDateTime todayDateTime = todayDate.atStartOfDay();

        for (SimpleUserInfoDTO userInfo : customerUsers) {

            FlightDemandPlanStatDO flightDemandPlanStatDO = new FlightDemandPlanStatDO();

            flightDemandPlanStatDO.setUserCode(userInfo.getUserNo());
            flightDemandPlanStatDO.setStatDate(yesterdayDate);
            flightDemandPlanStatDO.setTenantId(userInfo.getTenantId());

            try {

                setApprovedOrderNum(userInfo.getUserNo(), todayDateTime, flightDemandPlanStatDO);

                setValidDemandNum(userInfo, todayDateTime, flightDemandPlanStatDO);

                setFlightPlanNum(userInfo.getUserNo(), flightDemandPlanStatDO);

                flightDemandPlanStatRepository.save(flightDemandPlanStatDO);
            } catch (DuplicateKeyException duplicateKeyException) {
                LogUtil.warn("当前用户:{}, 日期:{}, 的统计数据已存在!", userInfo.getUserNo(), yesterdayDate);
                return;
            } catch (Exception e) {
                LogUtil.error("当前用户:{}, 日期:{}, 统计数据:{}, 写入失败!"
                        , userInfo.getUserNo(), yesterdayDate, JSONObject.toJSONString(flightDemandPlanStatDO), e);
                return;
            }

        }
    }

    private void setValidDemandNum(SimpleUserInfoDTO userInfo, LocalDateTime todayDateTime, FlightDemandPlanStatDO flightDemandPlanStatDO) {
        long count = flightDemandRepository.count(Wrappers.<FlightDemandDO>lambdaQuery()
                .eq(FlightDemandDO::getPublisherNo, userInfo.getUserNo())
                .eq(FlightDemandDO::getIsMergeDemand, false)
                .eq(FlightDemandDO::getApproveStatus, ApproveStatusEnum.APPROVED.getStatusCode())
                .eq(FlightDemandDO::getIsLatest, true)
                .lt(FlightDemandDO::getApproveEndTime, todayDateTime));
        flightDemandPlanStatDO.setValidDemandNum(count);
    }

    private void setFlightPlanNum(String userNo, FlightDemandPlanStatDO flightDemandPlanStatDO) {
        // 飞行计划数 总数量, 待飞行数量, 已飞行数量
        List<FlightPlanDO> yesterdayFlightPlanList = flightPlanRepository
                .getYesterdayFlightPlanList(FlightPlanPageQuery.builder().userNo(List.of(userNo)).build());

        flightDemandPlanStatDO.setTotalPlanNum(Convert.toLong(yesterdayFlightPlanList.size()));

        List<FlightPlanDO> needFlyPlanList = yesterdayFlightPlanList.stream()
                .filter(e ->
                        ObjectUtil.equals(e.getStatus(), FlightPlanStatusEnum.NEED_APPLIED.getCode())
                                || ObjectUtil.equals(e.getStatus(), FlightPlanStatusEnum.APPLIED.getCode()))
                .collect(Collectors.toList());

        flightDemandPlanStatDO.setReadyPlanNum(CollUtil.isEmpty(yesterdayFlightPlanList) ?
                0 : Convert.toLong(needFlyPlanList.size()));


        List<FlightPlanDO> fliedPlanList = yesterdayFlightPlanList.stream()
                .filter(e ->
                        ObjectUtil.equals(e.getStatus(), FlightPlanStatusEnum.EXECUTING.getCode())
                                || ObjectUtil.equals(e.getStatus(), FlightPlanStatusEnum.COMPLETED.getCode()))
                .collect(Collectors.toList());

        flightDemandPlanStatDO.setCompletePlanNum(CollUtil.isEmpty(yesterdayFlightPlanList) ?
                0 : Convert.toLong(fliedPlanList.size()));
    }

    private void setApprovedOrderNum(String userNo, LocalDateTime todayDateTime, FlightDemandPlanStatDO flightDemandPlanStatDO) {
        // 查询当前用户的已审核的规划数
        long approvedCount = flightOrderRepository.count(Wrappers.<FlightOrderDO>lambdaQuery()
                .eq(FlightOrderDO::getUserNo, userNo)
                .eq(FlightOrderDO::getOrderType, OrderTypeEnum.DEMAND_PLAN.getCode())
                .lt(FlightOrderDO::getApprovedTime, todayDateTime)
                .in(FlightOrderDO::getStatus, List.of(OrderStatusEnum.IN_PROGRESS.getCode()
                        , OrderStatusEnum.FINISHED.getCode())));

        flightDemandPlanStatDO.setApprovedOrderNum(approvedCount);
    }

}
