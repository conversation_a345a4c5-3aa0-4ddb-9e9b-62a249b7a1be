package com.deepinnet.skyflow.operationcenter.service.order;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightPositionVO;
import com.deepinnet.spatiotemporalplatform.dto.FlightPlanAchievementQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.FlightAchievementVO;
import com.deepinnet.spatiotemporalplatform.vo.FlightPlanVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 飞行订单服务接口
 *
 * <AUTHOR>
 */
public interface FlightOrderService {

    /**
     * 根据订单编号获取飞行订单
     *
     * @param dto 订单查询对象
     * @return 飞行订单数据
     */
    FlightOrderVO getFlightOrderByNo(OrderQueryDTO dto);

    /**
     * 根据订单编号获取飞行订单
     *
     * @param dto 订单查询对象
     * @return 飞行订单数据
     */
    CommonPage<FlightOrderVO> getFlightOrderListByPage(OrderPageQueryDTO dto);

    /**
     * 订单审核
     * @param dto 审核参数
     * @return 审核结果
     */
    Boolean orderApprove(OrderApproveDTO dto);

    /**
     * 创建订单
     * @param dto 创建参数
     * @return 创建结果
     */
    String createOrder(FlightOrderCreateDTO dto);

    /**
     * 计划列表查询
     * @param dto 查询参数
     * @return 计划列表
     */
    CommonPage<FlightPlanVO> getFlightPlanListByPage(FlightPlanPageQueryDTO dto);

    /**
     * 计划列表查询 该计划列表给交警 / 撮合大屏使用
     * @param dto 查询参数
     * @return 计划列表
     */
    CommonPage<FlightPlanVO> getPlanList(@NotNull(message = "计划查询参数不能为空") FlightPlanPageQueryDTO dto);

    /**
     * 计划详情查询
     * @param planId 计划编号
     * @return 计划详情
     */
    FlightPlanVO getFlightPlanDetail(String planId);

    /**
     * 变更订单状态
     * @param bizNo 订单编号
     * @param code 状态码
     * @param tenantId 租户ID
     */
    void updateOrderStatus(String bizNo, String code, String tenantId);

    /**
     * 订单用量扣减
     * @param dto 设置参数
     * @return 扣减是否成功
     */
    Boolean orderProductUsage(FlightOrderProductUsageDTO dto);

    /**
     * 需求计划单删除
     * @param dto 订单详情查询dto
     */
    Boolean deleteOrder(OrderQueryDTO dto);

    /**
     * 根据订单编号查询计划列表
     * 接口已废弃, 请勿使用
     * @param orderNo 订单编号
     * @return 计划列表
     */
    @Deprecated
    CommonPage<FlightPlanVO> getFlightPlanListByOrderNo(FlightPlanPageQueryDTO orderNo);

    /**
     * 根据特定权限查询当前飞行规划数据
     * @param dto 查询参数
     * @return 飞行规划
     */
    CommonPage<FlightOrderVO> getOrderPlanListByPage(@NotNull(message = "订单查询参数不能为空") OrderPageQueryDTO dto);

    /**
     * 查询飞行规划点位
     * @param orderNo 订单/规划单号
     * @return 点位信息
     */
    List<FlightPositionVO> queryPlanPositions(String orderNo);

    /**
     * 飞行次数追加申请
     *
     * @param dto 追加参数
     * @return 追加结果
     */
    FlightOrderFlyingNumApplyResponseDTO flyingNumApplySubmit(FlightOrderFlyingNumApplyDTO dto);

    /**
     * 飞行次数追加申请
     *
     * @param dto 追加参数
     * @return 追加结果
     */
    FlightOrderFlyingNumApplyResponseDTO flyingNumApplyEdit(@NotNull FlightOrderFlyingNumApplyEditDTO dto);

    /**
     * 根据计划ID返回成果内容
     * @param planId 计划ID
     * @return 成果
     */
    List<FlightAchievementVO> queryAchievementList(String planId);

    /**
     * 根据计划ID对应日期的location比对上一次的成果
     * @param queryDTO 查询参数
     * @return 上一次 | 既定日期比对成果
     */
    FlightAchievementVO queryAchievementCompareDate(FlightPlanAchievementQueryDTO queryDTO);
}