package com.deepinnet.skyflow.operationcenter.service.draft;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.FlightDraftDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDraftQueryDTO;

/**
 * 飞行草稿服务接口
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface FlightDraftService {

    /**
     * 保存草稿
     *
     * @param flightDraftDTO 草稿DTO
     * @return 保存后的草稿DTO
     */
    String saveDraft(FlightDraftDTO flightDraftDTO);

    /**
     * 更新草稿
     *
     * @param flightDraftDTO 草稿DTO
     * @return 更新后的草稿DTO
     */
    void updateDraft(FlightDraftDTO flightDraftDTO);

    /**
     * 删除草稿
     *
     * @param code 草稿code
     * @return 是否成功
     */
    boolean removeDraft(String code);

    /**
     * 根据ID查询草稿
     *
     * @param code 草稿ID
     * @return 草稿DTO
     */
    FlightDraftDTO getDraft(String code);

    /**
     * 分页查询草稿列表
     *
     * @param queryDTO 查询条件
     * @return 草稿分页列表
     */
    CommonPage<FlightDraftDTO> pageDrafts(FlightDraftQueryDTO queryDTO);
}