package com.deepinnet.skyflow.operationcenter.service.demand.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.error.ErrorCode;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.infra.api.dto.*;
import com.deepinnet.infra.api.enums.ApprovalStatusEnum;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.*;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightDemandStatCondition;
import com.deepinnet.skyflow.operationcenter.dal.repository.*;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandRoutineInspectionRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightDemandEmergencyResponseRepository;
import com.deepinnet.skyflow.operationcenter.dto.*;
import com.deepinnet.skyflow.operationcenter.enums.*;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveInstanceService;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveRuleService;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.*;
import com.deepinnet.skyflow.operationcenter.service.client.*;
import com.deepinnet.skyflow.operationcenter.service.constants.ApproveBizTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.demand.DemandLockHelper;
import com.deepinnet.skyflow.operationcenter.service.demand.FlightDemandService;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightDemandConvert;
import com.deepinnet.skyflow.operationcenter.service.demand.ServiceProviderClient;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyDispatcher;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.util.TimeFrequencyUtil;
import com.deepinnet.skyflow.operationcenter.util.DataFetcher;
import com.deepinnet.skyflow.operationcenter.util.GISUtil;
import com.deepinnet.skyflow.operationcenter.util.IdGenerateUtil;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.vo.FlightDemandVO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsStatDTO;
import com.deepinnet.spatiotemporalplatform.dto.FlightEventsStatQueryDTO;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import com.deepinnet.spatiotemporalplatform.vo.FlightPlanVO;
import com.deepinnet.tenant.TenantIdUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.locationtech.jts.geom.*;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 飞行需求服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FlightDemandServiceImpl implements FlightDemandService {

    public static final String VIRTUAL_CODE = "virtual";
    public static final String VIRTUAL_NAME = "合并需求类目";
    public static final String SYSTEM_USER_NO = "SYSTEM";
    public static final String SYSTEM_USER_NAME = "系统";

    @Resource
    private FlightDemandRepository flightDemandRepository;

    @Resource
    private FlightDemandPropRepository flightDemandPropRepository;

    @Resource
    private FlightDemandConvert flightDemandConvert;

    @Resource
    private FlightPlanQueryClient flightPlanQueryClient;

    @Resource
    private FlightEventRemoteClient flightEventRemoteClient;

    @Resource
    private FlightDemandAreaRepository flightDemandAreaRepository;

    @Resource
    private FlightMergeDemandRelationRepository flightMergeDemandRelationRepository;

    @Resource
    private FlightDemandMatchServiceProviderRepository flightDemandMatchServiceProviderRepository;

    @Resource
    private UserRemoteClient userClient;

    @Resource
    private ServiceProviderClient serviceProviderClient;

    @Resource
    private StrategyDispatcher strategyDispatcher;

    @Resource
    private FlightDepartmentClient departmentClient;

    @Resource
    private FlightDemandMergeHandleRepository flightDemandMergeHandleRepository;

    @Resource
    private BeanFactory beanFactory;

    @Resource
    private DemandLockHelper demandLockHelper;

    @Resource
    private FlightDemandRoutineInspectionRepository flightDemandRoutineInspectionRepository;

    @Resource
    private FlightDemandEmergencyResponseRepository flightDemandEmergencyResponseRepository;

    @Resource
    private FlightPositionRepository flightPositionRepository;

    @Resource
    private ApproveRuleService approveRuleService;

    @Resource
    private ApproveInstanceService approveInstanceService;

    @Resource
    private Executor httpServletAwareExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveFlightDemand(FlightDemandDTO flightDemandDTO) {
        //todo 需求失效必须是飞行规划时段的子集
        //todo 总飞行次数不能超过规划中的总飞行次数
        strategyDispatcher.validate(flightDemandDTO.getType(), flightDemandDTO);
        if (StrUtil.isBlank(flightDemandDTO.getDemandNo())) {
            String flightDemandCode = IdGenerateUtil.getId("FLIGHT_DEMAND");
            flightDemandDTO.setDemandNo(flightDemandCode);
        }
        //初始化为未匹配状态
        if (flightDemandDTO.getMatchStatus() == null) {
            flightDemandDTO.setMatchStatus(FlightDemandMatchStatusEnum.UNMATCHED);
        }
        if (flightDemandDTO.getMergeStatus() == null) {
            flightDemandDTO.setMergeStatus(MergeStatusEnum.NOT_PARTICIPATED);
        }

        if (flightDemandDTO.getPublishTime() == null) {
            flightDemandDTO.setPublishTime(LocalDateTime.now());
        }

        if (flightDemandDTO.getIsMergeDemand() == null || Objects.equals(Boolean.FALSE, flightDemandDTO.getIsMergeDemand())) {
            flightDemandDTO.setIsMergeDemand(Boolean.FALSE);

            // 创建自关联记录（target_type = self）
            createSelfRelation(flightDemandDTO);
        }

        //设置多区域需求WKT
        if (CollectionUtil.isNotEmpty(flightDemandDTO.getAreaList())) {
            setMultiAreaDemandWkt(flightDemandDTO.getAreaList(), flightDemandDTO);
        }

        // 设置新增字段的值
//        setFrequencyFields(flightDemandDTO);

        // 设置分组编号和版本号
        setGroupCodeAndVersion(flightDemandDTO);

        if (Objects.equals(flightDemandDTO.getIsSupplement(), true)) {
            //如果是补报的需求不需要走审核
            flightDemandDTO.setNeedApprove(Boolean.FALSE);
        }

        //默认都是最新的
        flightDemandDTO.setIsLatest(true);

        // 设置区域数量
        long areaNum = flightDemandDTO.getAreaList().stream().filter(FlightDemandAreaDTO::getIsChecked).count();
        flightDemandDTO.setAreaNum((int) areaNum);

        flightDemandDTO.setApproveStatus(ApproveStatusEnum.PENDING.name());
        FlightDemandDO flightDemandDO = flightDemandConvert.convertToDO(flightDemandDTO);

        boolean success = flightDemandRepository.save(flightDemandDO);
        if (!success) {
            log.error("保存飞行需求失败: {}", flightDemandDTO.getDemandNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
        }

        // 保存需求类型特征属性
        saveFlightDemandTypeDetail(flightDemandDTO);

        // 保存需求额外属性
        saveDemandProps(flightDemandDTO.getMergeDemandProp(), flightDemandDTO.getDemandNo(), flightDemandDTO.getTenantId());

        // 保存区域信息
        saveFlightDemandAreas(flightDemandDTO);

        //保存额外的区域信息，需要同步到需求规划里面
        saveAddArea(flightDemandDTO);

        log.info("保存飞行需求成功: {}", flightDemandDO.getId());

        String approvalId = createApproval(flightDemandDTO);

        flightDemandDTO.setApprovalId(approvalId);
        flightDemandRepository.update(Wrappers.lambdaUpdate(FlightDemandDO.class)
                .set(FlightDemandDO::getApprovalId, approvalId)
                .eq(FlightDemandDO::getDemandNo, flightDemandDTO.getDemandNo()));

        log.info("创建审批单成功: {}", approvalId);
        return flightDemandDTO.getDemandNo();
    }

    private String createApproval(FlightDemandDTO flightDemandDTO) {
        String approvalId;
        if (Objects.equals(flightDemandDTO.getNeedApprove(), false)) {
            //如果设定了审批状态为不需要审批的，则创建一个自动审核的审批单，没有审核人直接通过
            ApproveSubmitEntity submitEntity = new ApproveSubmitEntity();
            submitEntity.setTenantId(flightDemandDTO.getTenantId());
            submitEntity.setBizType(ApproveBizTypeEnum.FLIGHT_DEMAND.name());
            submitEntity.setBizId(flightDemandDTO.getDemandNo());
            //这里不需要额外的回调参数
            submitEntity.setCallbackParams(null);
            submitEntity.setSubmitUserId(StringUtils.isNotBlank(flightDemandDTO.getEditorNo()) ? flightDemandDTO.getEditorNo() : flightDemandDTO.getPublisherNo());
            approvalId = approveInstanceService.createAutoPassApprove(submitEntity);
        } else {
            ApproveRuleExecEntity approveRuleExecEntity = new ApproveRuleExecEntity();
            approveRuleExecEntity.setInstanceConfigRuleCode(ApproveConstants.ORG_LEVEL);
            ApproveSubmitEntity approveSubmitEntity = new ApproveSubmitEntity();
            approveSubmitEntity.setBizType(ApproveBizTypeEnum.FLIGHT_DEMAND.name());
            approveSubmitEntity.setBizId(flightDemandDTO.getDemandNo());
            approveSubmitEntity.setSubmitUserId(StringUtils.isNotBlank(flightDemandDTO.getEditorNo()) ? flightDemandDTO.getEditorNo() : flightDemandDTO.getPublisherNo());
            approveSubmitEntity.setCallbackParams(null);
            approveSubmitEntity.setTenantId(flightDemandDTO.getTenantId());
            approveRuleExecEntity.setApproveSubmitEntity(approveSubmitEntity);
            ApprovalInstance instance = approveRuleService.executeApproveRule(approveRuleExecEntity);
            approvalId = approveInstanceService.createApprove(instance);
        }
        return approvalId;
    }

    private void setFrequencyFields(FlightDemandDTO flightDemandDTO) {
        // 计算总飞行次数
        Integer flyingNum = flightDemandDTO.getFlyingNum();
        LocalDate startDate = flightDemandDTO.getDemandStartTime();
        LocalDate endDate = flightDemandDTO.getDemandEndTime();
        FlyingFrequencyEnum frequency = flightDemandDTO.getFlyingFrequency();

        if (startDate != null && endDate != null && frequency != null && flyingNum != null) {
            int totalFlightCount = calculateFlightCountByFrequency(frequency, startDate, endDate, flyingNum);
            flightDemandDTO.setTotalFlyingNum(totalFlightCount);
        }
    }

    /**
     * 设置分组编号和版本号
     *
     * @param flightDemandDTO 需求DTO
     */
    private void setGroupCodeAndVersion(FlightDemandDTO flightDemandDTO) {
        String parentCode = flightDemandDTO.getParentCode();

        if (StrUtil.isBlank(parentCode)) {
            // 新建需求，创建新的分组编号，版本号为1
            flightDemandDTO.setGroupCode(flightDemandDTO.getDemandNo());
            flightDemandDTO.setVersion(1);
        } else {
            // 编辑需求，使用父需求的分组编号，版本号递增
            FlightDemandDO parentDemand = flightDemandRepository.getOne(
                    Wrappers.lambdaQuery(FlightDemandDO.class)
                            .eq(FlightDemandDO::getDemandNo, parentCode)
            );

            if (parentDemand == null) {
                log.error("父需求不存在: {}", parentCode);
                throw new BizException(BizErrorCode.DEMAND_NOT_FOUND.getCode(), "父需求不存在");
            }

            flightDemandDTO.setGroupCode(parentDemand.getGroupCode());
            flightDemandDTO.setVersion(parentDemand.getVersion() + 1);
        }
    }

    @Override
    public FlightDemandDTO getFlightDemandByNo(String demandNo) {
        FlightDemandDO flightDemandDO = queryDemandDOByDemandCode(demandNo);

        FlightDemandDTO flightDemandDTO = flightDemandConvert.convert(flightDemandDO);

        //获取需求类型特征属性
        readFlightDemandTypeDetail(flightDemandDTO);

        //获取需求额外属性
        readFieldFromProp(flightDemandDTO);

        //获取服务商列表
        List<FlightDemandMatchServiceProviderDTO> serviceProviderList = this.queryMatchServiceProviderListByDemandCode(demandNo);
        flightDemandDTO.setServiceProviderList(serviceProviderList);

        // 合并需求的特殊处理
        if (Boolean.TRUE.equals(flightDemandDO.getIsMergeDemand())) {
            setMergeDemand(flightDemandDTO);
        } else {
            setOriginDemand(flightDemandDTO);
        }
        return flightDemandDTO;
    }

    private void setOriginDemand(FlightDemandDTO originDemandDTO) {
        originDemandDTO.setAreaList(queryOriginDemandAreaList(originDemandDTO.getDemandNo()));
    }

    private void setMergeDemand(FlightDemandDTO mergeDemandDTO) {
        // 获取原始需求编号列表（只查询target_type = merge的记录）
        List<FlightMergeDemandRelationDO> mergeDemandRelationDOList = flightMergeDemandRelationRepository.list(
                Wrappers.lambdaQuery(FlightMergeDemandRelationDO.class)
                        .eq(FlightMergeDemandRelationDO::getTargetDemandCode, mergeDemandDTO.getDemandNo())
                        .eq(FlightMergeDemandRelationDO::getTargetType, FlightMergeDemandTargetTypeEnum.MERGE.getCode())
        );

        if (CollectionUtil.isNotEmpty(mergeDemandRelationDOList)) {

            List<FlightDemandDTO> originDemandList = mergeDemandRelationDOList.stream()
                    .map(mergeDemandRelationDO -> {
                        return this.getFlightDemandByNo(mergeDemandRelationDO.getOriginalDemandCode());
                    })
                    .collect(Collectors.toList());
            mergeDemandDTO.setOriginDemandList(originDemandList);

            mergeDemandDTO.setAreaList(queryMergeDemandAreaList(mergeDemandDTO.getDemandNo()));
            List<String> originDemandOrderNoList = originDemandList.stream().map(FlightDemandDTO::getFlightOrderNo).collect(Collectors.toList());
            mergeDemandDTO.setFlightOrderNoList(originDemandOrderNoList);
        }
    }

    @Override
    public List<FlightDemandAreaDTO> queryMergeDemandAreaList(String mergeDemandNo) {
        List<String> originDemandCodeList = flightMergeDemandRelationRepository.list(
                        Wrappers.lambdaQuery(FlightMergeDemandRelationDO.class)
                                .eq(FlightMergeDemandRelationDO::getTargetDemandCode, mergeDemandNo)
                                .eq(FlightMergeDemandRelationDO::getTargetType, FlightMergeDemandTargetTypeEnum.MERGE.getCode())
                )
                .stream()
                .map(FlightMergeDemandRelationDO::getOriginalDemandCode)
                .collect(Collectors.toList());
        return this.queryOriginDemandAreaList(originDemandCodeList);
    }

    @Override
    public List<FlightDemandAreaDTO> queryOriginDemandAreaList(String originDemandNo) {
        return queryOriginDemandAreaList(Collections.singletonList(originDemandNo));
    }

    private List<FlightDemandAreaDTO> queryOriginDemandAreaList(List<String> originDemandCodeList) {
        // 获取所有原始需求的区域列表
        List<FlightDemandAreaDO> allAreas = flightDemandAreaRepository.list(
                Wrappers.lambdaQuery(FlightDemandAreaDO.class)
                        .in(FlightDemandAreaDO::getDemandCode, originDemandCodeList)
        );

        // 转换为DTO
        List<FlightDemandAreaDTO> areaDTOs = allAreas.stream()
                .map(areaDO -> flightDemandConvert.toFlightDemandAreaDTO(areaDO))
                .collect(Collectors.toList());
        return areaDTOs;
    }

    @Override
    public CommonPage<FlightDemandDTO> pageQueryFlightDemandCustomerManage(FlightDemandQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<FlightDemandDO> flightDemandDOList = flightDemandRepository.pageQueryFlightDemandCustomerManage(queryDTO);

        if (CollectionUtils.isEmpty(flightDemandDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<FlightDemandDTO> flightDemandDTOList = flightDemandConvert.convertList(flightDemandDOList);

        // 填充完整信息 - 因为只查询原始需求，所以不需要判断是否为合并需求
        setProp(flightDemandDTOList);

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), page.getPages(), page.getTotal(), flightDemandDTOList);
    }

    @Override
    public CommonPage<FlightDemandDTO> pageQueryFlightDemandOpManage(FlightDemandQueryDTO queryDTO) {
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<FlightDemandDO> flightDemandDOList = flightDemandRepository.pageQueryFlightDemandOpManage(queryDTO);

        if (CollectionUtils.isEmpty(flightDemandDOList)) {
            return CommonPage.buildEmptyPage();
        }

        List<FlightDemandDTO> flightDemandDTOList = flightDemandConvert.convertList(flightDemandDOList);

        // 填充完整信息
        setProp(flightDemandDTOList);

        return CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), page.getPages(), page.getTotal(), flightDemandDTOList);
    }

    private void setProp(List<FlightDemandDTO> flightDemandDTOList) {
        Map<String, List<FlightDemandMatchServiceProviderDTO>> demandServiceProviderMap = queryMatchServiceProviderListByDemandCodes(flightDemandDTOList.stream().map(FlightDemandDTO::getDemandNo).collect(Collectors.toList()));
        for (FlightDemandDTO flightDemandDTO : flightDemandDTOList) {
            // 获取服务商列表
            flightDemandDTO.setServiceProviderList(demandServiceProviderMap.get(flightDemandDTO.getDemandNo()));
//
//            // 获取需求属性
//            readFieldFromProp(flightDemandDTO);
//
//            // 设置区域信息和订单号
//            if (Boolean.TRUE.equals(flightDemandDTO.getIsMergeDemand())) {
//                flightDemandDTO.setAreaList(queryMergeDemandAreaList(flightDemandDTO.getDemandNo()));
//                // 获取合并需求关联的原始需求的订单号列表（target_type = merge）
//                List<FlightMergeDemandRelationDO> relations = flightMergeDemandRelationRepository.list(
//                        Wrappers.lambdaQuery(FlightMergeDemandRelationDO.class)
//                                .eq(FlightMergeDemandRelationDO::getTargetDemandCode, flightDemandDTO.getDemandNo())
//                                .eq(FlightMergeDemandRelationDO::getTargetType, FlightMergeDemandTargetTypeEnum.MERGE.getCode())
//                );
//                List<String> orderNos = relations.stream()
//                        .map(FlightMergeDemandRelationDO::getFlightOrderNo)
//                        .filter(StringUtils::isNotBlank)
//                        .collect(Collectors.toList());
//                flightDemandDTO.setFlightOrderNoList(orderNos);
//            } else {
//                flightDemandDTO.setAreaList(queryOriginDemandAreaList(flightDemandDTO.getDemandNo()));
//            }
        }
    }

    @Override
    public FlightDemandDTO getFlightDemandByPlanId(String planId) {
        FlightPlanVO flightPlanVO = flightPlanQueryClient.queryFlightPlan(planId);
        if (flightPlanVO == null) {
            return null;
        }
        return this.getFlightDemandByNo(flightPlanVO.getBizNo());
    }

    @Override
    public FlightDemandStatsDTO get90DayStatistics() {
        FlightDemandStatsDTO res = new FlightDemandStatsDTO();

        FlightDemandStatCondition condition = new FlightDemandStatCondition();
        condition.setUserNo(UserUtil.getUserNo());

        FlightDemandStatsDO statsDO = flightDemandRepository.select90DayStats(condition);

        if (statsDO == null) {
            return res;
        }

        res.setPlanNum(statsDO.getPlanNum());
        res.setFlightHours(String.format("%.1f", Objects.requireNonNullElse(statsDO.getFlightDuration(), 0D) / 3600));
        res.setFlightDistance(String.format("%.1f", Objects.requireNonNullElse(statsDO.getFlightMiles(), 0D) / 1000));
        res.setAerialPatrolArea(String.format("%.1f", Objects.requireNonNullElse(statsDO.getAerialPatrolArea(), 0D)));

        FlightDemandStatsDO avgRespTimeStats = flightDemandRepository.select90DayEmergencyResponseTimeStats(condition);
        if (avgRespTimeStats != null && avgRespTimeStats.getAverageResponseTime() != null) {
            res.setAverageResponseTime(String.valueOf((int) Math.ceil(avgRespTimeStats.getAverageResponseTime())));
        }

        FlightEventsStatQueryDTO flightEventsStatQueryDTO = new FlightEventsStatQueryDTO();
        flightEventsStatQueryDTO.setUserNo(UserUtil.getUserNo());
        flightEventsStatQueryDTO.setStartTime(LocalDateTime.now().minusMonths(3));
        flightEventsStatQueryDTO.setTenantId(TenantIdUtil.getTenantId());
        flightEventsStatQueryDTO.setDemandType(FlightDemandTypeEnum.ROUTINE_INSPECTION.name());
        List<FlightEventsStatDTO> flightEventsStatDTOS = flightEventRemoteClient.queryFlightEventsStat(flightEventsStatQueryDTO);
        res.setAerialPatrolEventDetected(String.valueOf(org.apache.commons.collections4.CollectionUtils.emptyIfNull(flightEventsStatDTOS).stream()
                .mapToLong(FlightEventsStatDTO::getEventNum)
                .sum()));

        return res;
    }

    @Override
    public FlightDemandStatsDTO getStatistics(FlightDemandStatCondition condition) {


        FlightDemandStatsDTO res = new FlightDemandStatsDTO();

        DataFetcher<FlightDemandStatsDTO> dataFetcher = new DataFetcher<>(res);

        dataFetcher.addNode(() -> flightDemandRepository.selectStats(condition), this::handlerStats);
        dataFetcher.addNode(() -> flightDemandRepository.selectEmergencyResponseTimeStats(condition), this::handlerAvgRespTimeStats);

        dataFetcher.addNode(() -> {
            FlightEventsStatQueryDTO flightEventsStatQueryDTO = new FlightEventsStatQueryDTO();
            flightEventsStatQueryDTO.setUserNoList(condition.getUserNoList());
            flightEventsStatQueryDTO.setStartTime(condition.getStartTime());
            flightEventsStatQueryDTO.setTenantId(TenantIdUtil.getTenantId());
            flightEventsStatQueryDTO.setDemandType(FlightDemandTypeEnum.ROUTINE_INSPECTION.name());
            return String.valueOf(org.apache.commons.collections4.
                    CollectionUtils.emptyIfNull(flightEventRemoteClient.queryFlightEventsStat(flightEventsStatQueryDTO))
                    .stream()
                    .mapToLong(FlightEventsStatDTO::getEventNum)
                    .sum());
        },  FlightDemandStatsDTO::setAerialPatrolEventDetected);

        dataFetcher.addNode(() -> {
            FlightEventsStatQueryDTO flightEventsStatQueryDTO = new FlightEventsStatQueryDTO();
            flightEventsStatQueryDTO.setUserNoList(condition.getUserNoList());
            flightEventsStatQueryDTO.setStartTime(condition.getStartTime());
            flightEventsStatQueryDTO.setTenantId(TenantIdUtil.getTenantId());
            flightEventsStatQueryDTO.setDemandType(FlightDemandTypeEnum.EMERGENCY_RESPONSE.name());
            return String.valueOf(org.apache.commons.collections4.
                    CollectionUtils.emptyIfNull(flightEventRemoteClient.queryFlightEventsStat(flightEventsStatQueryDTO))
                    .stream()
                    .mapToLong(FlightEventsStatDTO::getEventNum)
                    .sum());
        },  FlightDemandStatsDTO::setAerialPatrolEventEmergencyDetected);

        dataFetcher.fetch(5, httpServletAwareExecutor);

        return res;
    }

    private void handlerStats(FlightDemandStatsDTO dto, FlightDemandStatsDO statsDO) {
        if(statsDO == null) return;

        dto.setPlanNum(statsDO.getPlanNum());
        dto.setDemandNum(statsDO.getDemandNum());
        dto.setPlanFinishedNum(statsDO.getPlanFinishedNum());

        dto.setFlightHours(String.format("%.1f", Objects.requireNonNullElse(statsDO.getFlightDuration(), 0D) / 3600));
        dto.setFlightDistance(String.format("%.1f", Objects.requireNonNullElse(statsDO.getFlightMiles(), 0D) / 1000));
        dto.setAerialPatrolArea(String.format("%.1f", Objects.requireNonNullElse(statsDO.getAerialPatrolArea(), 0D)));
    }

    private void handlerAvgRespTimeStats(FlightDemandStatsDTO dto, FlightDemandStatsDO avgRespTimeStats) {
        if (avgRespTimeStats != null && avgRespTimeStats.getAverageResponseTime() != null) {
            dto.setAverageResponseTime(String.valueOf((int) Math.ceil(avgRespTimeStats.getAverageResponseTime())));
        }
    }

    @Override
    public List<FlightDemandDayStatsDTO> get7DayStatistics(FlightDemandStatCondition condition) {

        List<FlightDemandDayStatsDO> list = flightDemandRepository.select7DayStats(condition);

        return list.stream().map(s -> {
            FlightDemandDayStatsDTO statsDTO = new FlightDemandDayStatsDTO();
            statsDTO.setNum(s.getPlanNum());
            statsDTO.setDate(s.getDate().format(FlightDemandDayStatsDTO.DF));
            return statsDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 保存需求属性到专门的表中
     */
    private void saveFlightDemandTypeDetail(FlightDemandDTO flightDemandDTO) {
        // 根据对象类型保存到对应的表中
        FlightDemandTypeEnum type = flightDemandDTO.getType();
        switch (type) {
            case ROUTINE_INSPECTION:
                FlightDemandRoutineInspectionDO routineDO = flightDemandConvert.toRoutineInspectionDO(flightDemandDTO.getRoutineInspectionDetail(), flightDemandDTO.getDemandNo(), flightDemandDTO.getTenantId());
                flightDemandRoutineInspectionRepository.save(routineDO);
                break;
            case EMERGENCY_RESPONSE:
                FlightDemandEmergencyResponseDO emergencyDO = flightDemandConvert.toEmergencyResponseDO(flightDemandDTO.getEmergencyResponseDetail(), flightDemandDTO.getDemandNo(), flightDemandDTO.getTenantId());
                flightDemandEmergencyResponseRepository.save(emergencyDO);
                break;
            default:
                break;
        }
    }

    private void saveDemandProps(Object obj, String demandNo, String tenantId) {
        // 合并需求属性仍然使用原来的prop表方式
        List<FlightDemandPropDO> flightDemandPropDOList = new ArrayList<>();
        savePropsFromObject(obj, demandNo, tenantId, flightDemandPropDOList);
        if (CollectionUtil.isNotEmpty(flightDemandPropDOList)) {
            flightDemandPropRepository.saveBatch(flightDemandPropDOList);
        }
    }

    /**
     * 从对象中提取属性并保存
     */
    private void savePropsFromObject(Object obj, String demandNo, String tenantId, List<FlightDemandPropDO> propList) {
        if (obj == null) return;
        Field[] declaredFields = obj.getClass().getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            try {
                Object value = declaredField.get(obj);
                if (value != null) {
                    FlightDemandPropDO flightDemandPropDO = new FlightDemandPropDO();
                    flightDemandPropDO.setDemandNo(demandNo);
                    flightDemandPropDO.setTenantId(tenantId);
                    flightDemandPropDO.setPropKey(declaredField.getName());
                    flightDemandPropDO.setPropValue(JsonUtil.toJsonStr(value));
                    propList.add(flightDemandPropDO);
                }
            } catch (IllegalAccessException e) {
                log.error("保存需求属性失败", e);
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "保存需求属性失败");
            }
        }
    }

    private void readFlightDemandTypeDetail(FlightDemandDTO flightDemandDTO) {
        String demandNo = flightDemandDTO.getDemandNo();
        FlightDemandTypeEnum demandType = flightDemandDTO.getType();

        // 根据需求类型从对应的表中读取数据
        switch (demandType) {
            case ROUTINE_INSPECTION:
                FlightDemandRoutineInspectionDO routineDO = flightDemandRoutineInspectionRepository.getOne(
                        Wrappers.lambdaQuery(FlightDemandRoutineInspectionDO.class)
                                .eq(FlightDemandRoutineInspectionDO::getDemandNo, demandNo)
                );
                if (routineDO != null) {
                    RoutineInspectionFlightDemandDTO routineDto = flightDemandConvert.toRoutineInspectionDTO(routineDO);
                    flightDemandDTO.setRoutineInspectionDetail(routineDto);
                }
                break;
            case EMERGENCY_RESPONSE:
                FlightDemandEmergencyResponseDO emergencyDO = flightDemandEmergencyResponseRepository.getOne(
                        Wrappers.lambdaQuery(FlightDemandEmergencyResponseDO.class)
                                .eq(FlightDemandEmergencyResponseDO::getDemandNo, demandNo)
                );
                if (emergencyDO != null) {
                    EmergencyResponseFlightDemandDTO emergencyDto = flightDemandConvert.toEmergencyResponseDTO(emergencyDO);
                    flightDemandDTO.setEmergencyResponseDetail(emergencyDto);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 从专门的表中读取需求属性
     *
     * @param flightDemandDTO 需求DTO
     */
    private void readFieldFromProp(FlightDemandDTO flightDemandDTO) {
        String demandNo = flightDemandDTO.getDemandNo();

        // 合并需求属性仍然使用原来的prop表方式
        if (flightDemandDTO.getIsMergeDemand()) {
            List<FlightDemandPropDO> propDOList = flightDemandPropRepository.list(
                    Wrappers.lambdaQuery(FlightDemandPropDO.class)
                            .eq(FlightDemandPropDO::getDemandNo, demandNo)
            );

            if (CollectionUtil.isNotEmpty(propDOList)) {
                Map<String, String> propMap = propDOList.stream()
                        .collect(Collectors.toMap(FlightDemandPropDO::getPropKey, FlightDemandPropDO::getPropValue));
                flightDemandDTO.setMergeDemandProp(getSpecialDetail(FlightMergeDemandPropDTO.class, propMap));
            }
        }
    }


    private <T> T getSpecialDetail(Class<T> demandClass, Map<String, String> propMap) {
        Field[] declaredFields = demandClass.getDeclaredFields();
        Map<String, Object> objMap = new HashMap<>();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            String objStr = propMap.get(declaredField.getName());
            if (objStr == null) {
                continue;
            }
            objMap.put(declaredField.getName(), JsonUtil.parseJson(objStr, declaredField.getType()));
        }

        return JsonUtil.parseJson(JsonUtil.toJsonStr(objMap), demandClass);
    }

    /**
     * 创建自关联记录（target_type = self）
     *
     * @param flightDemandDTO 需求DTO
     */
    private void createSelfRelation(FlightDemandDTO flightDemandDTO) {
        // 只为原始需求创建自关联记录
        if (Boolean.TRUE.equals(flightDemandDTO.getIsMergeDemand())) {
            return;
        }

        FlightMergeDemandRelationDO selfRelation = new FlightMergeDemandRelationDO();
        selfRelation.setOriginalDemandCode(flightDemandDTO.getDemandNo());
        // 自关联：原需求编号和目标需求编号相同
        selfRelation.setTargetDemandCode(flightDemandDTO.getDemandNo());
        selfRelation.setTenantId(flightDemandDTO.getTenantId());
        selfRelation.setMergeStatus(MergeStatusEnum.CANNOT_MERGE.getCode());
        selfRelation.setTargetType(FlightMergeDemandTargetTypeEnum.SELF.getCode());
        selfRelation.setType(flightDemandDTO.getType().name());
        selfRelation.setPublisherNo(flightDemandDTO.getPublisherNo());
        selfRelation.setFlightOrderNo(flightDemandDTO.getFlightOrderNo());
        selfRelation.setOriginalDemandName(flightDemandDTO.getName());
        selfRelation.setTargetDemandName(flightDemandDTO.getName());
        selfRelation.setPublishTime(flightDemandDTO.getPublishTime());

        boolean success = flightMergeDemandRelationRepository.save(selfRelation);
        if (!success) {
            log.error("创建自关联记录失败: {}", flightDemandDTO.getDemandNo());
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "创建自关联记录失败");
        }
    }

    /**
     * 保存需求区域信息
     *
     * @param flightDemandDTO 需求DTO
     */
    private void saveFlightDemandAreas(FlightDemandDTO flightDemandDTO) {
        if (flightDemandDTO.getAreaList() == null || flightDemandDTO.getAreaList().isEmpty()) {
            return;
        }

        List<FlightDemandAreaDO> areaDOList = flightDemandDTO.getAreaList().stream()
                .filter(area -> area.getIsChecked())
                .map(area -> {
                    FlightDemandAreaDO areaDO = flightDemandConvert.toFlightDemandAreaDO(area);
                    if (StringUtils.isNotBlank(area.getAreaCode())) {
                        //说明这个区域原本就是在规划里的
                        areaDO.setAreaCode(area.getAreaCode());
                    } else {
                        String areaCode = IdGenerateUtil.getId("AREA");
                        areaDO.setAreaCode(areaCode);
                        area.setAreaCode(areaCode);
                    }
                    areaDO.setDemandCode(flightDemandDTO.getDemandNo());
                    areaDO.setDemandName(flightDemandDTO.getName());
                    areaDO.setTenantId(flightDemandDTO.getTenantId());
                    return areaDO;
                }).collect(Collectors.toList());

        if (!areaDOList.isEmpty()) {
            flightDemandAreaRepository.saveBatch(areaDOList);
        }
    }

    private void saveAddArea(FlightDemandDTO flightDemandDTO) {
        List<FlightPositionDO> positionDOList = flightDemandDTO.getAreaList()
                .stream()
                .filter(areaDTO -> areaDTO.getIsAdd())
                .map(areaDTO -> {
                    FlightPositionDO positionDO = new FlightPositionDO();

                    //这个code可能为空，那就说明需求上新增了，但是没有勾选
                    String areaCode = areaDTO.getAreaCode();
                    if (StringUtils.isBlank(areaCode)) {
                        areaCode = IdGenerateUtil.getId("AREA");
                    }
                    positionDO.setPositionNo(areaCode);
                    positionDO.setOrderNo(flightDemandDTO.getFlightOrderNo());
                    positionDO.setType(areaDTO.getType());
                    positionDO.setName(areaDTO.getAreaName());
                    positionDO.setFlightPosition(areaDTO.getAreaCoordinate());
                    positionDO.setCenterPoint(WktUtil.toWkt(WktUtil.toPoint(areaDTO.getCenterPointLongitude(), areaDTO.getCenterPointLatitude())));
                    positionDO.setTenantId(flightDemandDTO.getTenantId());
                    return positionDO;
                }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(positionDOList)) {
            flightPositionRepository.saveBatch(positionDOList);
        }
    }

    /**
     * 创建合并需求，一个合并建议
     *
     * @param createDTOList 创建合并需求DTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createMergeDemand(List<MergeDemandCreateDTO> createDTOList) {
        if (CollectionUtil.isEmpty(createDTOList)) {
            log.warn("创建合并需求参数为空");
            return null;
        }

        String mergeHandleCode = IdGenerateUtil.getId("merge_handle");

        // 创建合并处理记录
        FlightDemandMergeHandleDO mergeHandleDO = new FlightDemandMergeHandleDO();
        mergeHandleDO.setMergeHandleCode(mergeHandleCode);
        mergeHandleDO.setMergeStatus(MergeStatusEnum.PENDING.getCode());
        mergeHandleDO.setHandleTime(LocalDateTime.now());
        mergeHandleDO.setOperatorId(SYSTEM_USER_NO); // 系统创建
        mergeHandleDO.setOperatorName(SYSTEM_USER_NAME);
        mergeHandleDO.setTenantId(createDTOList.get(0).getTenantId());

        // 计算减少的飞行次数和其他效果数据
        calculateMergeEffects(createDTOList, mergeHandleDO);

        flightDemandMergeHandleRepository.save(mergeHandleDO);

        createDTOList.forEach(mergeDemandCreateDTO -> {
            String tenantId = mergeDemandCreateDTO.getTenantId();
            // 1. 校验原始需求是否存在
            List<FlightDemandDTO> originDemands = mergeDemandCreateDTO.getOriginDemandDTOList();

            // 2. 创建合并需求
            FlightDemandDTO mergeDemandDTO = createMergeFlightDemandDTO(mergeDemandCreateDTO, mergeHandleCode);
            String mergeDemandCode = saveFlightDemand(mergeDemandDTO);

            // 3. 创建合并需求关系记录（target_type = merge）
            List<FlightMergeDemandRelationDO> relations = new ArrayList<>();
            for (FlightDemandDTO originDemand : originDemands) {
                FlightMergeDemandRelationDO relation = new FlightMergeDemandRelationDO();
                relation.setOriginalDemandCode(originDemand.getDemandNo());
                relation.setTargetDemandCode(mergeDemandCode);
                relation.setMergeHandleCode(mergeHandleCode);
                relation.setTenantId(tenantId);
                relation.setMergeStatus(MergeStatusEnum.PENDING.getCode());
                relation.setTargetType(FlightMergeDemandTargetTypeEnum.MERGE.getCode());
                relation.setType(originDemand.getType().getType());
                relation.setPublisherNo(originDemand.getPublisherNo());
                relation.setFlightOrderNo(originDemand.getFlightOrderNo());
                relation.setMergeScheduleTime(JsonUtil.toJsonStr(mergeDemandCreateDTO.getMergeScheduleDTO()));
                relation.setOriginalDemandName(originDemand.getName());
                relation.setTargetDemandName(mergeDemandDTO.getName());
                relation.setPublishTime(originDemand.getPublishTime());
                relations.add(relation);
            }

            boolean success = flightMergeDemandRelationRepository.saveBatch(relations);
            if (!success) {
                log.error("保存合并需求关系失败: {}", mergeDemandCode);
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "保存合并需求关系失败");
            }

            // 4. 更新原始需求状态
            for (FlightDemandDTO originDemand : originDemands) {
                FlightDemandDO originDemandDO = new FlightDemandDO();
                originDemandDO.setId(originDemand.getId());
                originDemandDO.setDemandNo(originDemand.getDemandNo());
                originDemandDO.setMergeStatus(MergeStatusEnum.PENDING.getCode());
                originDemandDO.setMergeHandleCode(mergeHandleCode);
                originDemandDO.setGmtModified(LocalDateTime.now());
                success = flightDemandRepository.updateById(originDemandDO);
                if (!success) {
                    log.error("更新原始需求状态失败: {}", originDemand.getDemandNo());
                    throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "更新原始需求状态失败");
                }
            }
        });
        return mergeHandleCode;
    }

    private FlightDemandDTO createMergeFlightDemandDTO(MergeDemandCreateDTO mergeDemandCreateDTO, String mergeHandleCode) {
        List<FlightDemandDTO> originDemandDTOList = mergeDemandCreateDTO.getOriginDemandDTOList();
        String tenantId = mergeDemandCreateDTO.getTenantId();
        FlightDemandDTO firstDemandDTO = originDemandDTOList.get(0);
        FlightDemandDTO mergeDemand = new FlightDemandDTO();
        mergeDemand.setDemandNo(IdGenerateUtil.getId("FLIGHT_MERGE_DEMAND"));
        mergeDemand.setMergeHandleCode(mergeHandleCode);
        mergeDemand.setIsMergeDemand(Boolean.TRUE);
        mergeDemand.setMergeStatus(MergeStatusEnum.PENDING);
        mergeDemand.setMatchStatus(FlightDemandMatchStatusEnum.UNMATCHED);
        mergeDemand.setTenantId(tenantId);
        //需求发布时间，合并后的新需求生成时间
        mergeDemand.setPublishTime(LocalDateTime.now());
        FlightDemandTypeEnum flightDemandTypeEnum = firstDemandDTO.getType();
        //需求名称：【合】+【需求分类】+【合并后的需求编号】
        String mergeDemandName = String.format("合-%s-%s", flightDemandTypeEnum.getDesc(), mergeDemand.getDemandNo());
        mergeDemand.setName(mergeDemandName);

        //飞行需求描述：合并展示原需求中的描述内容
        List<String> demandDescList = originDemandDTOList.stream().map(FlightDemandDTO::getDemandDesc).collect(Collectors.toList());
        mergeDemand.setDemandDesc(JsonUtil.toJsonStr(demandDescList));
        mergeDemand.setType(flightDemandTypeEnum);
        mergeDemand.setScene(firstDemandDTO.getScene());

        mergeDemand.setInspectionAreaName(firstDemandDTO.getInspectionAreaName());
        mergeDemand.setInspectionAreaCode(firstDemandDTO.getInspectionAreaCode());

        //设置合并需求发布人和组织
        setDemandPublisherAndOrganization(mergeDemand, firstDemandDTO);

        //名称：【订】+【需求分类】+【合并后的需求编号】
        String mergeDemandProductName = String.format("订-%s-%s", flightDemandTypeEnum.getDesc(), mergeDemand.getDemandNo());
        mergeDemand.setProductName(mergeDemandProductName);
        //多个合并需求的增值服务都是一样的
        mergeDemand.setIncrementService(firstDemandDTO.getIncrementService());

        List<FileDTO> demandFileList = originDemandDTOList.stream().map(FlightDemandDTO::getRequestAdditionalFiles).flatMap(list -> list.stream()).collect(Collectors.toList());
        mergeDemand.setRequestAdditionalFiles(demandFileList);

        //这里设置合并需求分类为虚拟分类
        mergeDemand.setCategoryNo(VIRTUAL_CODE);
        mergeDemand.setCategoryFullName(VIRTUAL_NAME);

        //合并区域的WKT
        setMergeDemandWkt(originDemandDTOList, mergeDemand);

        //合并需求特殊属性
        FlightMergeDemandPropDTO mergeDemandProp = new FlightMergeDemandPropDTO();
        mergeDemandProp.setMergeScheduleDTO(mergeDemandCreateDTO.getMergeScheduleDTO());
        mergeDemandProp.setFlightRoutePlan(mergeDemandCreateDTO.getFlightRoutePlan());
        mergeDemand.setMergeDemandProp(mergeDemandProp);

        //各业务类型的模型特征属性都需要相应变更
        Object typeDetail = createMergeDemandTypeDetail(mergeDemandCreateDTO);
        mergeDemand.createDetail(typeDetail);

        setScheduleTimeAndFrequency(mergeDemandCreateDTO, mergeDemand);

        // 设置机型列表
        mergeDemand.setModelCodeList(firstDemandDTO.getModelCodeList());

        //合并需求不需要审核
        mergeDemand.setNeedApprove(Boolean.FALSE);
        return mergeDemand;
    }

    private void setScheduleTimeAndFrequency(MergeDemandCreateDTO mergeDemandCreateDTO, FlightDemandDTO mergeDemand) {
        // 获取所有原始需求的详情
        List<RoutineInspectionFlightDemandDTO> originDetails = mergeDemandCreateDTO.getOriginDemandDTOList().stream()
                .map(FlightDemandDTO::getRoutineInspectionDetail)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(originDetails)) {
            return;
        }

        // 合并逻辑：
        DemandMergeScheduleDTO mergeScheduleDTO = mergeDemandCreateDTO.getMergeScheduleDTO();
        mergeDemand.setFlyingFrequency(FlyingFrequencyEnum.DAILY);
        mergeDemand.setDemandStartTime(mergeScheduleDTO.getMergeScheduleStartDate());
        mergeDemand.setDemandEndTime(mergeScheduleDTO.getMergeScheduleEndDate());
        mergeDemand.setFlyingStartTime(mergeScheduleDTO.getMergeScheduleStartTime());
        mergeDemand.setFlyingEndTime(mergeScheduleDTO.getMergeScheduleEndTime());

        // 3. 其他字段取第一个非空值
        RoutineInspectionFlightDemandDTO firstDetail = originDetails.get(0);
        mergeDemand.setCycleType(firstDetail.getCycleType());
        mergeDemand.setFlyingFrequency(firstDetail.getFlyingFrequency());

        // 4. 飞行次数保持一致，因为在规则中指定了
        mergeDemand.setFlyingNum(mergeDemandCreateDTO.getOriginDemandDTOList().get(0).getRoutineInspectionDetail().getFlyingNum());
    }

    @Value("${merge.publisher-no}")
    private String publisherNo;

    private void setDemandPublisherAndOrganization(FlightDemandDTO mergeDemand, FlightDemandDTO firstDemandDTO) {
        //根据需求的发布用户信息，获取一级部门的组织ID
        UserRootDepartmentQueryDTO queryDTO = new UserRootDepartmentQueryDTO();
        queryDTO.setUserNos(ListUtil.toList(firstDemandDTO.getPublisherNo()));
        List<UserRootDepartmentDTO> userRootDepartmentList = departmentClient.getUserRootDepartments(queryDTO);
        DepartmentFlatDTO departmentDTO = userRootDepartmentList.get(0).getDepartmentDTO();
        mergeDemand.setOrganizationId(String.valueOf(departmentDTO.getId()));
        mergeDemand.setOrganizationName(departmentDTO.getName());

        //todo 一级部门下的统筹账号，暂时先写死
        mergeDemand.setPublisherNo(publisherNo);
        List<UserDetailDTO> userDetailDTOList = userClient.getUserDetailListWithoutTenantId(ListUtil.toList(publisherNo));
        mergeDemand.setPublisherName(userDetailDTOList.get(0).getUserName());
    }

    /**
     * 计算合并效果
     */
    private void calculateMergeEffects(List<MergeDemandCreateDTO> createDTOList, FlightDemandMergeHandleDO mergeHandleDO) {
        MergeEffect mergeEffect = new MergeEffect();

        int totalFlightCountBefore = 0;
        int totalFlightCountAfter = 0;
        Set<String> originDemandCodeSet = new HashSet<>();

        // 计算合并前的总飞行次数和需求数量
        for (MergeDemandCreateDTO createDTO : createDTOList) {
            List<FlightDemandDTO> originDemandList = createDTO.getOriginDemandDTOList();

            // 计算每个原始需求的飞行次数
            for (FlightDemandDTO originDemand : originDemandList) {
                boolean success = originDemandCodeSet.add(originDemand.getDemandNo());
                if (success) {
                    // 根据需求类型计算飞行次数
                    int demandFlightCount = calculateDemandFlightCount(originDemand);
                    totalFlightCountBefore += demandFlightCount;
                }
            }

            // 计算合并后需求的飞行次数
            DemandMergeScheduleDTO mergeSchedule = createDTO.getMergeScheduleDTO();
            if (mergeSchedule != null && !originDemandList.isEmpty()) {
                // 合并后的飞行次数 = 合并周期天数 * 每天飞行次数
                // 假设合并后保持原始需求的飞行频率，但合并飞行轨迹
                FlightDemandDTO firstDemand = originDemandList.get(0);
                int mergedFlightCount = calculateMergedDemandFlightCount(mergeSchedule, firstDemand);
                totalFlightCountAfter += mergedFlightCount;
            }
        }

        // 设置合并效果数据
        mergeEffect.setFlightCountBefore(totalFlightCountBefore);
        mergeEffect.setFlightCountAfter(totalFlightCountAfter);
        mergeEffect.setDemandCountBefore(originDemandCodeSet.size());
        mergeEffect.setDemandCountAfter(createDTOList.size());

        // 计算减少的飞行次数
        int reduceFlightCount = totalFlightCountBefore - totalFlightCountAfter;
        mergeHandleDO.setReduceFlightCount(Math.max(0, reduceFlightCount)); // 确保不为负数

        mergeEffect.setReduceFlightCount(reduceFlightCount);

        mergeHandleDO.setMergeEffectJson(JsonUtil.toJsonStr(mergeEffect));
    }

    /**
     * 计算单个原始需求的总飞行次数
     *
     * @param demandDTO 需求DTO
     * @return 总飞行次数
     */
    private int calculateDemandFlightCount(FlightDemandDTO demandDTO) {
        try {
            if (demandDTO.getType() == FlightDemandTypeEnum.ROUTINE_INSPECTION) {
                RoutineInspectionFlightDemandDTO routineDetail = demandDTO.getRoutineInspectionDetail();
                if (routineDetail == null) {
                    return 0;
                }

                // 获取需求周期天数
                LocalDate startDate = routineDetail.getDemandStartTime();
                LocalDate endDate = routineDetail.getDemandEndTime();
                if (startDate == null || endDate == null) {
                    return 0;
                }

                Integer flyingNum = routineDetail.getFlyingNum();
                if (flyingNum == null || flyingNum <= 0) {
                    flyingNum = 1; // 默认每次飞行1次
                }

                FlyingFrequencyEnum frequency = routineDetail.getFlyingFrequency();
                if (frequency == null) {
                    return flyingNum; // 如果没有频率，默认只飞行一次
                }

                // 根据飞行频率计算总次数
                return calculateFlightCountByFrequency(frequency, startDate, endDate, flyingNum);
            } else {
                throw new RuntimeException("暂时不支持除日常巡检以外的需求类型");
            }
        } catch (Exception e) {
            log.error("计算需求飞行次数失败，需求编号: {}", demandDTO.getDemandNo(), e);
            return 1; // 出错时返回默认值
        }
    }

    /**
     * 计算合并需求的总飞行次数
     *
     * @param mergeSchedule   合并时间计划
     * @param referenceDemand 参考需求（用于获取飞行频率等信息）
     * @return 合并需求的总飞行次数
     */
    private int calculateMergedDemandFlightCount(DemandMergeScheduleDTO mergeSchedule, FlightDemandDTO referenceDemand) {
        if (referenceDemand.getType() == FlightDemandTypeEnum.ROUTINE_INSPECTION) {
            RoutineInspectionFlightDemandDTO routineDetail = referenceDemand.getRoutineInspectionDetail();
            if (routineDetail == null) {
                return 0;
            }

            Integer flyingNum = routineDetail.getFlyingNum();
            if (flyingNum == null || flyingNum <= 0) {
                flyingNum = 1; // 默认每次飞行1次
            }
            return mergeSchedule.getContinuousDays() * flyingNum;

        } else {
            throw new IllegalArgumentException("暂时不支持除日常巡检以外的需求类型");
        }
    }

    /**
     * 根据飞行频率计算总飞行次数
     *
     * @param frequency        飞行频率
     * @param flyingNumPerTime 每次飞行次数
     * @return 总飞行次数
     */
    private int calculateFlightCountByFrequency(FlyingFrequencyEnum frequency, LocalDate startDate, LocalDate endDate, int flyingNumPerTime) {
        Set<DayOfWeek> repeatDays = TimeFrequencyUtil.getDayOfWeekSetFromFrequency(frequency);
        return TimeFrequencyUtil.countWeekdaysInRange(startDate, endDate, repeatDays) * flyingNumPerTime;
    }

    private Object createMergeDemandTypeDetail(MergeDemandCreateDTO mergeDemandCreateDTO) {
        List<FlightDemandDTO> originDemandList = mergeDemandCreateDTO.getOriginDemandDTOList();
        if (CollectionUtils.isEmpty(originDemandList)) {
            return null;
        }

        FlightDemandDTO firstDemand = originDemandList.get(0);
        return strategyDispatcher.mergeDetails(firstDemand.getType(), mergeDemandCreateDTO);
    }

    private void setMergeDemandWkt(List<FlightDemandDTO> originDemandList, FlightDemandDTO mergeDemand) {
        List<FlightDemandAreaDTO> areaList = originDemandList.stream().map(FlightDemandDTO::getAreaList).flatMap(List::stream).collect(Collectors.toList());
        setMultiAreaDemandWkt(areaList, mergeDemand);
    }

    private void setMultiAreaDemandWkt(List<FlightDemandAreaDTO> originAreaList, FlightDemandDTO demandDTO) {
        if (CollectionUtils.isEmpty(originAreaList)) {
            return;
        }

        // 获取所有原始需求的区域坐标和相关信息
        List<Geometry> geometryList = new ArrayList<>();
        List<Double> areaList = new ArrayList<>();
        List<PointCoordinate> pointList = new ArrayList<>();

        originAreaList.forEach(areaDO -> {
            Geometry geometry = WktUtil.toGeometry(areaDO.getAreaCoordinate());
            geometryList.add(geometry);
            areaList.add(areaDO.getArea());
            pointList.add(new PointCoordinate(Double.valueOf(areaDO.getCenterPointLongitude()), Double.valueOf(areaDO.getCenterPointLatitude())));
        });

        if (CollectionUtils.isEmpty(geometryList)) {
            return;
        }

        try {
            // 创建MultiPolygon
            GeometryFactory factory = new GeometryFactory();
            GeometryCollection multiGeometry = factory.createGeometryCollection(geometryList.toArray(new Geometry[0]));

            // 设置合并后的WKT字符串
            demandDTO.setAreaCoordinate(multiGeometry.toString());

            // 计算中心点：多中心点间取平均
            PointCoordinate pointCoordinate = GISUtil.calculateSphericalCenter(pointList);
            demandDTO.setCenterPointLongitude(String.valueOf(pointCoordinate.getLongitude()));
            demandDTO.setCenterPointLatitude(String.valueOf(pointCoordinate.getLatitude()));

            // 计算面积：按原来区域的相加
            double totalArea = areaList.stream().mapToDouble(Double::doubleValue).sum();
            demandDTO.setArea(totalArea);

        } catch (Exception e) {
            log.error("多区域合并WKT处理失败", e);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "多区域合并WKT处理失败");
        }
    }

    /**
     * 合并建议列表
     *
     * @param queryDTO 查询条件
     * @return
     */
    @Override
    public MergeHandleGroupAdvice queryMergeHandleGroups(MergeHandleQueryDTO queryDTO) {
        MergeHandleGroupAdvice advice = new MergeHandleGroupAdvice();
        Page<Object> page = PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 从合并处理记录表查询待合并的记录，按reduce_flight_count倒序排列
        List<FlightDemandMergeHandleDO> mergeHandleRecords = flightDemandMergeHandleRepository.list(
                Wrappers.lambdaQuery(FlightDemandMergeHandleDO.class)
                        .eq(FlightDemandMergeHandleDO::getMergeStatus, MergeStatusEnum.PENDING.getCode())
                        .orderByDesc(FlightDemandMergeHandleDO::getReduceFlightCount)
                        .orderByDesc(FlightDemandMergeHandleDO::getHandleTime)
                        .orderByDesc(FlightDemandMergeHandleDO::getId)
        );

        if (CollectionUtils.isEmpty(mergeHandleRecords)) {
            CommonPage commonPage = CommonPage.buildEmptyPage();
            advice.setCommonPage(commonPage);
            advice.setOriginDemandTotalNum(0L);
            return advice;
        }

        List<MergeHandleGroupDTO> groupList = new ArrayList<>();
        for (FlightDemandMergeHandleDO handleRecord : mergeHandleRecords) {
            String mergeHandleCode = handleRecord.getMergeHandleCode();

            // 查询该合并处理编号下的原始需求关系记录（target_type = merge）
            List<FlightMergeDemandRelationDO> mergeRelations = flightMergeDemandRelationRepository.list(
                    Wrappers.lambdaQuery(FlightMergeDemandRelationDO.class)
                            .eq(FlightMergeDemandRelationDO::getMergeHandleCode, mergeHandleCode)
                            .eq(FlightMergeDemandRelationDO::getTargetType, FlightMergeDemandTargetTypeEnum.MERGE.getCode())
            );

            if (CollectionUtils.isEmpty(mergeRelations)) {
                continue;
            }

            MergeHandleGroupDTO groupDTO = new MergeHandleGroupDTO();
            groupDTO.setMergeHandleCode(mergeHandleCode);

            // 获取原始需求信息并按起飞时间排序
            List<String> originDemandCodes = mergeRelations.stream()
                    .map(FlightMergeDemandRelationDO::getOriginalDemandCode)
                    .collect(Collectors.toList());

            List<FlightMergeDemandListVO> originDemandList = buildDemandListVO(originDemandCodes);
            groupDTO.setOriginDemandList(originDemandList);
            groupList.add(groupDTO);
        }

        CommonPage<MergeHandleGroupDTO> commonPage = CommonPage.buildPage(queryDTO.getPageNum(), queryDTO.getPageSize(), page.getPages(), page.getTotal(), groupList);
        advice.setCommonPage(commonPage);
        long originDemandTotalNum = flightDemandRepository.count(Wrappers.lambdaQuery(FlightDemandDO.class)
                .eq(FlightDemandDO::getIsMergeDemand, false)
                .eq(FlightDemandDO::getMergeStatus, MergeStatusEnum.PENDING.getCode())
        );
        advice.setOriginDemandTotalNum(originDemandTotalNum);
        return advice;
    }

    private List<FlightMergeDemandListVO> buildDemandListVO(List<String> demandCodeList) {
        if (CollectionUtil.isEmpty(demandCodeList)) {
            return new ArrayList<>();
        }
        List<FlightDemandDO> originDemands = flightDemandRepository.list(
                Wrappers.lambdaQuery(FlightDemandDO.class)
                        .in(FlightDemandDO::getDemandNo, demandCodeList)
        );

        List<FlightMergeDemandListVO> result = new ArrayList<>();
        for (FlightDemandDO demandDO : originDemands) {
            FlightMergeDemandListVO vo = new FlightMergeDemandListVO();
            vo.setId(demandDO.getId());
            vo.setDemandNo(demandDO.getDemandNo());
            vo.setName(demandDO.getName());
            vo.setType(FlightDemandTypeEnum.valueOf(demandDO.getType()));
            vo.setPublisherNo(demandDO.getPublisherNo());
            vo.setPublisherName(demandDO.getPublisherName());
            vo.setPublishTime(demandDO.getPublishTime());

            //设置周期时间
            setDemandTime(demandDO, vo);

            result.add(vo);
        }

        // 按周期开始时间降序排序（非空的在前）
        result.sort((a, b) -> {
            int sort = sortByStartDate(a, b);
            if (sort == 0) {
                return Math.toIntExact(b.getId() - a.getId());
            } else {
                return sort;
            }
        });

        return result;
    }

    private int sortByStartDate(FlightMergeDemandListVO a, FlightMergeDemandListVO b) {
        if (a.getStartDate() == null && b.getStartDate() == null) return 0;
        if (a.getStartDate() == null) return 1;
        if (b.getStartDate() == null) return -1;
        return b.getStartDate().compareTo(a.getStartDate());
    }

    /**
     * 从需求中提取起飞时间
     * 合并需求也会在详情表加上这些字段
     */
    private void setDemandTime(FlightDemandDO demandDO, FlightMergeDemandListVO mergeDemandListVO) {
        try {
            FlightDemandDTO flightDemandDTO = new FlightDemandDTO();
            flightDemandDTO.setDemandNo(demandDO.getDemandNo());
            flightDemandDTO.setType(FlightDemandTypeEnum.valueOf(demandDO.getType()));
            flightDemandDTO.setIsMergeDemand(demandDO.getIsMergeDemand());
            readFlightDemandTypeDetail(flightDemandDTO);
            readFieldFromProp(flightDemandDTO);
            if (flightDemandDTO.getType() == FlightDemandTypeEnum.ROUTINE_INSPECTION) {
                RoutineInspectionFlightDemandDTO routineDetail = flightDemandDTO.getRoutineInspectionDetail();
                if (routineDetail != null) {
                    //原始需求立即起飞时是没有时间的，合并需求的肯定会有值
                    mergeDemandListVO.setStartDate(routineDetail.getDemandStartTime());
                    mergeDemandListVO.setEndDate(routineDetail.getDemandEndTime());
                    mergeDemandListVO.setEarliestStartTime(routineDetail.getFlyingStartTime());
                    mergeDemandListVO.setLatestEndTime(routineDetail.getFlyingEndTime());
                }
            } else {
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "需求类型不支持合并");
            }
        } catch (Exception e) {
            log.error("提取起飞时间失败，需求编号: {}", demandDO.getDemandNo(), e);
        }
    }

    @Override
    public MergeHandleDetailDTO getMergeHandleDetail(String mergeHandleCode) {
        if (!org.springframework.util.StringUtils.hasText(mergeHandleCode)) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "合并处理编号不能为空");
        }

        // 查询该mergeHandleCode下的所有需求
        List<FlightDemandDO> demands = flightDemandRepository.list(
                Wrappers.lambdaQuery(FlightDemandDO.class)
                        .eq(FlightDemandDO::getMergeHandleCode, mergeHandleCode)
                        .orderByDesc(FlightDemandDO::getPublishTime)
        );

        if (CollectionUtils.isEmpty(demands)) {
            throw new BizException(BizErrorCode.DEMAND_NOT_FOUND.getCode(), "未找到相关需求");
        }

        MergeHandleDetailDTO detailDTO = new MergeHandleDetailDTO();
        detailDTO.setMergeHandleCode(mergeHandleCode);

        // 分离原始需求和合并需求
        List<String> originDemandCodeList = demands.stream()
                .filter(d -> Boolean.FALSE.equals(d.getIsMergeDemand()))
                .map(FlightDemandDO::getDemandNo)
                .collect(Collectors.toList());

        List<String> mergeDemandCodeList = demands.stream()
                .filter(d -> Boolean.TRUE.equals(d.getIsMergeDemand()))
                .map(FlightDemandDO::getDemandNo)
                .collect(Collectors.toList());

        //转换数据格式
        List<FlightMergeDemandListVO> originDemands = buildDemandListVO(originDemandCodeList);
        List<FlightMergeDemandListVO> mergeDemands = buildDemandListVO(mergeDemandCodeList);

        detailDTO.setOriginDemands(originDemands);
        detailDTO.setMergeDemands(mergeDemands);

        // 设置状态和创建时间
        FlightDemandMergeHandleDO demandMergeHandleDO = flightDemandMergeHandleRepository.getOne(
                Wrappers.lambdaQuery(FlightDemandMergeHandleDO.class)
                        .eq(FlightDemandMergeHandleDO::getMergeHandleCode, mergeHandleCode)
        );
        detailDTO.setMergeStatus(MergeStatusEnum.valueOf(demandMergeHandleDO.getMergeStatus()));
        detailDTO.setMergeEffect(JsonUtil.parseJson(demandMergeHandleDO.getMergeEffectJson(), MergeEffect.class));
        detailDTO.setHandleTime(demandMergeHandleDO.getHandleTime());
        detailDTO.setCreateTime(demandMergeHandleDO.getGmtCreated());

        return detailDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String executeMerge(ExecuteMergeDTO executeMergeDTO) {
        String mergeHandleCode = executeMergeDTO.getMergeHandleCode();
        // 1. 验证相关原需求是否已分配服务商
        List<FlightMergeDemandRelationDO> pendingMergeRelations = flightMergeDemandRelationRepository.list(
                Wrappers.lambdaQuery(FlightMergeDemandRelationDO.class)
                        .eq(FlightMergeDemandRelationDO::getMergeHandleCode, mergeHandleCode)
                        .eq(FlightMergeDemandRelationDO::getTargetType, FlightMergeDemandTargetTypeEnum.MERGE.getCode())
                        .eq(FlightMergeDemandRelationDO::getMergeStatus, MergeStatusEnum.PENDING.getCode())
        );

        List<String> originDemandCodes = pendingMergeRelations.stream()
                .map(FlightMergeDemandRelationDO::getOriginalDemandCode)
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(originDemandCodes)) {
            throw new BizException(BizErrorCode.DEMAND_NOT_FOUND.getCode(), "未找到关联的原始需求");
        }

        //检查需求是否已经被修改
        List<FlightDemandDO> originDemandList = flightDemandRepository.list(Wrappers.lambdaQuery(FlightDemandDO.class)
                .in(FlightDemandDO::getDemandNo, originDemandCodes));
        boolean hasModifyDemand = originDemandList.stream().anyMatch(demandDO -> Objects.equals(demandDO.getIsLatest(), false));
        if (hasModifyDemand) {
            return cancelMergeAndThrow(executeMergeDTO, mergeHandleCode, "因部分原始需求已被修改，合并失败");
        }

        // 检查原始需求是否已分配服务商
        List<FlightDemandMatchServiceProviderDO> assignedProviders = flightDemandMatchServiceProviderRepository.list(
                Wrappers.lambdaQuery(FlightDemandMatchServiceProviderDO.class)
                        .in(FlightDemandMatchServiceProviderDO::getDemandCode, originDemandCodes)
                        .eq(FlightDemandMatchServiceProviderDO::getIsMergeDemand, Boolean.FALSE)
        );

        if (CollectionUtil.isNotEmpty(assignedProviders)) {
            Set<String> assignedDemandCodes = assignedProviders.stream()
                    .map(FlightDemandMatchServiceProviderDO::getDemandCode)
                    .collect(Collectors.toSet());
            String errorMsg = String.format("因部分原始需求已分配到服务商，合并失败。已分配需求: %s", assignedDemandCodes);
            return cancelMergeAndThrow(executeMergeDTO, mergeHandleCode, errorMsg);
        }

        //验证原始需求中有没有最早起飞时间于当前比较小于72小时
        List<FlightMergeDemandRelationDO> relationList = flightMergeDemandRelationRepository.list(Wrappers.lambdaQuery(FlightMergeDemandRelationDO.class)
                .eq(FlightMergeDemandRelationDO::getMergeHandleCode, mergeHandleCode)
        );
        List<String> originDemandCodeList = relationList.stream()
                .map(FlightMergeDemandRelationDO::getOriginalDemandCode)
                .collect(Collectors.toList());
        for (String originDemandCode : originDemandCodeList) {
            FlightDemandDTO flightDemandDTO = getFlightDemandByNo(originDemandCode);

            if (flightDemandDTO.getType() != FlightDemandTypeEnum.ROUTINE_INSPECTION) {
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "需求类型不支持合并");
            }

            boolean isValid = TimeFrequencyUtil.moreThanHourBeforeTime(flightDemandDTO.getRoutineInspectionDetail().getDemandStartTime(), 72);
            if (!isValid) {
                String errorMsg = String.format("原始需求：%s 的最早开始时间距离目前已不足72小时，无法执行合并需求操作，请单独分配服务商", originDemandCode);
                return cancelMergeAndThrow(executeMergeDTO, mergeHandleCode, errorMsg);
            }
        }

        boolean canExec = demandLockHelper.tryLockExecMergeHandle(mergeHandleCode);
        if (!canExec) {
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "其他人正在执行这个合并需求操作，请勿重复执行");
        }
        try {
            // 2. 更新合并处理记录状态
            boolean handleSuccess = flightDemandMergeHandleRepository.update(
                    Wrappers.lambdaUpdate(FlightDemandMergeHandleDO.class)
                            .set(FlightDemandMergeHandleDO::getMergeStatus, MergeStatusEnum.MERGED.getCode())
                            .set(FlightDemandMergeHandleDO::getHandleTime, LocalDateTime.now())
                            .set(FlightDemandMergeHandleDO::getOperatorId, executeMergeDTO.getOperatorUserNo())
                            .set(FlightDemandMergeHandleDO::getOperatorName, executeMergeDTO.getOperatorUserName())
                            .set(FlightDemandMergeHandleDO::getGmtModified, LocalDateTime.now())
                            .eq(FlightDemandMergeHandleDO::getMergeHandleCode, mergeHandleCode)
                            .eq(FlightDemandMergeHandleDO::getMergeStatus, MergeStatusEnum.PENDING.getCode())
            );

            if (!handleSuccess) {
                log.error("更新合并处理记录状态失败，合并处理编号: {}", mergeHandleCode);
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "更新合并处理记录状态失败");
            }

            // 3. 批量更新所有相关需求的状态为已合并
            flightDemandRepository.update(
                    Wrappers.lambdaUpdate(FlightDemandDO.class)
                            .set(FlightDemandDO::getMergeStatus, MergeStatusEnum.MERGED.getCode())
                            .set(FlightDemandDO::getPublishTime, LocalDateTime.now())
                            .set(FlightDemandDO::getGmtModified, LocalDateTime.now())
                            .eq(FlightDemandDO::getMergeHandleCode, mergeHandleCode)
                            .eq(FlightDemandDO::getMergeStatus, MergeStatusEnum.PENDING.getCode())
                            .eq(FlightDemandDO::getIsMergeDemand, true)
            );

            flightDemandRepository.update(
                    Wrappers.lambdaUpdate(FlightDemandDO.class)
                            .set(FlightDemandDO::getMergeStatus, MergeStatusEnum.MERGED.getCode())
                            .set(FlightDemandDO::getGmtModified, LocalDateTime.now())
                            .eq(FlightDemandDO::getMergeHandleCode, mergeHandleCode)
                            .eq(FlightDemandDO::getMergeStatus, MergeStatusEnum.PENDING.getCode())
                            .eq(FlightDemandDO::getIsMergeDemand, false)
            );

            // 4. 批量更新关联关系表的状态为已合并
            boolean relationSuccess = flightMergeDemandRelationRepository.update(
                    Wrappers.lambdaUpdate(FlightMergeDemandRelationDO.class)
                            .set(FlightMergeDemandRelationDO::getMergeStatus, MergeStatusEnum.MERGED.getCode())
                            .set(FlightMergeDemandRelationDO::getGmtModified, LocalDateTime.now())
                            .eq(FlightMergeDemandRelationDO::getMergeHandleCode, mergeHandleCode)
                            .eq(FlightMergeDemandRelationDO::getMergeStatus, MergeStatusEnum.PENDING.getCode())
            );

            if (!relationSuccess) {
                log.error("批量更新关联关系状态失败，合并处理编号: {}", mergeHandleCode);
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "批量更新关联关系状态失败");
            }

            //删除自关联记录
            flightMergeDemandRelationRepository.update(Wrappers.lambdaUpdate(FlightMergeDemandRelationDO.class)
                    .set(FlightMergeDemandRelationDO::getIsDeleted, true)
                    .set(FlightMergeDemandRelationDO::getGmtModified, LocalDateTime.now())
                    .eq(FlightMergeDemandRelationDO::getTargetType, FlightMergeDemandTargetTypeEnum.SELF.getCode())
                    .in(FlightMergeDemandRelationDO::getTargetDemandCode, originDemandCodes)
            );

            log.info("执行合并操作成功，合并处理编号: {}", mergeHandleCode);

            return mergeHandleCode;
        } finally {
            demandLockHelper.unlockExecMergeHandle(mergeHandleCode);
        }
    }

    private String cancelMergeAndThrow(ExecuteMergeDTO executeMergeDTO, String mergeHandleCode, String errorMsg) {
        FlightDemandService selfBean = beanFactory.getBean(FlightDemandService.class);
        log.error(errorMsg);
        CancelMergeDTO cancelMergeDTO = new CancelMergeDTO();
        cancelMergeDTO.setMergeHandleCode(mergeHandleCode);
        cancelMergeDTO.setOperatorUserNo(executeMergeDTO.getOperatorUserNo());
        cancelMergeDTO.setOperatorUserName(executeMergeDTO.getOperatorUserName());
        selfBean.cancelMerge(cancelMergeDTO);
        throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), errorMsg);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Boolean cancelMerge(CancelMergeDTO cancelMergeDTO) {
        String mergeHandleCode = cancelMergeDTO.getMergeHandleCode();

        boolean canExec = demandLockHelper.tryLockExecMergeHandle(mergeHandleCode);
        if (!canExec) {
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "其他人正在执行这个合并需求操作，请勿重复执行");
        }

        try {
            // 1. 更新合并处理记录状态
            boolean handleSuccess = flightDemandMergeHandleRepository.update(
                    Wrappers.lambdaUpdate(FlightDemandMergeHandleDO.class)
                            .set(FlightDemandMergeHandleDO::getMergeStatus, MergeStatusEnum.CANCELLED.getCode())
                            .set(FlightDemandMergeHandleDO::getHandleTime, LocalDateTime.now())
                            .set(FlightDemandMergeHandleDO::getOperatorId, cancelMergeDTO.getOperatorUserNo())
                            .set(FlightDemandMergeHandleDO::getOperatorName, cancelMergeDTO.getOperatorUserName())
                            .set(FlightDemandMergeHandleDO::getGmtModified, LocalDateTime.now())
                            .eq(FlightDemandMergeHandleDO::getMergeHandleCode, mergeHandleCode)
                            .eq(FlightDemandMergeHandleDO::getMergeStatus, MergeStatusEnum.PENDING.getCode())
            );

            if (!handleSuccess) {
                log.error("更新合并处理记录状态失败，合并处理编号: {}", mergeHandleCode);
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "更新合并处理记录状态失败");
            }

            // 2. 批量更新所有相关需求的状态为取消合并
            boolean success = flightDemandRepository.update(
                    Wrappers.lambdaUpdate(FlightDemandDO.class)
                            .set(FlightDemandDO::getMergeStatus, MergeStatusEnum.CANCELLED.getCode())
                            .set(FlightDemandDO::getGmtModified, LocalDateTime.now())
                            .eq(FlightDemandDO::getMergeHandleCode, mergeHandleCode)
                            .eq(FlightDemandDO::getMergeStatus, MergeStatusEnum.PENDING.getCode())
            );

            if (!success) {
                log.error("批量更新需求状态为取消合并失败，合并处理编号: {}", mergeHandleCode);
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "批量更新需求状态失败");
            }

            // 3. 批量更新关联关系表的状态为取消合并
            boolean relationSuccess = flightMergeDemandRelationRepository.update(
                    Wrappers.lambdaUpdate(FlightMergeDemandRelationDO.class)
                            .set(FlightMergeDemandRelationDO::getIsDeleted, Boolean.TRUE)
                            .set(FlightMergeDemandRelationDO::getMergeStatus, MergeStatusEnum.CANCELLED.getCode())
                            .set(FlightMergeDemandRelationDO::getGmtModified, LocalDateTime.now())
                            .eq(FlightMergeDemandRelationDO::getMergeHandleCode, mergeHandleCode)
                            .eq(FlightMergeDemandRelationDO::getMergeStatus, MergeStatusEnum.PENDING.getCode())
            );

            if (!relationSuccess) {
                log.error("批量更新关联关系状态为取消合并失败，合并处理编号: {}", mergeHandleCode);
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "批量更新关联关系状态失败");
            }

            // 注意：不需要重新创建self记录，因为在创建合并需求时self记录并未被删除，仍然存在

            log.info("取消合并操作成功，合并处理编号: {}", mergeHandleCode);

            return Boolean.TRUE;
        } finally {
            demandLockHelper.unlockExecMergeHandle(mergeHandleCode);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void assignServiceProvider(FlightDemandAssignDTO dto) {
        String demandCode = dto.getDemandCode();
        FlightDemandDTO flightDemandDTO = getFlightDemandByNo(demandCode);
        //匹配服务商的需求必须是已通过审核的
        if (Objects.equals(flightDemandDTO.getNeedApprove(), true) && !Objects.equals(flightDemandDTO.getApproveStatus(), ApproveStatusEnum.APPROVED.name())) {
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "该需求还没有审核通过，无法分配服务商");
        }

        if (!flightDemandDTO.getIsLatest()) {
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "该需求已经被修改过了，请重新分配");
        }

        //校验分配的服务商是否已通过审核
        List<UserApprovalStatusDTO> userApprovalStatus = userClient.getUserApprovalStatus(ListUtil.toList(dto.getServiceProviderNo()));
        if (CollectionUtil.isEmpty(userApprovalStatus)) {
            throw new BizException(BizErrorCode.NOT_FOUND.getCode(), "未找到服务商信息");
        }
        if (!Objects.equals(ApprovalStatusEnum.getByCode(userApprovalStatus.get(0).getApprovalStatus()), ApprovalStatusEnum.APPROVED)) {
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "服务商未通过审核");
        }

        // 获取服务商信息
        List<UserDetailDTO> userDetailList = userClient.getUserDetailListWithoutTenantId(ListUtil.toList(dto.getServiceProviderNo()));
        if (CollectionUtil.isEmpty(userDetailList)) {
            throw new BizException(BizErrorCode.NOT_FOUND.getCode(), "未找到服务商信息");
        }
        UserDetailDTO supplierData = userDetailList.get(0);
        // 保存服务商匹配信息
        FlightDemandMatchServiceProviderDO matchDO = new FlightDemandMatchServiceProviderDO();
        matchDO.setTenantId(dto.getTenantId());
        matchDO.setDemandCode(demandCode);
        matchDO.setServiceProviderNo(supplierData.getUserNo());
        matchDO.setServiceProviderName(supplierData.getUserName());
        matchDO.setCompanyName(supplierData.getSupplierDetailDTO().getCompanyName());
        matchDO.setAssignWay(dto.getAssignWay().name());
        matchDO.setAssignOperatorUserNo(dto.getAssignOperatorUserNo());
        matchDO.setAssignTime(LocalDateTime.now());
        matchDO.setSyncStatus(FlightDemandSyncStatusEnum.UN_SYNCED.name());
        matchDO.setIsMergeDemand(flightDemandDTO.getIsMergeDemand());
        //查询flight_merge_demand_relation表，只要是合并需求都有周期时间
        if (flightDemandDTO.getIsMergeDemand()) {
            if (Objects.equals(flightDemandDTO.getMergeStatus(), MergeStatusEnum.MERGED.getCode())) {
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "合并需求必须完成合并后才能分配服务商");
            }
            DemandMergeScheduleDTO mergeScheduleDTO = flightDemandDTO.getMergeDemandProp().getMergeScheduleDTO();
            matchDO.setMergeScheduleTime(JsonUtil.toJsonStr(mergeScheduleDTO));
        } else {
            //如果是原始需求，则周期时间就是他本身的时间，可以为空
            if (Objects.equals(MergeStatusEnum.MERGED.getCode(), flightDemandDTO.getMergeStatus())) {
                //需要判断是否已经被合并，如果被合并了就不允许再分配
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "原始需求已经被合并，不能单独分配，请联系运营人员");
            }
        }
        boolean canExec = demandLockHelper.tryLockAssignDemand(demandCode);
        if (!canExec) {
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "其他人正在操作，请稍后再试");
        }
        try {
            boolean success = flightDemandMatchServiceProviderRepository.save(matchDO);
            if (!success) {
                log.error("保存服务商匹配信息失败: {}", demandCode);
                throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), BizErrorCode.OPERATION_FAILED.getDesc());
            }

            // 更新需求表的匹配状态
            updateDemandMatchStatus(demandCode, true);
            log.info("assign service provider success: {}", dto);
        } catch (DuplicateKeyException e) {
            //如果数据库已经存在一条分配记录，表示已经分配过了
            log.error("该需求已分配过服务商", e);
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "该需求已分配过服务商");
        } finally {
            demandLockHelper.unlockAssignDemand(demandCode);
        }
    }

    @Override
    public Boolean checkMatchServiceProvider(String demandCode, String serviceProviderNo) {
        FlightDemandDTO flightDemandByNo = this.getFlightDemandByNo(demandCode);
        List<String> matchServiceProviderList = serviceProviderClient.getMatchServiceProviderList(flightDemandByNo);
        return CollectionUtil.isNotEmpty(matchServiceProviderList) && matchServiceProviderList.contains(serviceProviderNo);
    }

    /**
     * 更新需求匹配状态
     *
     * @param demandCode     需求编号
     * @param isMatchSuccess 是否分配成功，true表示分配成功，false表示分配失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDemandMatchStatus(String demandCode, boolean isMatchSuccess) {
        FlightDemandDO flightDemandDO = queryDemandDOByDemandCode(demandCode);
        if (flightDemandDO.getIsMergeDemand()) {
            // 如果是合并需求，更新状态的同时，还需要同步更新关联的原始需求的分配状态，检查关联的合并需求是否都已分配
            updateMergeDemandMatchStatus(demandCode, isMatchSuccess);
        } else {
            // 如果是原始需求，根据分配结果直接更新状态
            if (isMatchSuccess) {
                flightDemandDO.setMatchStatus(FlightDemandMatchStatusEnum.MATCHED.name());
            } else {
                flightDemandDO.setMatchStatus(FlightDemandMatchStatusEnum.MATCH_FAILED.name());
            }
            flightDemandRepository.updateById(flightDemandDO);
        }
    }

    /**
     * 更新合并需求匹配状态
     *
     * @param mergeDemandCode 合并需求编号
     * @param isMatchSuccess  是否分配成功，true表示分配成功，false表示分配失败
     */
    private void updateMergeDemandMatchStatus(String mergeDemandCode, boolean isMatchSuccess) {
        //更新合并需求本身的分配状态
        String matchStatus = isMatchSuccess ? FlightDemandMatchStatusEnum.MATCHED.name() : FlightDemandMatchStatusEnum.MATCH_FAILED.name();
        flightDemandRepository.update(Wrappers.lambdaUpdate(FlightDemandDO.class)
                .set(FlightDemandDO::getMatchStatus, matchStatus)
                .set(FlightDemandDO::getGmtModified, LocalDateTime.now())
                .eq(FlightDemandDO::getDemandNo, mergeDemandCode)
        );

        List<FlightMergeDemandRelationDO> relationList = flightMergeDemandRelationRepository.list(
                Wrappers.lambdaQuery(FlightMergeDemandRelationDO.class)
                        .eq(FlightMergeDemandRelationDO::getTargetDemandCode, mergeDemandCode)
                        .eq(FlightMergeDemandRelationDO::getTargetType, FlightMergeDemandTargetTypeEnum.MERGE.getCode())
                        .eq(FlightMergeDemandRelationDO::getMergeStatus, MergeStatusEnum.MERGED.getCode())
        );

        //有合并需求肯定会有原始需求
        relationList.forEach(relation -> {
            updateOriginDemandMatchStatus(relation.getOriginalDemandCode(), isMatchSuccess);
        });
    }

    /**
     * 更新原始需求匹配状态
     *
     * @param originDemandCode 原始需求编号
     * @param isMatchSuccess   是否分配成功，true表示分配成功，false表示分配失败
     */
    private void updateOriginDemandMatchStatus(String originDemandCode, boolean isMatchSuccess) {
        // 查询该原始需求关联的所有合并需求（通过relation表查询）
        List<FlightMergeDemandRelationDO> relations = flightMergeDemandRelationRepository.list(
                Wrappers.lambdaQuery(FlightMergeDemandRelationDO.class)
                        .eq(FlightMergeDemandRelationDO::getOriginalDemandCode, originDemandCode)
                        .eq(FlightMergeDemandRelationDO::getTargetType, FlightMergeDemandTargetTypeEnum.MERGE.getCode())
                        .eq(FlightMergeDemandRelationDO::getMergeStatus, MergeStatusEnum.MERGED.getCode())
        );

        if (CollectionUtils.isEmpty(relations)) {
            // 没有关联的合并需求，就说明未参与合并，直接根据分配结果更新状态
            String matchStatus = isMatchSuccess ? FlightDemandMatchStatusEnum.MATCHED.name() : FlightDemandMatchStatusEnum.MATCH_FAILED.name();
            flightDemandRepository.update(
                    Wrappers.lambdaUpdate(FlightDemandDO.class)
                            .set(FlightDemandDO::getMatchStatus, matchStatus)
                            .set(FlightDemandDO::getGmtModified, LocalDateTime.now())
                            .eq(FlightDemandDO::getDemandNo, originDemandCode)
            );
            return;
        }

        // 检查所有关联的合并需求的分配状态
        List<String> mergeDemandCodes = relations.stream()
                .map(FlightMergeDemandRelationDO::getTargetDemandCode)
                .collect(Collectors.toList());

        // 查询所有合并需求的当前状态
        List<FlightDemandDO> mergeDemands = flightDemandRepository.list(
                Wrappers.lambdaQuery(FlightDemandDO.class)
                        .in(FlightDemandDO::getDemandNo, mergeDemandCodes)
        );

        // 统计各种状态的合并需求数量
        int totalCount = mergeDemands.size();
        int matchedCount = 0;
        int failedCount = 0;
        int unmatchedCount = 0;

        for (FlightDemandDO mergeDemand : mergeDemands) {
            String status = mergeDemand.getMatchStatus();
            if (Objects.equals(FlightDemandMatchStatusEnum.MATCHED.name(), status)) {
                matchedCount++;
            } else if (Objects.equals(FlightDemandMatchStatusEnum.MATCH_FAILED.name(), status)) {
                failedCount++;
            } else if (Objects.equals(FlightDemandMatchStatusEnum.UNMATCHED.name(), status)) {
                unmatchedCount++;
            }
        }

        // 根据统计结果决定原始需求的状态
        FlightDemandMatchStatusEnum newStatus;
        if (matchedCount == totalCount) {
            // 全部分配成功
            newStatus = FlightDemandMatchStatusEnum.MATCHED;
        } else if (failedCount == totalCount) {
            // 全部分配失败
            newStatus = FlightDemandMatchStatusEnum.MATCH_FAILED;
        } else if (matchedCount > 0) {
            // 部分分配成功
            newStatus = FlightDemandMatchStatusEnum.PARTIAL_MATCHED;
        } else {
            // 全部未分配
            newStatus = FlightDemandMatchStatusEnum.UNMATCHED;
        }

        flightDemandRepository.update(
                Wrappers.lambdaUpdate(FlightDemandDO.class)
                        .set(FlightDemandDO::getMatchStatus, newStatus.name())
                        .set(FlightDemandDO::getGmtModified, LocalDateTime.now())
                        .eq(FlightDemandDO::getDemandNo, originDemandCode)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void syncDemandToProvider(String demandCode, String providerNo) {
        FlightDemandDTO demandDTO = this.getFlightDemandByNo(demandCode);
        try {
            //同步需求给服务商
            String syncOrderNo = serviceProviderClient.syncDemandToProvider(demandDTO, providerNo);

            //更新同步数据到库
            flightDemandMatchServiceProviderRepository.update(Wrappers.lambdaUpdate(FlightDemandMatchServiceProviderDO.class)
                    .set(FlightDemandMatchServiceProviderDO::getSyncOrderNo, syncOrderNo)
                    .set(FlightDemandMatchServiceProviderDO::getSyncStatus, FlightDemandSyncStatusEnum.SYNCED)
                    .set(FlightDemandMatchServiceProviderDO::getGmtModified, LocalDateTime.now())
                    .eq(FlightDemandMatchServiceProviderDO::getDemandCode, demandDTO.getDemandNo())
            );

            log.info("Successfully processed demand {}, assigned to provider {}, sync order: {}",
                    demandDTO.getDemandNo(), providerNo, syncOrderNo);
        } catch (Exception e) {
            log.error("Failed to sync demand to provider: {}", providerNo, e);

            //更新同步数据到库
            String stackTrace = ExceptionUtils.getStackTrace(e);
            flightDemandMatchServiceProviderRepository.update(Wrappers.lambdaUpdate(FlightDemandMatchServiceProviderDO.class)
                    .set(FlightDemandMatchServiceProviderDO::getSyncStatus, FlightDemandSyncStatusEnum.SYNC_FAILED)
                    .set(FlightDemandMatchServiceProviderDO::getSyncFailReason, stackTrace.substring(0, Math.min(1000, stackTrace.length())))
                    .set(FlightDemandMatchServiceProviderDO::getGmtModified, LocalDateTime.now())
                    .eq(FlightDemandMatchServiceProviderDO::getDemandCode, demandDTO.getDemandNo())
            );
        }
    }

    @Override
    public List<FlightDemandMatchServiceProviderDTO> queryMatchServiceProviderListByDemandCode(String demandCode) {
        FlightDemandDO flightDemandDO = queryDemandDOByDemandCode(demandCode);

        List<FlightDemandMatchServiceProviderDO> matchList;
        //如果是原始需求需要查看合并需求列表中的所有服务商，合并需求只会有一个服务商
        if (originAndMergedDemand(flightDemandDO)) {
            //如果是原始需求并且已经被合并了，则需要查看合并需求列表的分配服务商情况
            List<FlightMergeDemandRelationDO> relationList = flightMergeDemandRelationRepository.list(
                    Wrappers.lambdaQuery(FlightMergeDemandRelationDO.class)
                            .eq(FlightMergeDemandRelationDO::getOriginalDemandCode, demandCode)
                            .eq(FlightMergeDemandRelationDO::getTargetType, FlightMergeDemandTargetTypeEnum.MERGE.getCode())
                            .eq(FlightMergeDemandRelationDO::getMergeStatus, MergeStatusEnum.MERGED.getCode())
            );
            List<String> mergeDemandCodeList = relationList.stream().map(FlightMergeDemandRelationDO::getTargetDemandCode).collect(Collectors.toList());
            matchList = flightDemandMatchServiceProviderRepository.list(
                    Wrappers.lambdaQuery(FlightDemandMatchServiceProviderDO.class)
                            .in(FlightDemandMatchServiceProviderDO::getDemandCode, mergeDemandCodeList)
            );
        } else {
            matchList = flightDemandMatchServiceProviderRepository.list(
                    Wrappers.lambdaQuery(FlightDemandMatchServiceProviderDO.class)
                            .eq(FlightDemandMatchServiceProviderDO::getDemandCode, demandCode)
            );
        }

        return matchList.stream()
                .map(flightDemandMatchServiceProviderDO ->
                        flightDemandConvert.toFlightDemandMatchServiceProviderDTO(flightDemandMatchServiceProviderDO))
                .collect(Collectors.toList());
    }

    /**
     * 批量查询需求匹配的服务商列表
     *
     * @param demandCodes 需求编号列表
     * @return 返回Map，key为需求编号，value为该需求匹配的服务商列表
     */
    @Override
    public Map<String, List<FlightDemandMatchServiceProviderDTO>> queryMatchServiceProviderListByDemandCodes(List<String> demandCodes) {
        if (CollectionUtil.isEmpty(demandCodes)) {
            return new HashMap<>();
        }

        // 1. 批量查询所有需求信息
        List<FlightDemandDO> demandDOList = flightDemandRepository.list(
                Wrappers.lambdaQuery(FlightDemandDO.class)
                        .in(FlightDemandDO::getDemandNo, demandCodes)
        );

        if (CollectionUtils.isEmpty(demandDOList)) {
            return new HashMap<>();
        }

        // 按需求编号分组，便于后续查找
        Map<String, FlightDemandDO> demandMap = demandDOList.stream()
                .collect(Collectors.toMap(FlightDemandDO::getDemandNo, demand -> demand));

        // 2. 找出已合并的原始需求，批量查询合并关系
        List<String> mergedOriginDemandCodes = demandDOList.stream()
                .filter(demand -> originAndMergedDemand(demand))
                .map(FlightDemandDO::getDemandNo)
                .collect(Collectors.toList());

        // 查询所有合并关系
        Map<String, List<String>> originToMergeMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(mergedOriginDemandCodes)) {
            List<FlightMergeDemandRelationDO> relationList = flightMergeDemandRelationRepository.list(
                    Wrappers.lambdaQuery(FlightMergeDemandRelationDO.class)
                            .in(FlightMergeDemandRelationDO::getOriginalDemandCode, mergedOriginDemandCodes)
                            .eq(FlightMergeDemandRelationDO::getTargetType, FlightMergeDemandTargetTypeEnum.MERGE.getCode())
                            .eq(FlightMergeDemandRelationDO::getMergeStatus, MergeStatusEnum.MERGED.getCode())
            );

            // 按原始需求编号分组合并需求编号
            originToMergeMap = relationList.stream()
                    .collect(Collectors.groupingBy(
                            FlightMergeDemandRelationDO::getOriginalDemandCode,
                            Collectors.mapping(FlightMergeDemandRelationDO::getTargetDemandCode, Collectors.toList())
                    ));
        }

        // 3. 收集所有需要查询服务商匹配信息的需求编号
        Set<String> demandCodesForMatching = new HashSet<>();

        for (String demandCode : demandCodes) {
            FlightDemandDO demandDO = demandMap.get(demandCode);
            if (demandDO == null) {
                continue;
            }

            if (originAndMergedDemand(demandDO)) {
                // 已合并的原始需求，使用对应的合并需求编号
                List<String> mergeDemandCodes = originToMergeMap.get(demandCode);
                if (CollectionUtil.isNotEmpty(mergeDemandCodes)) {
                    demandCodesForMatching.addAll(mergeDemandCodes);
                }
            } else {
                // 其他情况，直接使用原需求编号
                demandCodesForMatching.add(demandCode);
            }
        }

        // 4. 批量查询所有服务商匹配信息
        List<FlightDemandMatchServiceProviderDO> allMatchList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(demandCodesForMatching)) {
            allMatchList = flightDemandMatchServiceProviderRepository.list(
                    Wrappers.lambdaQuery(FlightDemandMatchServiceProviderDO.class)
                            .in(FlightDemandMatchServiceProviderDO::getDemandCode, demandCodesForMatching)
            );
        }

        // 按需求编号分组匹配信息
        Map<String, List<FlightDemandMatchServiceProviderDO>> matchMapByDemandCode = allMatchList.stream()
                .collect(Collectors.groupingBy(FlightDemandMatchServiceProviderDO::getDemandCode));

        // 5. 组装最终结果
        Map<String, List<FlightDemandMatchServiceProviderDTO>> result = new HashMap<>();

        for (String originalDemandCode : demandCodes) {
            FlightDemandDO demandDO = demandMap.get(originalDemandCode);
            if (demandDO == null) {
                result.put(originalDemandCode, new ArrayList<>());
                continue;
            }

            List<FlightDemandMatchServiceProviderDO> matchList = new ArrayList<>();

            if (originAndMergedDemand(demandDO)) {
                // 已合并的原始需求，从合并需求的匹配信息中获取
                List<String> mergeDemandCodes = originToMergeMap.get(originalDemandCode);
                if (CollectionUtil.isNotEmpty(mergeDemandCodes)) {
                    for (String mergeDemandCode : mergeDemandCodes) {
                        List<FlightDemandMatchServiceProviderDO> mergeMatches = matchMapByDemandCode.get(mergeDemandCode);
                        if (CollectionUtil.isNotEmpty(mergeMatches)) {
                            matchList.addAll(mergeMatches);
                        }
                    }
                }
            } else {
                // 其他情况，直接从当前需求的匹配信息中获取
                List<FlightDemandMatchServiceProviderDO> directMatches = matchMapByDemandCode.get(originalDemandCode);
                if (CollectionUtil.isNotEmpty(directMatches)) {
                    matchList.addAll(directMatches);
                }
            }

            // 转换为DTO
            List<FlightDemandMatchServiceProviderDTO> matchDTOList = matchList.stream()
                    .map(flightDemandMatchServiceProviderDO ->
                            flightDemandConvert.toFlightDemandMatchServiceProviderDTO(flightDemandMatchServiceProviderDO))
                    .collect(Collectors.toList());

            result.put(originalDemandCode, matchDTOList);
        }

        return result;
    }

    private boolean originAndMergedDemand(FlightDemandDO demandDO) {
        return !demandDO.getIsMergeDemand() && Objects.equals(demandDO.getMergeStatus(), MergeStatusEnum.MERGED.getCode());
    }

    private FlightDemandDO queryDemandDOByDemandCode(String demandCode) {
        // 检查需求是否存在
        FlightDemandDO demand = flightDemandRepository.getOne(
                Wrappers.lambdaQuery(FlightDemandDO.class)
                        .eq(FlightDemandDO::getDemandNo, demandCode)
        );
        if (demand == null) {
            throw new BizException(BizErrorCode.DEMAND_NOT_FOUND.getCode(), BizErrorCode.DEMAND_NOT_FOUND.getDesc());
        }
        return demand;
    }

    /**
     * 根据需求编号查询最新版本的需求
     *
     * @param demandCode 需求编号（可以是任意版本的）
     * @return 最新版本的需求
     */
    private FlightDemandDO queryLatestDemandByAnyCode(String demandCode) {
        // 先查询指定编号的需求
        FlightDemandDO demand = queryDemandDOByDemandCode(demandCode);

        // 如果已经是最新版本，直接返回
        if (Boolean.TRUE.equals(demand.getIsLatest())) {
            return demand;
        }

        // 否则查询同组中的最新版本
        FlightDemandDO latestDemand = flightDemandRepository.getOne(
                Wrappers.lambdaQuery(FlightDemandDO.class)
                        .eq(FlightDemandDO::getGroupCode, demand.getGroupCode())
                        .eq(FlightDemandDO::getIsLatest, true)
        );

        if (latestDemand == null) {
            log.error("未找到最新版本需求，groupCode: {}", demand.getGroupCode());
            throw new BizException(BizErrorCode.DEMAND_NOT_FOUND.getCode(), "未找到最新版本需求");
        }

        return latestDemand;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void matchAndSyncDemand(FlightDemandDTO demandDTO) {
        String matchedProvider = serviceProviderClient.getBestMatchServiceProvider(demandDTO);
        if (matchedProvider == null) {
            log.warn("No service providers matched for demand: {}", demandDTO.getDemandNo());
            this.updateDemandMatchStatus(demandDTO.getDemandNo(), false);
            return;
        }

        //更新分配服务商信息
        FlightDemandAssignDTO assignDTO = new FlightDemandAssignDTO();
        assignDTO.setDemandCode(demandDTO.getDemandNo());
        assignDTO.setServiceProviderNo(matchedProvider);
        //系统自动分配的就设置为空，用户手动分配的就设置为用户编号
        assignDTO.setAssignOperatorUserNo(SYSTEM_USER_NO);
        assignDTO.setAssignWay(FlightDemandAssignServiceProviderWayEnum.SYSTEM);
        assignDTO.setTenantId(demandDTO.getTenantId());
        //分配服务商后会自动更新匹配状态
        FlightDemandService selfBean = beanFactory.getBean(FlightDemandService.class);
        selfBean.assignServiceProvider(assignDTO);

        //同步需求给服务商
        selfBean.syncDemandToProvider(demandDTO.getDemandNo(), matchedProvider);
    }

    @Override
    public void validateAllowEditDemand(String demandNo, String editUserNo) {
        if (StringUtils.isBlank(demandNo)) {
            throw new BizException(BizErrorCode.ILLEGAL_PARAMS.getCode(), "需求编号不能为空");
        }

        FlightDemandDO existDemand = flightDemandRepository.getOne(Wrappers.lambdaQuery(FlightDemandDO.class)
                .eq(FlightDemandDO::getDemandNo, demandNo));
        if (existDemand == null) {
            throw new BizException(BizErrorCode.DEMAND_NOT_FOUND.getCode(), BizErrorCode.DEMAND_NOT_FOUND.getDesc());
        }

        //判断需求是否有权限修改
        if (!Objects.equals(existDemand.getPublisherNo(), editUserNo)) {
            throw new BizException(BizErrorCode.NO_PERMISSION.getCode(), "无权限修改需求");
        }

        //合并需求不允许修改
        if (existDemand.getIsMergeDemand()) {
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "合并需求不允许修改");
        }

        //如果原始需求被合并了也不允许修改
        if (Objects.equals(existDemand.getMergeStatus(), MergeStatusEnum.MERGED.getCode())) {
            //暂时抛出这个错误，减少解释成本
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "需求已被分配，不允许修改");
        }

        //todo 如果在执飞中，编辑需求将影响后续飞行计划，需要进入审核流程，审核通过后终止需求下的所有飞行计划
        //暂时实现，如果需求已经同步就不允许修改，因为服务商暂时无法终止
        FlightDemandMatchServiceProviderDO matchServiceProvider = flightDemandMatchServiceProviderRepository.getOne(Wrappers.lambdaQuery(FlightDemandMatchServiceProviderDO.class)
                .eq(FlightDemandMatchServiceProviderDO::getDemandCode, demandNo)
        );
        if (matchServiceProvider != null) {
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "已分配服务商的需求不允许编辑");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String editDemand(FlightDemandDTO demandDTO) {
        String demandNo = demandDTO.getDemandNo();
        validateAllowEditDemand(demandNo, demandDTO.getEditorNo());

        boolean canExec = demandLockHelper.tryLockEditDemand(demandNo);
        if (!canExec) {
            throw new BizException(BizErrorCode.OPERATION_FAILED.getCode(), "其他人正在执行这个需求的编辑操作，请勿重复执行");
        }
        try {
            FlightDemandDO existDemand = flightDemandRepository.getOne(Wrappers.lambdaQuery(FlightDemandDO.class)
                    .eq(FlightDemandDO::getDemandNo, demandNo));

            //如果需求正在审核过程中的修改需要终止本次审批，然后重新创建一个审批单
            if (StrUtil.isNotBlank(existDemand.getApprovalId())) {
                ApprovalInstance existInstance = approveInstanceService.getApprovalInstance(existDemand.getApprovalId());
                ApproveStatusEnum approveStatus = existInstance.getStatus();
                if (approveStatus == ApproveStatusEnum.PENDING || approveStatus == ApproveStatusEnum.APPROVING) {
                    approveInstanceService.stopApprove(existDemand.getApprovalId());
                }
            }

            //保存新需求，将新需求的parentCode设置为原始需求的编号
            demandDTO.setParentCode(demandNo);
            //这里设置为空，保存的时候会自动生成
            demandDTO.setDemandNo(null);
            demandDTO.setApproveStatus(ApproveStatusEnum.PENDING.name());
            String newDemandNo = this.saveFlightDemand(demandDTO);

            //修改原始需求的最新标记
            flightDemandRepository.update(Wrappers.lambdaUpdate(FlightDemandDO.class)
                    .set(FlightDemandDO::getIsLatest, false)
                    .eq(FlightDemandDO::getDemandNo, demandNo));
            log.info("编辑需求成功，原需求编号: {}, 新需求编号: {}", demandNo, newDemandNo);
            return newDemandNo;
        } finally {
            demandLockHelper.unlockEditDemand(demandNo);
        }
    }

    @Override
    public List<String> findAllHistoryDemandCode(String demandCode) {
        FlightDemandDO curDemandDO = flightDemandRepository.getOne(Wrappers.lambdaQuery(FlightDemandDO.class)
                .eq(FlightDemandDO::getDemandNo, demandCode)
        );

        List<FlightDemandDO> historyVersionDemandList = flightDemandRepository.list(Wrappers.lambdaQuery(FlightDemandDO.class)
                .eq(FlightDemandDO::getGroupCode, curDemandDO.getGroupCode())
                .orderByAsc(FlightDemandDO::getVersion)
        );
        return historyVersionDemandList.stream().map(FlightDemandDO::getDemandNo).collect(Collectors.toList());
    }

    @Override
    public void approveDemand(ApprovalEntity approvalEntity) {
        //校验有效期
        String demandNo = approvalEntity.getBizId();
        if (StringUtils.isBlank(demandNo)) {
            if (StringUtils.isBlank(approvalEntity.getApprovalId())) {
                throw new BizException(ErrorCode.ILLEGAL_PARAMS.getCode(), "审批id不能为空");
            }
            FlightDemandDO demandDO = flightDemandRepository.getOne(Wrappers.lambdaQuery(FlightDemandDO.class)
                    .eq(FlightDemandDO::getApprovalId, approvalEntity.getApprovalId()));
            demandNo = demandDO.getDemandNo();
        }
        FlightDemandDTO flightDemand = getFlightDemandByNo(demandNo);
        LocalDateTime endTime;
        if (flightDemand.getFlyingEndTime() == null) {
            endTime = flightDemand.getDemandEndTime().plusDays(1).atStartOfDay();
        } else {
            endTime = LocalDateTime.of(flightDemand.getDemandEndTime(), flightDemand.getFlyingEndTime());
        }
        if (LocalDateTime.now().isAfter(endTime)) {
            throw new BizException(BizErrorCode.CYCLE_DEMAND_EXPIRED_ERROR.getCode(), BizErrorCode.CYCLE_DEMAND_EXPIRED_ERROR.getDesc());
        }

        ApproveStatusEnum approveStatusEnum = approveInstanceService.doApprove(approvalEntity);
        //更新审核单状态
        flightDemandRepository.update(Wrappers.lambdaUpdate(FlightDemandDO.class)
                .set(FlightDemandDO::getApproveStatus, approveStatusEnum.getStatusCode())
                .eq(FlightDemandDO::getApprovalId, approvalEntity.getApprovalId()));
    }

    @Override
    public void deleteDemand(String demandCode) {
        //todo 如果在执飞中，删除需求将影响后续飞行计划，不可直接删除，需要先终止需求下的所有飞行计划
    }

    @Override
    public void stopDemand(String demandCode) {
        //todo 终止所有制定的飞行计划
    }

    @Override
    public List<FlightDemandVO> getFlightDemandByNoList(List<String> demandNoList) {
        List<FlightDemandDO> list = flightDemandRepository.list(Wrappers.lambdaQuery(FlightDemandDO.class).in(FlightDemandDO::getDemandNo, demandNoList));
        List<FlightDemandDTO> flightDemandDTOS = flightDemandConvert.convertList(list);
        return flightDemandConvert.convertToVOList(flightDemandDTOS);
    }
}