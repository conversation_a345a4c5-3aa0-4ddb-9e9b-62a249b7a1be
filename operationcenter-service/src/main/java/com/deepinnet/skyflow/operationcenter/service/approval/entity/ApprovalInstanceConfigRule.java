package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Description:
 * Date: 2025/7/21
 * Author: lijunheng
 */
@Data
public class ApprovalInstanceConfigRule implements Serializable {

    /**
     * 规则编号
     */
    private String code;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则类型，例如是需求审批规则、规划审批规则、飞行次数审批规则
     */
    private String ruleType;

    /**
     * 一级组织编号
     */
    private String orgCode;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String editor;

    /**
     * 审批选择配置列表
     */
    private List<ApprovalChooseConfigRule> approvalChooseConfigRuleList;

    /**
     * 整个审批单超时配置，可以设置为自动通过，或者自动拒绝
     */
    private ApproveTimeoutConfig approveTimeoutConfig;

    /**
     * 审批组织架构，用户事先配置好各层级的审批人
     */
    private ApproveOrgUserConfig approveOrgUserConfig;

    /**
     * 租户编号
     */
    private String tenantId;
}
