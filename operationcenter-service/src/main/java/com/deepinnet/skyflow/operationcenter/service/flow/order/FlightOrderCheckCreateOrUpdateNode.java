package com.deepinnet.skyflow.operationcenter.service.flow.order;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderRepository;
import com.deepinnet.skyflow.operationcenter.dto.FlightOrderCreateDTO;
import com.deepinnet.skyflow.operationcenter.enums.OrderStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.flow.context.FlightOrderCreateContext;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.yomahub.liteflow.annotation.LiteflowCmpDefine;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import com.yomahub.liteflow.enums.NodeTypeEnum;

import javax.annotation.Resource;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@LiteflowComponent(value = "flightOrderCheckCreateOrUpdateNode", name = "创建或更新订单")
@LiteflowCmpDefine(value = NodeTypeEnum.BOOLEAN)
public class FlightOrderCheckCreateOrUpdateNode {

    @Resource
    private FlightOrderRepository flightOrderRepository;

    @LiteflowMethod(LiteFlowMethodEnum.PROCESS_BOOLEAN)
    public Boolean process(NodeComponent bindCmp) {
        FlightOrderCreateContext contextBean = bindCmp.getContextBean(FlightOrderCreateContext.class);

        FlightOrderCreateDTO flightOrderCreateDTO = contextBean.getFlightOrderCreateDTO();

        // 创建订单
        if (StrUtil.isEmpty(flightOrderCreateDTO.getOrderNo())) {
            return true;
        }

        FlightOrderDO flightOrderDO = flightOrderRepository.getOne(
                Wrappers.lambdaQuery(FlightOrderDO.class)
                        .eq(FlightOrderDO::getOrderNo, flightOrderCreateDTO.getOrderNo())
                        .eq(FlightOrderDO::getUserNo, flightOrderCreateDTO.getUserNo()));

        if (ObjectUtil.isNull(flightOrderDO)) {
            LogUtil.error("获取订单失败，订单不存在: {}, tenantId:{}, userNo:{}", flightOrderCreateDTO.getOrderNo(), flightOrderCreateDTO.getTenantId(), flightOrderCreateDTO.getUserNo());
            throw new BizException(BizErrorCode.ORDER_NOT_FOUND.getCode(), BizErrorCode.ORDER_NOT_FOUND.getDesc());
        }

        if (StrUtil.equals(flightOrderDO.getOrderType(), OrderTypeEnum.NORMAL.getCode())) {
            LogUtil.error("当前订单:{}, 订单类型:{}, 不允许修改;", flightOrderCreateDTO.getOrderNo(), flightOrderCreateDTO.getOrderType());
            throw new BizException(BizErrorCode.ORDER_TYPE_CAN_NOT_MODIFY_ERROR.getCode(), BizErrorCode.ORDER_TYPE_CAN_NOT_MODIFY_ERROR.getDesc());
        }

        contextBean.setExistOrder(flightOrderDO);

        return false;
    }
}
