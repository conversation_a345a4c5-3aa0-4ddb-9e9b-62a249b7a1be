package com.deepinnet.skyflow.operationcenter.service.approval.impl;

import com.deepinnet.infra.api.dto.SimpleDepartmentDTO;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalNode;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * Description:
 * Date: 2025/7/23
 * Author: lijunheng
 */
@Component
public class ApproveHelper {

    /**
     * 系统自动处理账号
     */
    public static final String SYSTEM = "SYSTEM";

    @Resource
    private UserRemoteClient userRemoteClient;

    /**
     * 创建审批节点
     */
    public ApprovalNode createApprovalNode(String approveUserId) {
        ApprovalNode node = new ApprovalNode();
        node.setApproveUserId(approveUserId);
        if (Objects.equals(approveUserId, SYSTEM)) {
            node.setApproveUserName("系统");
        } else {
            SimpleDepartmentDTO simpleDepartmentDTO = userRemoteClient.getUserRelatedDepartment(approveUserId);
            node.setApproveUserName(simpleDepartmentDTO.getUserName());
            node.setApproveDepartmentId(String.valueOf(simpleDepartmentDTO.getId()));
            node.setApproveDepartmentName(simpleDepartmentDTO.getName());
            node.setApproveUserPhone(simpleDepartmentDTO.getMemberPhone());
        }
        return node;
    }
}
