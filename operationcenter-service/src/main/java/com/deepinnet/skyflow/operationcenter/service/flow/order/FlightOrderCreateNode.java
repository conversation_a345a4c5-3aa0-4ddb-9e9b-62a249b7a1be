package com.deepinnet.skyflow.operationcenter.service.flow.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.dto.QueryUserRelatedDepartmentDTO;
import com.deepinnet.infra.api.dto.SimpleDepartmentDTO;
import com.deepinnet.infra.api.dto.UserInfoDTO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.*;
import com.deepinnet.skyflow.operationcenter.dal.enums.ScheduleTaskTypeEnum;
import com.deepinnet.skyflow.operationcenter.dal.repository.*;
import com.deepinnet.skyflow.operationcenter.dto.FlightOrderCreateDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightOrderFlyingInfoDTO;
import com.deepinnet.skyflow.operationcenter.enums.*;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveInstanceService;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveRuleService;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalInstance;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApproveConstants;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApproveRuleExecEntity;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApproveSubmitEntity;
import com.deepinnet.skyflow.operationcenter.service.approval.property.ApproveOrgProperty;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.skyflow.operationcenter.service.constants.ApproveBizTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.flow.context.FlightOrderCreateContext;
import com.deepinnet.skyflow.operationcenter.service.task.ScheduleTaskService;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.tenant.TenantIdUtil;
import com.google.common.collect.Lists;
import com.yomahub.liteflow.annotation.LiteflowCmpDefine;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.env.Environment;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@LiteflowComponent(value = "flightOrderCreateNode", name = "订单创建节点")
@LiteflowCmpDefine
public class FlightOrderCreateNode {

    @Resource
    private FlightOrderRepository flightOrderRepository;

    @Resource
    private FlightOrderProductUsageRepository orderProductUsageRepository;

    @Resource
    private FlightOrderFileRepository flightOrderFileRepository;

    @Resource
    private FlightOrderFlyingInfoRepository flightOrderFlyingInfoRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private ScheduleTaskService taskService;

    @Resource
    private FlightOrderApprovalRepository orderApprovalRepository;

    @Resource
    private FlightPositionRepository flightPositionRepository;

    @Resource
    private ApproveRuleService approveRuleService;

    @Resource
    private ApproveInstanceService approveInstanceService;

    @Resource
    private Environment env;

    @Resource
    private UserRemoteClient userService;

    @Resource
    private ApproveOrgProperty approveOrgProperty;

    @LiteflowMethod(LiteFlowMethodEnum.PROCESS)
    public void process(NodeComponent bindCmp) {

        FlightOrderCreateContext contextBean = bindCmp.getContextBean(FlightOrderCreateContext.class);

        FlightOrderCreateDTO flightOrderCreateDTO = contextBean.getFlightOrderCreateDTO();

        UserInfoDTO userInfo = contextBean.getUserInfo();

        String orderNo = IdUtil.getSnowflakeNextIdStr();

        flightOrderCreateDTO.setOrderNo(orderNo);

        FlightOrderDO flightOrderDO = buildOrderDO(flightOrderCreateDTO);

        List<FlightOrderProductUsageDO> orderProductUsageList = buildOrderProductUsageList(flightOrderCreateDTO, orderNo);

        List<ScheduleTaskDO> tasks;

        if (CollUtil.isNotEmpty(orderProductUsageList)) {
            List<FlightOrderProductUsageDO> mainProductType = orderProductUsageList.stream()
                    .filter(usage ->
                            StrUtil.equals(usage.getProductType(), FlightProductTypeEnum.FLIGHT_SCENARIO.getType())
                                    || StrUtil.equals(usage.getProductType(), FlightProductTypeEnum.FLIGHT_UAV.getType()))
                    .collect(Collectors.toList());

            tasks = buildScheduleTask(mainProductType, flightOrderCreateDTO);
        } else {
            tasks = buildScheduleTask(Lists.newArrayList(), flightOrderCreateDTO);
        }

        List<FlightOrderFileDO> files = buildFlightOrderFiles(flightOrderCreateDTO);

        FlightOrderFlyingInfoDO flyingInfo = buildFlyingInfo(flightOrderCreateDTO);

        FlightOrderApprovalDO flightOrderApprovalDO = buildFlightOrderApprovalDO(flightOrderCreateDTO, userInfo);

        List<FlightPositionDO> flightPositionDOS = buildFlightPositionDOList(flightOrderCreateDTO);

        try {

            transactionTemplate.executeWithoutResult(e -> {
                // 创建订单
                flightOrderRepository.save(flightOrderDO);

                // 创建订单产品用量表
                if (CollUtil.isNotEmpty(orderProductUsageList)) {
                    orderProductUsageRepository.saveBatch(orderProductUsageList);
                }

                // 审核信息
                // orderApprovalRepository.save(flightOrderApprovalDO);
                if (StrUtil.equals(OrderTypeEnum.NORMAL.getCode(), flightOrderCreateDTO.getOrderType())) {
                    orderApprovalRepository.save(flightOrderApprovalDO);
                }

                // 保存文件
                if (CollUtil.isNotEmpty(files)) {
                    flightOrderFileRepository.saveBatch(files);
                }

                // 保存飞行信息
                if (ObjectUtil.isNotNull(flyingInfo)) {
                    flightOrderFlyingInfoRepository.save(flyingInfo);
                }

                // 保存飞行点位
                if (CollUtil.isNotEmpty(flightPositionDOS)) {
                    flightPositionRepository.saveBatch(flightPositionDOS);
                }

                // 创建定时任务
                if (CollUtil.isNotEmpty(tasks)) {
                    taskService.batchInsertScheduleTasks(tasks);
                }

                // 飞行规划创建审批单
                if (StrUtil.equals(OrderTypeEnum.DEMAND_PLAN.getCode(), flightOrderCreateDTO.getOrderType())) {
                    ApprovalInstance approvalInstance = createApproval(flightOrderDO);
                    approveInstanceService.createApprove(approvalInstance);
                }

            });
        } catch (Exception e) {
            LogUtil.error("订单创建失败", e);
            throw new BizException(BizErrorCode.ORDER_CREATE_FAILED.getCode(), BizErrorCode.ORDER_CREATE_FAILED.getDesc());
        }

    }

    private ApprovalInstance createApproval(FlightOrderDO flightOrder) {
        ApproveRuleExecEntity approveRuleExecEntity = new ApproveRuleExecEntity();
        //判断发起人是否属于政数局的
        SimpleDepartmentDTO simpleDepartmentDTO = userService.getUserRelatedDepartment(flightOrder.getUserNo());
        if (StrUtil.equals(String.valueOf(simpleDepartmentDTO.getFullPath().get(0)), approveOrgProperty.getZsjOrgCode())) {
            approveRuleExecEntity.setInstanceConfigRuleCode(ApproveConstants.ORG_LEVEL);
        } else {
            approveRuleExecEntity.setInstanceConfigRuleCode(ApproveConstants.SPECIFIED_APPROVE);
        }
        ApproveSubmitEntity approveSubmitEntity = new ApproveSubmitEntity();
        approveSubmitEntity.setBizType(ApproveBizTypeEnum.FLIGHT_DEMAND_PLAN.name());
        approveSubmitEntity.setBizId(flightOrder.getOrderNo());
        approveSubmitEntity.setSubmitUserId(flightOrder.getUserNo());
        approveSubmitEntity.setCallbackParams(null);
        approveSubmitEntity.setTenantId(flightOrder.getTenantId());
        approveRuleExecEntity.setApproveSubmitEntity(approveSubmitEntity);
        return approveRuleService.executeApproveRule(approveRuleExecEntity);
    }

    private List<FlightPositionDO> buildFlightPositionDOList(FlightOrderCreateDTO flightOrderCreateDTO) {

        if (CollUtil.isEmpty(flightOrderCreateDTO.getFlightPositions())) {
            return Lists.newArrayList();
        }

        return flightOrderCreateDTO.getFlightPositions().stream().map(position -> {
            FlightPositionDO flightPositionDO = new FlightPositionDO();
            flightPositionDO.setOrderNo(flightOrderCreateDTO.getOrderNo());
            flightPositionDO.setPositionNo("POSITION_" + IdUtil.getSnowflakeNextIdStr());
            flightPositionDO.setName(position.getName());
            flightPositionDO.setCenterPoint(position.getCenterPoint());
            flightPositionDO.setType(ObjectUtil.isNull(position.getType()) ? null : position.getType().getCode());
            flightPositionDO.setFlightPosition(position.getFlightPosition());
            flightPositionDO.setGmtModified(LocalDateTime.now());
            flightPositionDO.setTenantId(flightOrderCreateDTO.getTenantId());
            return flightPositionDO;
        }).collect(Collectors.toList());
    }

    private List<ScheduleTaskDO> buildScheduleTask(List<FlightOrderProductUsageDO> mainProductType, FlightOrderCreateDTO flightOrderCreateDTO) {
        List<ScheduleTaskDO> tasks;

        if (StrUtil.equals(flightOrderCreateDTO.getOrderType(), OrderTypeEnum.NORMAL.getCode())) {
            if (CollUtil.isNotEmpty(mainProductType) && ObjectUtil.isNotNull(mainProductType.get(0).getValidityPeriodEnd())) {
                tasks = buildScheduleTask(flightOrderCreateDTO.getOrderNo(), mainProductType.get(0).getValidityPeriodEnd(), TenantIdUtil.getTenantId());
            } else {
                tasks = Lists.newArrayList();
            }
        } else {
            tasks = buildScheduleTask(flightOrderCreateDTO.getOrderNo(), flightOrderCreateDTO.getFlyingInfo().getValidityPeriodEnd(), TenantIdUtil.getTenantId());
        }

        return tasks;
    }

    private FlightOrderApprovalDO buildFlightOrderApprovalDO(FlightOrderCreateDTO flightOrderCreateDTO, UserInfoDTO userInfo) {
        FlightOrderApprovalDO flightOrderApprovalDO = new FlightOrderApprovalDO();
        flightOrderApprovalDO.setOrderNo(flightOrderCreateDTO.getOrderNo());
        flightOrderApprovalDO.setApprovalStatus(FlightOrderApproveStatusEnum.SUBMIT.getStatusCode());
        flightOrderApprovalDO.setApprovalUserNo(UserUtil.getUserNo());
        flightOrderApprovalDO.setApprovalUserName(UserUtil.getUserName());
        flightOrderApprovalDO.setApprovalTime(DateUtil.current());
        flightOrderApprovalDO.setRole(userInfo.getRoles().get(0).getRoleName());
        flightOrderApprovalDO.setPhone(userInfo.getPhone());
        flightOrderApprovalDO.setOrganizationId(ObjectUtil.isNull(userInfo.getUserDepartment()) ? null : String.valueOf(userInfo.getUserDepartment().getDepartmentId()));
        flightOrderApprovalDO.setOrganizationName(ObjectUtil.isNull(userInfo.getUserDepartment()) ? null : userInfo.getUserDepartment().getDepartmentName());
        flightOrderApprovalDO.setTenantId(TenantIdUtil.getTenantId());

        return flightOrderApprovalDO;
    }

    private FlightOrderFlyingInfoDO buildFlyingInfo(FlightOrderCreateDTO flightOrderCreateDTO) {

        if (ObjectUtil.isNull(flightOrderCreateDTO.getFlyingInfo())) {
            return null;
        }

        FlightOrderFlyingInfoDTO flyingInfoDTO = flightOrderCreateDTO.getFlyingInfo();

        FlightOrderFlyingInfoDO flyingInfo = new FlightOrderFlyingInfoDO();
        flyingInfo.setOrderNo(flightOrderCreateDTO.getOrderNo());
        flyingInfo.setFlyingFrequency(flyingInfoDTO.getFlyingFrequency());
        flyingInfo.setFlyingNum(flyingInfoDTO.getFlyingNum());
        flyingInfo.setExceptFlyingNum(flyingInfoDTO.getExceptFlyingNum());
        flyingInfo.setStartTime(flyingInfoDTO.getStartTime());
        flyingInfo.setEndTime(flyingInfoDTO.getEndTime());
        flyingInfo.setFlyingArea(flyingInfoDTO.getFlyingArea());
        flyingInfo.setRemark(flyingInfoDTO.getRemark());
        flyingInfo.setValidityPeriodStart(flyingInfoDTO.getValidityPeriodStart());
        flyingInfo.setValidityPeriodEnd(flyingInfoDTO.getValidityPeriodEnd());
        flyingInfo.setTenantId(flightOrderCreateDTO.getTenantId());
        return flyingInfo;
    }

    private List<FlightOrderFileDO> buildFlightOrderFiles(FlightOrderCreateDTO flightOrderCreateDTO) {
        
        if (CollUtil.isEmpty(flightOrderCreateDTO.getFiles())) {
            return Lists.newArrayList();
        }
        
        return flightOrderCreateDTO.getFiles().stream().map(e -> {
            FlightOrderFileDO flightOrderFileDO = new FlightOrderFileDO();
            flightOrderFileDO.setOrderNo(flightOrderCreateDTO.getOrderNo());
            flightOrderFileDO.setFileName(e.getFileName());
            flightOrderFileDO.setFilePath(e.getFilePath());
            flightOrderFileDO.setTenantId(flightOrderCreateDTO.getTenantId());
            return flightOrderFileDO;
        }).collect(Collectors.toList());
    }

    private List<ScheduleTaskDO> buildScheduleTask(String orderNo, LocalDateTime validityPeriodEnd, String tenantId) {
        ScheduleTaskDO task = new ScheduleTaskDO();
        task.setBizNo(orderNo);
        task.setTaskType(ScheduleTaskTypeEnum.CLOSE_ORDER.getType());
        task.setStatus(ScheduleTaskStatusEnum.WAIT_EXECUTE.getStatus());
        String[] active = env.getActiveProfiles();
        String profile = active.length > 0
                ? active[0]
                : env.getDefaultProfiles()[0];
        task.setEnv(profile);
        long triggerMillis = validityPeriodEnd
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toInstant()
                .toEpochMilli();
        task.setTriggerTime(triggerMillis);
        task.setTenantId(tenantId);

        return Lists.newArrayList(task);
    }

    private static List<FlightOrderProductUsageDO> buildOrderProductUsageList(FlightOrderCreateDTO flightOrderCreateDTO, String orderNo) {

        if (CollUtil.isEmpty(flightOrderCreateDTO.getProducts())) {
            return Lists.newArrayList();
        }

        return flightOrderCreateDTO.getProducts().stream().map(product -> {
            FlightOrderProductUsageDO flightOrderProductUsageDO = new FlightOrderProductUsageDO();
            flightOrderProductUsageDO.setOrderNo(orderNo);
            flightOrderProductUsageDO.setProductNo(product.getProductNo());
            flightOrderProductUsageDO.setProductType(product.getProductType().getType());
            flightOrderProductUsageDO.setUavModel(product.getUavModel());
            flightOrderProductUsageDO.setTotalQuantity(product.getTotalQuantity());
            flightOrderProductUsageDO.setUseQuantity(0);
            flightOrderProductUsageDO.setValidityPeriodType(ObjectUtil.isNull(product.getValidityPeriodType()) ? null : product.getValidityPeriodType().getType());
            flightOrderProductUsageDO.setValidityPeriodStart(product.getValidityPeriodStart());
            flightOrderProductUsageDO.setValidityPeriodEnd(product.getValidityPeriodEnd());
            flightOrderProductUsageDO.setCount(product.getCount());
            flightOrderProductUsageDO.setPrice(product.getPrice());
            flightOrderProductUsageDO.setBasePrice(product.getBasePrice());
            flightOrderProductUsageDO.setTenantId(flightOrderCreateDTO.getTenantId());

            return flightOrderProductUsageDO;
        }).collect(Collectors.toList());
    }

    private static FlightOrderDO buildOrderDO(FlightOrderCreateDTO flightOrderCreateDTO) {
        FlightOrderDO flightOrderDO = new FlightOrderDO();
        flightOrderDO.setOrderNo(flightOrderCreateDTO.getOrderNo());
        flightOrderDO.setStatus(OrderStatusEnum.APPROVING.getCode());
        flightOrderDO.setProductName(flightOrderCreateDTO.getProductName());
        flightOrderDO.setMainProductType(flightOrderCreateDTO.getProductType());
        flightOrderDO.setUserNo(flightOrderCreateDTO.getUserNo());
        flightOrderDO.setOrderAmount(flightOrderCreateDTO.getOrderAmount());
        flightOrderDO.setPayType(flightOrderCreateDTO.getPayType().getPayType());
        flightOrderDO.setApproveStatus(FlightOrderApproveStatusEnum.APPROVING.getStatusCode());
        flightOrderDO.setPayStatus(FlightOrderPayStatusEnum.UN_PAY.getStatus());
        flightOrderDO.setRefundStatus(FlightOrderRefundStatusEnum.UN_REFUND.getStatus());
        flightOrderDO.setOrderType(flightOrderCreateDTO.getOrderType());
        flightOrderDO.setCategoryNo(flightOrderCreateDTO.getCategoryNo());
        flightOrderDO.setCategoryName(flightOrderCreateDTO.getCategoryName());
        flightOrderDO.setOrganizationId(flightOrderCreateDTO.getOrganizationId());
        flightOrderDO.setOrganizationName(flightOrderCreateDTO.getOrganizationName());
        flightOrderDO.setOrderTime(LocalDateTime.now());
        flightOrderDO.setScene(flightOrderCreateDTO.getScene());
        flightOrderDO.setTenantId(flightOrderCreateDTO.getTenantId());
        flightOrderDO.setCycleNo(flightOrderCreateDTO.getCycleNo());
        flightOrderDO.setCycleName(flightOrderCreateDTO.getCycleName());
        flightOrderDO.setTenantId(flightOrderCreateDTO.getTenantId());

        return flightOrderDO;
    }
}
