package com.deepinnet.skyflow.operationcenter.service.approval.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TimeoutActionTypeEnum {

    /**
     * 自动通过
     */
    AUTO_APPROVE("AUTO_APPROVE", "自动通过"),

    /**
     * 自动拒绝
     */
    AUTO_REJECT("AUTO_REJECT", "自动拒绝"),

    /**
     * 发送通知
     */
    NOTIFY("NOTIFY", "发送通知"),

    /**
     * 转交他人审核
     */
    TRANSFER("TRANSFER", "转交他人审核"),

    /**
     * 拉取他人参与审核
     */
    PULL_REVIEWER("PULL_REVIEWER", "拉取他人参与审核");

    private final String type;

    private final String desc;
}
