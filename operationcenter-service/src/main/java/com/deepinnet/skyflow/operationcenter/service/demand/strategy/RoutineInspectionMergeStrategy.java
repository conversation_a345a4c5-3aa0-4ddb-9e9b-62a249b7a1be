package com.deepinnet.skyflow.operationcenter.service.demand.strategy;

import com.deepinnet.skyflow.operationcenter.dto.DemandMergeScheduleDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.MergeDemandCreateDTO;
import com.deepinnet.skyflow.operationcenter.dto.RoutineInspectionFlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlyingFrequencyEnum;
import com.deepinnet.skyflow.operationcenter.service.strategy.Strategy;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyContext;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyTypeEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 日常巡检需求合并策略
 * 
 * <AUTHOR>
 */
@Service
public class RoutineInspectionMergeStrategy implements Strategy {

    @Override
    public boolean supports(StrategyTypeEnum strategyType, Enum<?> demandType) {
        return StrategyTypeEnum.MERGE_DETAIL == strategyType && FlightDemandTypeEnum.ROUTINE_INSPECTION == demandType;
    }

    @Override
    public Object execute(StrategyContext context) {
        MergeDemandCreateDTO mergeDemandCreateDTO = context.getFirstArg();
        return mergeDetails(mergeDemandCreateDTO);
    }
    
    private RoutineInspectionFlightDemandDTO mergeDetails(MergeDemandCreateDTO mergeDemandCreateDTO) {
        RoutineInspectionFlightDemandDTO mergedDetail = new RoutineInspectionFlightDemandDTO();
        
        // 获取所有原始需求的详情
        List<RoutineInspectionFlightDemandDTO> originDetails = mergeDemandCreateDTO.getOriginDemandDTOList().stream()
                .map(FlightDemandDTO::getRoutineInspectionDetail)
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(originDetails)) {
            return mergedDetail;
        }
        
        // 合并逻辑：
        DemandMergeScheduleDTO mergeScheduleDTO = mergeDemandCreateDTO.getMergeScheduleDTO();
        mergedDetail.setFlyingFrequency(FlyingFrequencyEnum.DAILY);
        mergedDetail.setDemandStartTime(mergeScheduleDTO.getMergeScheduleStartDate());
        mergedDetail.setDemandEndTime(mergeScheduleDTO.getMergeScheduleEndDate());
        mergedDetail.setFlyingStartTime(mergeScheduleDTO.getMergeScheduleStartTime());
        mergedDetail.setFlyingEndTime(mergeScheduleDTO.getMergeScheduleEndTime());
        
        // 3. 其他字段取第一个非空值
        RoutineInspectionFlightDemandDTO firstDetail = originDetails.get(0);
        mergedDetail.setCycleType(firstDetail.getCycleType());
        mergedDetail.setFlyingFrequency(firstDetail.getFlyingFrequency());
        
        // 4. 飞行次数保持一致，因为在规则中指定了
        mergedDetail.setFlyingNum(mergeDemandCreateDTO.getOriginDemandDTOList().get(0).getRoutineInspectionDetail().getFlyingNum());

        return mergedDetail;
    }
} 