package com.deepinnet.skyflow.operationcenter.service.task.worker;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightPlanCycleDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.ScheduleTaskDO;
import com.deepinnet.skyflow.operationcenter.dal.enums.ScheduleTaskTypeEnum;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightPlanCycleRepository;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.order.FlightOrderService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/23
 */

@Component
public class PlanCycleCloseScheduleWorker extends AbstractBaseTaskWorker{

    @Resource
    private FlightPlanCycleRepository flightPlanCycleRepository;

    @Override
    public ScheduleTaskTypeEnum getTaskType() {
        return ScheduleTaskTypeEnum.CLOSE_CYCLE_PLAN;
    }

    @Override
    protected void doOtherBiz(ScheduleTaskDO scheduleTask) {

    }

    @Override
    protected List<String> needTerminatedErrorCode() {
        return List.of(BizErrorCode.CYCLE_PLAN_EXPIRED_ERROR.getCode(), BizErrorCode.CYCLE_PLAN_NOT_EXIST_ERROR.getCode());
    }

    @Override
    protected void checkBizStatus(Long taskId, String bizNo, String tenantId) {

        FlightPlanCycleDO cycleDO = queryFlightPlanCycle(bizNo, tenantId);

        if (!cycleDO.getIsActive()) {
            throw new BizException(BizErrorCode.CYCLE_PLAN_EXPIRED_ERROR.getCode()
                    , BizErrorCode.CYCLE_PLAN_EXPIRED_ERROR.getDesc());
        }
    }

    @Override
    protected void updateBizStatus(String bizNo, String tenantId) {
        flightPlanCycleRepository.update(Wrappers.<FlightPlanCycleDO>lambdaUpdate()
                .eq(FlightPlanCycleDO::getCycleNo, bizNo)
                .set(FlightPlanCycleDO::getIsActive, false)
                .set(FlightPlanCycleDO::getGmtModified, LocalDateTime.now()));
    }

    private FlightPlanCycleDO queryFlightPlanCycle(String bizNo, String tenantId) {
        return Optional.ofNullable(
                        flightPlanCycleRepository
                                .getOne(Wrappers.<FlightPlanCycleDO>lambdaQuery()
                                        .eq(FlightPlanCycleDO::getCycleNo, bizNo)
                                        .eq(FlightPlanCycleDO::getTenantId, tenantId)))
                .orElseThrow(() -> new BizException(BizErrorCode.CYCLE_PLAN_NOT_EXIST_ERROR.getCode()
                        , BizErrorCode.CYCLE_PLAN_NOT_EXIST_ERROR.getDesc()));
    }
}
