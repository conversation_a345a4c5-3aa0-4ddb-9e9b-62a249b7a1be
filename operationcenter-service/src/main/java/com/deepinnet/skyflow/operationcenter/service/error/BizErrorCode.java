package com.deepinnet.skyflow.operationcenter.service.error;

import lombok.Getter;

/**
 * 业务错误码
 *
 * <AUTHOR>
 */
@Getter
public enum BizErrorCode {

    /**
     * 通用错误码
     */
    SYSTEM_ERROR("SYSTEM_ERROR", "系统内部错误"),
    ILLEGAL_PARAMS("IL<PERSON><PERSON>L_PARAMS", "参数非法"),
    ILLEGAL_REQUEST("ILLEGAL_REQUEST", "非法请求"),
    OPERATION_FAILED("OPERATION_FAILED", "操作失败"),
    NOT_FOUND("NOT_FOUND", "未找到相关资源"),
    ALREADY_EXISTS("ALREADY_EXISTS", "资源已存在"),
    UNKNOWN_EXCEPTION("UNKNOWN_EXCEPTION", "未知异常"),
    NOT_EXIST_ERROR("NOT_EXIST_ERROR", "暂无数据"),
    FILE_DOWNLOAD_ERROR("FILE_DOWNLOAD_ERROR", "文件下载异常"),
    NO_PERMISSION("NO_PERMISSION", "无权限"),

    /**
     * 飞行产品相关错误码
     */
    PRODUCT_NOT_FOUND("PRODUCT_NOT_FOUND", "产品不存在"),
    PRODUCT_ALREADY_EXISTS("PRODUCT_ALREADY_EXISTS", "产品已存在"),
    PRODUCT_PRICE_NOT_FOUND("PRODUCT_PRICE_NOT_FOUND", "产品价格不存在"),
    PRODUCT_PRICE_TYPE_NOT_FOUND("PRODUCT_PRICE_TYPE_NOT_FOUND", "产品价格类型不存在"),
    PRODUCT_TYPE_NOT_FOUND("PRODUCT_TYPE_NOT_FOUND", "产品类型不存在"),

    /**
     * 类目相关错误码
     */
    CATEGORY_NOT_FOUND("CATEGORY_NOT_FOUND", "类目不存在"),
    CATEGORY_ALREADY_EXISTS("CATEGORY_ALREADY_EXISTS", "类目已存在"),

    /**
     * 价格相关错误码
     */
    PRICE_NOT_FOUND("PRICE_NOT_FOUND", "价格信息不存在"),

    /**
     * 飞行无人机站点相关错误码
     */
    FLIGHT_STATION_NOT_FOUND("FLIGHT_STATION_NOT_FOUND", "机巢不存在"),
    FLIGHT_STATION_ALREADY_EXISTS("FLIGHT_STATION_ALREADY_EXISTS", "机巢已存在"),

    /**
     * 飞行无人机品牌型号相关错误码
     */
    FLIGHT_UAV_BM_NOT_FOUND("FLIGHT_UAV_BM_NOT_FOUND", "无人机型号不存在"),
    FLIGHT_UAV_BM_ALREADY_EXISTS("FLIGHT_UAV_BM_ALREADY_EXISTS", "无人机型号已存在"),

    /**
     * 飞行无人机相关错误码
     */
    FLIGHT_UAV_NOT_FOUND("FLIGHT_UAV_NOT_FOUND", "无人机不存在"),
    FLIGHT_UAV_ALREADY_EXISTS("FLIGHT_UAV_ALREADY_EXISTS", "无人机已存在"),

    /**
     * 飞行需求相关错误码
     */
    DEMAND_NOT_FOUND("DEMAND_NOT_FOUND", "需求不存在"),
    DEMAND_ALREADY_EXISTS("DEMAND_ALREADY_EXISTS", "需求已存在"),
    DEMAND_PROP_NOT_FOUND("DEMAND_PROP_NOT_FOUND", "需求属性不存在"),

    /**
     * 撮合订单相关错误码
     */
    MATCH_ORDER_NOT_FOUND("MATCH_ORDER_NOT_FOUND", "撮合订单不存在"),
    MATCH_ORDER_ALREADY_EXISTS("MATCH_ORDER_ALREADY_EXISTS", "撮合订单已存在"),

    /**
     * 订单相关错误码
     */
    ORDER_NOT_FOUND("ORDER_NOT_FOUND", "订单不存在"),
    ORDER_APPROVE_STATUS_NOT_EXIST("APPROVE_STATUS_NOT_EXIST", "审核状态不存在"),
    ORDER_APPROVE_STATUS_ILLEGAL("ORDER_APPROVE_STATUS_ILLEGAL", "审核状态不合法"),
    ORDER_APPROVE_STATUS_ALREADY_APPROVED("ORDER_APPROVE_STATUS_ALREADY_APPROVED", "订单已审核通过"),
    ORDER_APPROVE_STATUS_ALREADY_REFUSED("ORDER_APPROVE_STATUS_ALREADY_REFUSED", "订单已审核拒绝"),
    ORDER_NOT_EXIST_MAIN_PRODUCT("ORDER_NOT_EXIST_MAIN_PRODUCT", "主产品不存在"),
    ORDER_CREATE_FAILED("ORDER_CREATE_FAILED", "订单创建失败"),
    CURRENT_USER_CAN_NOT_CREATE_ORDER("CURRENT_USER_CAN_NOT_CREATE_ORDER", "您的身份暂不支持服务下单功能！"),
    USER_INFO_NOT_COMPLETE("USER_INFO_NOT_COMPLETE", "请完善单位信息，再进行购买操作！"),
    USER_CHECK_ERROR("USER_CHECK_ERROR", "用户校验失败"),
    ORDER_ALREADY_CLOSED("ORDER_ALREADY_CLOSED", "订单已关闭"),
    ORDER_ALREADY_FINISHED("ORDER_ALREADY_FINISHED", "订单已结束"),
    ORDER_USAGE_CHANGE_ERROR("ORDER_USAGE_CHANGE_ERROR", "订单产品用量变更失败"),
    PRODUCT_SERVICE_NOT_FOUND_ERROR("PRODUCT_SERVICE_NOT_FOUND_ERROR", "服务不存在"),
    ORDER_TYPE_CAN_NOT_MODIFY_ERROR("ORDER_TYPE_CAN_NOT_MODIFY_ERROR", "当前订单类型不允许修改"),
    ORDER_APPROVAL_ERROR("ORDER_APPROVAL_ERROR", "订单审核异常"),
    ORDER_CAN_NOT_DELETE_ERROR("ORDER_CAN_NOT_DELETE_ERROR", "当前订单暂不允许删除"),
    ORDER__DELETE_ERROR("ORDER__DELETE_ERROR", "当前订单暂删除失败"),

    /**
     * 飞行计划查询错误码
     */
    FLIGHT_PLAN_QUERY_ERROR("FLIGHT_PLAN_QUERY_ERROR", "飞行计划查询失败"),

    /**
     * 用户相关错误码
     */
    ACCOUNT_NOT_LOGIN("account_not_login", "当前账号未登录，请先登录"),
    ACCOUNT_LOGIN_EXPIRE("account_login_expire", "当前账号登录已经失效，请重新登录"),
    LENGTH_EXCEEDS_LIMIT("length_exceeds_limit", "长度超过限制"),
    ACCOUNT_NOT_FOUND("ACCOUNT_NOT_FOUND", "账号不存在"),

    /**
     * 租户
     */
    TENANT_ID_NOT_FOUND("tenant_id_not_found", "租户id不存在"),

    /**
     * 定时任务
     */
    INSERT_TASK_ERROR("insert_task_error", "插入任务失败"),
    UPDATE_TASK_STATUS_ERROR("update_task_status_error", "更新任务状态失败"),
    ILLEGAL_SCHEDULE_TASK_TYPE("illegal_schedule_task_type", "非法的定时任务类型"),
    TASK_DELETE_ERROR("task_delete_error", "定时任务删除失败"),

    FILE_IS_EMPTY("file_is_empty", "文件不能为空"),

    FILE_SIZE_EXCEEDS_THE_LIMIT("file_size_exceeds_the_limit", "文件大小超过限制"),

    /**
     * 规则相关错误
     */
    RULE_TYPE_NOT_FOUND_ERROR("rule_type_not_found", "规则类型不存在"),

    /**
     * 周期计划相关错误
     */
    CYCLE_PLAN_SAVE_ERROR("cycle_save_error", "规划周期新增失败"),

    EXIST_CYCLE_PLAN_ERROR("exist_cycle_plan_error", "已存在周期规划"),

    /**
     * 周期计划更新失败
     */
    CYCLE_PLAN_UPDATE_ERROR("cycle_update_error", "规划周期更新失败"),

    CYCLE_PLAN_NOT_EXIST_ERROR("cycle_not_exist_error", "规划周期不存在"),

    CYCLE_PLAN_EXPIRED_ERROR("cycle_plan_expired_error", "当前规划周期已过期"),

    CYCLE_PLAN_EXIST_ORDER_ERROR("cycle_plan_exist_order_error", "当前规划周期已存在规划"),

    CYCLE_PLAN_DELETE_ERROR("cycle_plan_delete_error", "当前规划周期删除失败"),

    /**
     * 周期需求
     */
    CYCLE_DEMAND_EXPIRED_ERROR("cycle_demand_expired_error", "当前需求周期已过期"),
    ;
    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误描述
     */
    private final String desc;

    BizErrorCode(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}