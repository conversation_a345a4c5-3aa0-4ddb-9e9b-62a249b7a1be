package com.deepinnet.skyflow.operationcenter.service.log;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 请求参数处理工具类
 * 用于将请求参数转换为JSON字符串
 *
 * <AUTHOR>
 */
public class RequestParamsUtil {

    /**
     * 将对象转换为JSON字符串
     * 如果转换失败或对象为null，返回null
     *
     * @param params 请求参数对象
     * @return JSON字符串或null
     */
    public static String toJsonString(Object params) {
        if (params == null) {
            return null;
        }

        try {
            // 处理特殊类型
            if (params instanceof MultipartFile) {
                return handleMultipartFile((MultipartFile) params);
            }
            
            if (params instanceof MultipartFile[]) {
                return handleMultipartFiles((MultipartFile[]) params);
            }

            // 处理Map类型，过滤敏感信息
            if (params instanceof Map) {
                Map<String, Object> filteredMap = filterSensitiveData((Map<?, ?>) params);
                return JSON.toJSONString(filteredMap);
            }

            // 处理普通对象
            return JSON.toJSONString(params);
            
        } catch (JSONException e) {
            LogUtil.warn("请求参数转换为JSON失败: {}", e.getMessage());
            return null;
        } catch (Exception e) {
            LogUtil.warn("请求参数处理异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 处理文件上传参数
     */
    private static String handleMultipartFile(MultipartFile file) {
        Map<String, Object> fileInfo = new HashMap<>();
        fileInfo.put("fileName", file.getOriginalFilename());
        fileInfo.put("size", file.getSize());
        fileInfo.put("contentType", file.getContentType());
        fileInfo.put("type", "MultipartFile");
        return JSON.toJSONString(fileInfo);
    }

    /**
     * 处理多文件上传参数
     */
    private static String handleMultipartFiles(MultipartFile[] files) {
        Map<String, Object> filesInfo = new HashMap<>();
        filesInfo.put("fileCount", files.length);
        filesInfo.put("type", "MultipartFile[]");
        
        if (files.length > 0) {
            Map<String, Object> firstFileInfo = new HashMap<>();
            MultipartFile firstFile = files[0];
            firstFileInfo.put("fileName", firstFile.getOriginalFilename());
            firstFileInfo.put("size", firstFile.getSize());
            firstFileInfo.put("contentType", firstFile.getContentType());
            filesInfo.put("firstFile", firstFileInfo);
        }
        
        return JSON.toJSONString(filesInfo);
    }

    /**
     * 过滤敏感数据
     * 移除密码、token等敏感信息
     */
    private static Map<String, Object> filterSensitiveData(Map<?, ?> originalMap) {
        Map<String, Object> filteredMap = new HashMap<>();
        
        for (Map.Entry<?, ?> entry : originalMap.entrySet()) {
            String key = String.valueOf(entry.getKey()).toLowerCase();
            Object value = entry.getValue();
            
            // 过滤敏感字段
            if (isSensitiveField(key)) {
                filteredMap.put(String.valueOf(entry.getKey()), "***");
            } else {
                filteredMap.put(String.valueOf(entry.getKey()), value);
            }
        }
        
        return filteredMap;
    }

    /**
     * 判断是否为敏感字段
     */
    private static boolean isSensitiveField(String fieldName) {
        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("password") ||
               lowerFieldName.contains("pwd") ||
               lowerFieldName.contains("token") ||
               lowerFieldName.contains("secret") ||
               lowerFieldName.contains("key") ||
               lowerFieldName.contains("authorization");
    }

    /**
     * 限制JSON字符串长度
     * 如果超过指定长度，进行截断
     *
     * @param jsonString JSON字符串
     * @param maxLength 最大长度
     * @return 截断后的字符串
     */
    public static String limitLength(String jsonString, int maxLength) {
        if (jsonString == null) {
            return null;
        }
        
        if (jsonString.length() <= maxLength) {
            return jsonString;
        }
        
        return jsonString.substring(0, maxLength - 3) + "...";
    }

    /**
     * 默认限制长度为2000字符
     */
    public static String limitLength(String jsonString) {
        return limitLength(jsonString, 2000);
    }
}
