package com.deepinnet.skyflow.operationcenter.service.demand.strategy;

import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightDemandDO;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.strategy.Strategy;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyContext;
import com.deepinnet.skyflow.operationcenter.service.strategy.StrategyTypeEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 应急处置需求合并策略
 * 
 * <AUTHOR>
 */
@Service
public class EmergencyResponseMergeStrategy implements Strategy {

    @Override
    public boolean supports(StrategyTypeEnum strategyType, Enum<?> demandType) {
        return StrategyTypeEnum.MERGE_DETAIL == strategyType && FlightDemandTypeEnum.EMERGENCY_RESPONSE == demandType;
    }

    @Override
    public Object execute(StrategyContext context) {
        List<FlightDemandDO> originDemandList = context.getFirstArg();
        return mergeDetails(originDemandList);
    }

    private Object mergeDetails(List<FlightDemandDO> originDemandList) {
        throw new UnsupportedOperationException("暂不支持合并");
    }
} 