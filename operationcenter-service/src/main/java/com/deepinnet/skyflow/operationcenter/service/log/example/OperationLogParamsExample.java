package com.deepinnet.skyflow.operationcenter.service.log.example;

import com.deepinnet.skyflow.operationcenter.enums.OperationLogTemplateEnum;
import com.deepinnet.skyflow.operationcenter.service.log.OperationLogUtil;
import com.deepinnet.skyflow.operationcenter.service.log.annotation.OperationLog;
import org.springframework.stereotype.Service;

/**
 * 操作日志请求参数记录使用示例
 * 展示如何记录请求参数到操作日志
 *
 * <AUTHOR>
 */
@Service
public class OperationLogParamsExample {

    /**
     * 示例1：使用注解自动记录请求参数
     */
    @OperationLog(template = OperationLogTemplateEnum.DEMAND_PUBLISH, useTemplate = true, logParams = true)
    public String createDemandWithAutoParams(DemandCreateRequest request) {
        // 业务逻辑
        System.out.println("创建需求: " + request.getName());
        return "DEMAND_" + System.currentTimeMillis();
    }

    /**
     * 示例2：手动设置请求参数
     */
    @OperationLog
    public String createDemandWithManualParams(DemandCreateRequest request) {
        // 手动设置操作日志上下文和请求参数
        OperationLogUtil.demandPublish(request.getName());
        OperationLogUtil.setRequestParams(request);
        
        // 业务逻辑
        System.out.println("创建需求: " + request.getName());
        return "DEMAND_" + System.currentTimeMillis();
    }

    /**
     * 示例3：多参数方法自动记录
     */
    @OperationLog(template = OperationLogTemplateEnum.MEMBER_ADD, useTemplate = true, logParams = true)
    public void addMemberWithMultiParams(String organizationId, String memberName, String memberRole) {
        // 业务逻辑
        System.out.println("添加成员: " + memberName + " 到组织: " + organizationId + " 角色: " + memberRole);
    }

    /**
     * 示例4：无参数方法
     */
    @OperationLog(template = OperationLogTemplateEnum.LOGIN_SUCCESS, useTemplate = true, logParams = true)
    public void loginExample() {
        // 无参数的方法，请求参数会记录为null
        System.out.println("用户登录成功");
    }

    /**
     * 示例5：包含敏感信息的参数（密码会被过滤）
     */
    @OperationLog(template = OperationLogTemplateEnum.ACCOUNT_ADD, useTemplate = true, logParams = true)
    public String createAccountWithSensitiveData(AccountCreateRequest request) {
        // 密码等敏感信息会被自动过滤为 "***"
        System.out.println("创建账号: " + request.getUsername());
        return "ACCOUNT_" + System.currentTimeMillis();
    }

    /**
     * 示例6：文件上传参数
     */
    @OperationLog(template = OperationLogTemplateEnum.DATA_DOWNLOAD, useTemplate = true, logParams = true)
    public String uploadFileExample(org.springframework.web.multipart.MultipartFile file, String description) {
        // 文件参数会被转换为文件信息（文件名、大小、类型等）
        System.out.println("上传文件: " + file.getOriginalFilename());
        return "FILE_" + System.currentTimeMillis();
    }

    /**
     * 示例7：手动设置复杂参数
     */
    @OperationLog
    public void complexParamsExample(String param1, Integer param2) {
        // 设置操作日志上下文
        OperationLogUtil.flightPlanPublish("测试计划");
        
        // 手动构建参数对象
        java.util.Map<String, Object> params = new java.util.HashMap<>();
        params.put("param1", param1);
        params.put("param2", param2);
        params.put("timestamp", System.currentTimeMillis());
        
        // 设置请求参数
        OperationLogUtil.setRequestParams(params);
        
        // 业务逻辑
        System.out.println("执行复杂操作");
    }

    // 示例DTO类
    public static class DemandCreateRequest {
        private String name;
        private String description;
        private String type;

        // getters and setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
    }

    public static class AccountCreateRequest {
        private String username;
        private String password; // 敏感信息，会被过滤
        private String email;

        // getters and setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
    }
}
