package com.deepinnet.skyflow.operationcenter.service.strategy;

import lombok.Builder;
import lombok.Data;

/**
 * 策略执行上下文
 * 
 * <AUTHOR>
 */
@Data
@Builder
public class StrategyContext {
    
    /**
     * 执行参数
     */
    private Object[] args;
    
    /**
     * 便捷方法：获取第一个参数
     */
    public <T> T getArg(int index) {
        if (args == null || index >= args.length) {
            return null;
        }
        return (T) args[index];
    }
    
    /**
     * 便捷方法：获取第一个参数
     */
    public <T> T getFirstArg() {
        return getArg(0);
    }
    
    /**
     * 便捷方法：获取第二个参数
     */
    public <T> T getSecondArg() {
        return getArg(1);
    }

    /**
     * 便捷方法：获取第三个参数
     */
    public <T> T getThirdArg() {
        return getArg(2);
    }
} 