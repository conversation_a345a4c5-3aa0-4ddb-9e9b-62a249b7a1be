package com.deepinnet.skyflow.operationcenter.service.flow.order;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.infra.api.dto.SimpleDepartmentDTO;
import com.deepinnet.infra.api.dto.UserInfoDTO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderApprovalDO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.FlightOrderDO;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderApprovalRepository;
import com.deepinnet.skyflow.operationcenter.dal.repository.FlightOrderRepository;
import com.deepinnet.skyflow.operationcenter.enums.FlightOrderApproveStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.flow.context.OrderApproveContext;
import com.deepinnet.skyflow.operationcenter.service.util.UserDepartmentUtil;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.yomahub.liteflow.annotation.LiteflowCmpDefine;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */

@LiteflowComponent(value = "flightDemandOrderApproveNode", name = "需求规划订单审核节点")
@LiteflowCmpDefine
public class FlightDemandOrderApproveNode {

    @Resource
    private FlightOrderRepository flightOrderRepository;

    @Resource
    private FlightOrderApprovalRepository flightOrderApprovalRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    @LiteflowMethod(LiteFlowMethodEnum.PROCESS)
    public void process(NodeComponent bindCmp) {
        OrderApproveContext contextBean = bindCmp.getContextBean(OrderApproveContext.class);

        UserInfoDTO userInfo = contextBean.getUserInfo();

        FlightOrderVO flightOrder = contextBean.getFlightOrder();

        FlightOrderApproveStatusEnum approveStatusEnum = FlightOrderApproveStatusEnum.getEnumByStatusName(flightOrder.getApproveStatus());

        // 当前用户是客户的最高权限, 未经过下级审核, 该订单为待审核状态, 直接审核则提示错误信息
        if (StrUtil.equals(flightOrder.getStatus(), FlightOrderApproveStatusEnum.APPROVING.getStatusCode())
                && UserDepartmentUtil.isRootDepartment(userInfo.getUserDepartment().getDepartmentId())) {
            LogUtil.error("订单:{}, 客户最高权限用户:{}, 审核状态:{}, 请通过上级审核后再进行审核", flightOrder.getOrderNo(), UserUtil.getUserNo(), flightOrder.getApproveStatus());
            throw new BizException(BizErrorCode.ORDER_APPROVE_STATUS_ILLEGAL.getCode(), BizErrorCode.ORDER_APPROVE_STATUS_ILLEGAL.getDesc());
        }

        // 最高级审核已通过, 无需再次审核
        if (StrUtil.equals(flightOrder.getApproveStatus(), FlightOrderApproveStatusEnum.APPROVED.getStatusCode())
                && UserDepartmentUtil.isRootDepartment(userInfo.getUserDepartment().getDepartmentId())) {
            LogUtil.error("订单:{}, 审核状态:{}, 已审核", flightOrder.getOrderNo(), flightOrder.getApproveStatus());
            throw new BizException(BizErrorCode.ORDER_APPROVE_STATUS_ALREADY_APPROVED.getCode(), BizErrorCode.ORDER_APPROVE_STATUS_ALREADY_APPROVED.getDesc());
        }

        // 一级审核已通过, 无需再次审核
        if (StrUtil.equals(flightOrder.getApproveStatus(), FlightOrderApproveStatusEnum.PARTLY_APPROVED.getStatusCode())
                && !UserDepartmentUtil.isRootDepartment(userInfo.getUserDepartment().getDepartmentId())) {
            LogUtil.error("订单:{}, 审核状态:{}, 已审核", flightOrder.getOrderNo(), flightOrder.getApproveStatus());
            throw new BizException(BizErrorCode.ORDER_APPROVE_STATUS_ALREADY_APPROVED.getCode(), BizErrorCode.ORDER_APPROVE_STATUS_ALREADY_APPROVED.getDesc());
        }

        if (ObjectUtil.isNull(approveStatusEnum)) {
            LogUtil.error("订单:{}, 审核状态:{}, 不存在", flightOrder.getOrderNo(), flightOrder.getApproveStatus());
            throw new BizException(BizErrorCode.ORDER_APPROVE_STATUS_NOT_EXIST.getCode(), BizErrorCode.ORDER_APPROVE_STATUS_NOT_EXIST.getDesc());
        }

        if (StrUtil.equals(flightOrder.getApproveStatus(), FlightOrderApproveStatusEnum.REJECTED.getStatusCode())) {
            LogUtil.error("订单:{}, 审核状态:{}, 已拒绝", flightOrder.getOrderNo(), flightOrder.getApproveStatus());
            throw new BizException(BizErrorCode.ORDER_APPROVE_STATUS_ALREADY_REFUSED.getCode(), BizErrorCode.ORDER_APPROVE_STATUS_ALREADY_REFUSED.getDesc());
        }

        try{

            transactionTemplate.executeWithoutResult(e -> {

                String approveStatus;
                String status;

                if (StrUtil.equals(contextBean.getApproveStatus(), FlightOrderApproveStatusEnum.APPROVED.getStatusCode())) {
                    //判断一级审核，一级审核则部分审核通过,
                    // 二级审核则全部审核通过
                    approveStatus = UserDepartmentUtil.isRootDepartment(userInfo.getUserDepartment().getDepartmentId()) ? FlightOrderApproveStatusEnum.APPROVED.getStatusCode() : FlightOrderApproveStatusEnum.PARTLY_APPROVED.getStatusCode();
                    status = UserDepartmentUtil.isRootDepartment(userInfo.getUserDepartment().getDepartmentId()) ? OrderStatusEnum.IN_PROGRESS.getCode() : OrderStatusEnum.PARTLY_APPROVED.getCode();
                } else {
                    approveStatus = FlightOrderApproveStatusEnum.REJECTED.getStatusCode();
                    status = OrderStatusEnum.CLOSED.getCode();
                }

                flightOrderRepository.update(Wrappers.<FlightOrderDO>lambdaUpdate()
                        .eq(FlightOrderDO::getOrderNo, flightOrder.getOrderNo())
                        .set(FlightOrderDO::getApproveStatus, approveStatus)
                        .set(FlightOrderDO::getStatus, status)
                        .set(FlightOrderDO::getGmtModified, LocalDateTime.now()));

                flightOrderApprovalRepository.save(buildFlightApprovalDO(contextBean));
            });
        } catch (Exception e) {
            LogUtil.error("订单:{}, 审核状态:{}, 审核失败, 堆栈信息:{}", flightOrder.getOrderNo(), contextBean.getApproveStatus(), e);
            throw new BizException(BizErrorCode.ORDER_APPROVAL_ERROR.getCode(), BizErrorCode.ORDER_APPROVAL_ERROR.getCode());
        }

    }

    private List<Long> getDepartmentIdList(UserInfoDTO userInfo) {
        return userInfo.getDepartments().stream()
                .filter(ObjectUtil::isNotNull)
                .flatMap(List::stream)
                .filter(ObjectUtil::isNotNull)
                .map(SimpleDepartmentDTO::getId)
                .filter(ObjectUtil::isNotNull)
                .distinct()
                .collect(Collectors.toList());
    }

    private FlightOrderApprovalDO buildFlightApprovalDO(OrderApproveContext contextBean) {
        UserInfoDTO userInfo = contextBean.getUserInfo();
        FlightOrderApprovalDO flightOrderApprovalDO = new FlightOrderApprovalDO();
        flightOrderApprovalDO.setOrderNo(contextBean.getOrderNo());
        flightOrderApprovalDO.setApprovalStatus(contextBean.getApproveStatus());
        flightOrderApprovalDO.setApprovalUserNo(contextBean.getApprovalUserNo());
        flightOrderApprovalDO.setApprovalUserName(contextBean.getApprovalUserName());
        flightOrderApprovalDO.setApprovalTime(DateUtil.current());
        flightOrderApprovalDO.setRemark(contextBean.getRemark());
        flightOrderApprovalDO.setRole(userInfo.getRoles().get(0).getRoleName());
        flightOrderApprovalDO.setPhone(userInfo.getPhone());
        flightOrderApprovalDO.setOrganizationId(String.valueOf(userInfo.getUserDepartment().getDepartmentId()));
        flightOrderApprovalDO.setOrganizationName(userInfo.getUserDepartment().getDepartmentName());
        flightOrderApprovalDO.setTenantId(contextBean.getTenantId());
        return flightOrderApprovalDO;
    }
}
