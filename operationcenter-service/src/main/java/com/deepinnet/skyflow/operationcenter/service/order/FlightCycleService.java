package com.deepinnet.skyflow.operationcenter.service.order;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.FlightPlanCycleDTO;
import com.deepinnet.skyflow.operationcenter.dto.PageQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.PlanCycleQueryDTO;
import com.deepinnet.skyflow.operationcenter.vo.FlightPlanCycleVO;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
public interface FlightCycleService {

    CommonPage<FlightPlanCycleVO> queryList(PlanCycleQueryDTO dto);

    FlightPlanCycleVO queryDetail(String cycleNo);

    Boolean save(FlightPlanCycleDTO dto);

    Boolean update(FlightPlanCycleDTO dto);

    Boolean delete(String cycleNo);
}
