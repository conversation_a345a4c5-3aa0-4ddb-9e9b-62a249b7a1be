package com.deepinnet.skyflow.operationcenter.service.workbench.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.constants.DataScopeConstants;
import com.deepinnet.infra.api.dto.DataAccessDTO;
import com.deepinnet.infra.api.dto.SimpleUserInfoDTO;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.*;
import com.deepinnet.skyflow.operationcenter.dal.model.FlightPlanPageQuery;
import com.deepinnet.skyflow.operationcenter.dal.repository.*;
import com.deepinnet.skyflow.operationcenter.dto.BizStatQueryDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightPlanStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderStatusEnum;
import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApproveStatusEnum;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import com.deepinnet.skyflow.operationcenter.service.constants.ApproveBizTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.constants.DownloadSetConstants;
import com.deepinnet.skyflow.operationcenter.service.error.BizErrorCode;
import com.deepinnet.skyflow.operationcenter.service.excel.model.BizStatExportRow;
import com.deepinnet.skyflow.operationcenter.service.flow.order.FlightDemandOrderApproveNode;
import com.deepinnet.skyflow.operationcenter.service.util.DateTimeRange;
import com.deepinnet.skyflow.operationcenter.service.util.StatCalcUtil;
import com.deepinnet.skyflow.operationcenter.service.workbench.FlightStatisticsService;
import com.deepinnet.skyflow.operationcenter.util.UserUtil;
import com.deepinnet.skyflow.operationcenter.util.enums.UserTypeEnum;
import com.deepinnet.skyflow.operationcenter.vo.BizStatVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightWorkbenchCoreVO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.LongConsumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 工作台统计实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/18
 */

@Service
@RequiredArgsConstructor
public class FlightStatisticsServiceImpl implements FlightStatisticsService {

    private final UserRemoteClient userRemoteClient;

    private final FlightOrderRepository flightOrderRepository;

    private final FlightPlanRepository flightPlanRepository;

    private final FlightDemandPlanStatRepository flightDemandPlanStatRepository;

    private final FlightDemandRepository flightDemandRepository;

    private final ApprovalInstanceRepository approvalInstanceRepository;

    @Override
    public FlightWorkbenchCoreVO getCoreView() {
        List<String> userNos = getAvailableUsers();

        // 统计出来至昨日的数据记录
        List<FlightDemandPlanStatDO> yesterdayUsersStat = flightDemandPlanStatRepository.list(Wrappers.<FlightDemandPlanStatDO>lambdaQuery()
                .in(CollUtil.isNotEmpty(userNos), FlightDemandPlanStatDO::getUserCode, userNos)
                .eq(FlightDemandPlanStatDO::getStatDate, DateTimeRange.yesterdayDate()));

        FlightWorkbenchCoreVO flightWorkbenchCoreVO = new FlightWorkbenchCoreVO();

        // 已审核的规划数
        queryApprovedOrderCount(userNos, yesterdayUsersStat, flightWorkbenchCoreVO);

        // 有效需求数
        queryValidDemandCount(userNos, yesterdayUsersStat, flightWorkbenchCoreVO);

        // 飞行计划数 [待飞行计划数] && [已完成计划数]
        queryFlightPlanCount(userNos, yesterdayUsersStat, flightWorkbenchCoreVO);

        return flightWorkbenchCoreVO;
    }

    private void queryValidDemandCount(List<String> userNos, List<FlightDemandPlanStatDO> yesterdayUsersStat, FlightWorkbenchCoreVO flightWorkbenchCoreVO) {
        long todayCount = flightDemandRepository.count(Wrappers.<FlightDemandDO>lambdaQuery()
                .in(CollUtil.isNotEmpty(userNos), FlightDemandDO::getPublisherNo, userNos)
                .eq(FlightDemandDO::getIsMergeDemand, false)
                .eq(FlightDemandDO::getApproveStatus, ApproveStatusEnum.APPROVED.getStatusCode())
                .eq(FlightDemandDO::getIsLatest, true));

        int yesterdayCount = CollUtil.isEmpty(yesterdayUsersStat) ? 0 : Convert.toInt(yesterdayUsersStat.stream()
                .map(FlightDemandPlanStatDO::getValidDemandNum)
                .mapToLong(Long::longValue)
                .sum());

        flightWorkbenchCoreVO.setEffectiveDemands(StatCalcUtil.buildBasic(Convert.toInt(todayCount), yesterdayCount));
    }

    @Override
    public List<BizStatVO> getRangeDetail(BizStatQueryDTO dto) {

        Assert.notNull(dto, "query params not null!");
        Assert.notNull(dto.getStartDate(), "start date not null!");
        Assert.notNull(dto.getEndDate(), "end date not null!");

        LocalDate realStart = dto.getStartDate().minusDays(1);

        List<String> userNos = getAvailableUsers();

        if (StrUtil.isNotEmpty(dto.getDepartmentId())) {
            List<SimpleUserInfoDTO> departmentBindUsers = userRemoteClient.getDepartmentBindUsers(dto.getDepartmentId());
            userNos = CollUtil.isEmpty(departmentBindUsers) ?
                    userNos : departmentBindUsers.stream().map(SimpleUserInfoDTO::getUserNo).collect(Collectors.toList());
        }

        List<FlightDemandPlanStatDO> rawList = flightDemandPlanStatRepository
                .selectUserCodesByStatDateRange(realStart, dto.getEndDate(), userNos);

        if (CollUtil.isEmpty(rawList)) {
            return Lists.newArrayList();
        }

        Map<LocalDate, FlightDemandPlanStatDO> rawMap = rawList.stream()
                .collect(Collectors.toMap(FlightDemandPlanStatDO::getStatDate, Function.identity()));

        return calRangeData(dto, rawMap);

    }

    @Override
    public void exportRangeDetail(BizStatQueryDTO dto, HttpServletResponse response) {

        List<BizStatVO> list = getRangeDetail(dto);

        if (CollUtil.isEmpty(list)) {
            handleEmptyData(response);
            return;
        }

        List<BizStatExportRow> exportRows = list.stream()
                .map(this::convertToExportRow)
                .collect(Collectors.toList());

        String fileName = generateFileName(dto);

        setDownloadResponseHeaders(response, fileName);

        try {
            EasyExcel.write(response.getOutputStream(), BizStatExportRow.class)
                    .sheet("业务数据明细")
                    .doWrite(exportRows);
        } catch (IOException e) {
            LogUtil.error("数据下载异常, 查询参数: {}", JSONObject.toJSONString(dto), e);
            throw new BizException(BizErrorCode.FILE_DOWNLOAD_ERROR.getCode(), BizErrorCode.FILE_DOWNLOAD_ERROR.getDesc());
        }
    }

    @Override
    public Integer pendingApprovalCount(String type) {

        Assert.notNull(type, "type not null!");

        List<ApprovalNodeDO> needApprovalNodeList = approvalInstanceRepository.queryPendingApprovalCountByUserNo(type, UserUtil.getUserNo());

        return needApprovalNodeList.size();
    }

    private void handleEmptyData(HttpServletResponse response) {
        try {
            response.setStatus(HttpServletResponse.SC_OK);
            response.setContentType(DownloadSetConstants.APPLICATION_JSON_CONTENT_TYPE);
            String errorResponse = JSONObject.toJSONString(
                    Result.fail(BizErrorCode.NOT_EXIST_ERROR.getCode(), BizErrorCode.NOT_EXIST_ERROR.getDesc()));
            response.getWriter().write(errorResponse);
        } catch (IOException e) {
            LogUtil.error("空数据处理异常", e);
            throw new BizException(BizErrorCode.FILE_DOWNLOAD_ERROR.getCode(), BizErrorCode.FILE_DOWNLOAD_ERROR.getDesc());
        }
    }

    private BizStatExportRow convertToExportRow(BizStatVO e) {
        return BizStatExportRow.builder()
                .statDate(e.getStatDate())
                .validDemandNum(formatToString(e.getValidDemandNum(), e.getValidDemandDiff(), e.getSignedValidDemandPct()))
                .totalPlanNum(formatToString(e.getTotalPlanNum(), e.getTotalPlanDiff(), e.getSignedTotalPlanPct()))
                .readyPlanNum(formatToString(e.getReadyPlanNum(), e.getReadyPlanDiff(), e.getSignedReadyPlanPct()))
                .completePlanNum(formatToString(e.getCompletePlanNum(), e.getCompletePlanDiff(), e.getSignedCompletePlanPct()))
                .build();
    }

    private String generateFileName(BizStatQueryDTO dto) {
        return DownloadSetConstants.FILE_NAME
                + dto.getStartDate()
                + DownloadSetConstants.UNDER_LINE
                + dto.getEndDate()
                + DownloadSetConstants.FILE_NAME_SUFFIX;
    }

    private void setDownloadResponseHeaders(HttpServletResponse response, String fileName) {
        response.setContentType(DownloadSetConstants.EXCEL_CONTENT_TYPE);
        response.setCharacterEncoding(DownloadSetConstants.CHARACTER_ENCODING);
        response.setHeader(DownloadSetConstants.EXCEL_EXPORT_HEADER_NAME
                , DownloadSetConstants.EXCEL_EXPORT_HEADER_VALUE
                        + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
    }

    private String formatToString(long validDemandNum, long validDemandDiff, String signedValidDemandPct) {
        String diff = validDemandDiff == 0 ? "0" : (validDemandDiff > 0 ? "+" + validDemandDiff : "-" + validDemandDiff);
        return validDemandNum + "(" + diff + "," + (StrUtil.isBlank(signedValidDemandPct) ? "0" : signedValidDemandPct) + ")";
    }

    private List<BizStatVO> calRangeData(BizStatQueryDTO dto, Map<LocalDate, FlightDemandPlanStatDO> rawMap) {
        FlightDemandPlanStatDO emptyStatDO = new FlightDemandPlanStatDO(null, null, null, 0L, 0L, 0L, 0L, 0L, null, null, null, null);
        return Stream.iterate(dto.getStartDate(),
                        d -> !d.isAfter(dto.getEndDate()),
                        d -> d.plusDays(1))
                .filter(rawMap::containsKey)
                .map(cur -> {
                    FlightDemandPlanStatDO today = rawMap.getOrDefault(cur, emptyStatDO);
                    FlightDemandPlanStatDO yesterday = rawMap.getOrDefault(cur.minusDays(1), emptyStatDO);

                    BizStatVO vo = new BizStatVO();
                    vo.setStatDate(cur);

                    fill(vo::setValidDemandNum
                            , vo::setValidDemandDiff
                            , vo::setValidDemandPct
                            , vo::setSignedValidDemandPct
                            , today.getValidDemandNum()
                            , yesterday.getValidDemandNum());

                    fill(vo::setTotalPlanNum
                            , vo::setTotalPlanDiff
                            , vo::setTotalPlanPct
                            , vo::setSignedTotalPlanPct
                            , today.getTotalPlanNum()
                            , yesterday.getTotalPlanNum());

                    fill(vo::setReadyPlanNum
                            , vo::setReadyPlanDiff
                            , vo::setReadyPlanPct
                            , vo::setSignedReadyPlanPct
                            , today.getReadyPlanNum()
                            , yesterday.getReadyPlanNum());

                    fill(vo::setCompletePlanNum
                            , vo::setCompletePlanDiff
                            , vo::setCompletePlanPct
                            , vo::setSignedCompletePlanPct
                            , today.getCompletePlanNum()
                            , yesterday.getCompletePlanNum());

                    fill(vo::setApprovedOrderNum
                            , vo::setApprovedOrderDiff
                            , vo::setApprovedOrderPct
                            , vo::setSignedApprovedOrderPct
                            , today.getApprovedOrderNum()
                            , yesterday.getApprovedOrderNum());

                    return vo;
                })
                .sorted(Comparator.comparing(BizStatVO::getStatDate).reversed())
                .collect(Collectors.toList());
    }

    private void fill(LongConsumer setVal,
                      LongConsumer setDiff,
                      Consumer<String> setPct,
                      Consumer<String> setSignedVal,
                      long today, long yesterday) {

        setVal.accept(today);
        long diff = today - yesterday;
        setDiff.accept(diff);

        if (yesterday == 0) {
            setPct.accept("-");
            return;
        }
        BigDecimal pct = BigDecimal.valueOf(diff)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(yesterday), 2, RoundingMode.DOWN);

        setPct.accept(pct.toString());

        // 带符号
        setSignedVal.accept((diff >= 0 ? "+" : "") + pct.stripTrailingZeros().toPlainString() + "%");
    }

    private void queryFlightPlanCount(List<String> userNos, List<FlightDemandPlanStatDO> yesterdayUsersStat, FlightWorkbenchCoreVO flightWorkbenchCoreVO) {
        // 飞行计划数
        List<FlightPlanDO> flightPlanList = flightPlanRepository
                .getFlightPlanList(FlightPlanPageQuery.builder().userNo(userNos).build());

        List<FlightPlanDO> todayNeedFlyPlanList = Lists.newArrayList();
        List<FlightPlanDO> todayFliedPlanList = Lists.newArrayList();

        if (CollUtil.isNotEmpty(flightPlanList)) {
            todayNeedFlyPlanList = flightPlanList.stream()
                    .filter(e -> ObjectUtil.equals(e.getStatus(), FlightPlanStatusEnum.NEED_APPLIED.getCode()) || ObjectUtil.equals(e.getStatus(), FlightPlanStatusEnum.APPLIED.getCode()))
                    .collect(Collectors.toList());

            todayFliedPlanList = flightPlanList.stream()
                    .filter(e -> ObjectUtil.equals(e.getStatus(), FlightPlanStatusEnum.EXECUTING.getCode()) || ObjectUtil.equals(e.getStatus(), FlightPlanStatusEnum.COMPLETED.getCode()))
                    .collect(Collectors.toList());
        }

        // 待飞行计划数
        queryNeedFlyFlightPlanCount(todayNeedFlyPlanList, yesterdayUsersStat, flightWorkbenchCoreVO);

        // 完成飞行计划数
        queryFliedFlightPlanCount(todayFliedPlanList, yesterdayUsersStat, flightWorkbenchCoreVO);
    }

    private void queryFliedFlightPlanCount(List<FlightPlanDO> flightPlanList, List<FlightDemandPlanStatDO> yesterdayUsersStat, FlightWorkbenchCoreVO flightWorkbenchCoreVO) {

        int yesterdayCount = CollUtil.isEmpty(yesterdayUsersStat) ? 0 : Convert.toInt(yesterdayUsersStat.stream()
                .map(FlightDemandPlanStatDO::getCompletePlanNum)
                .mapToLong(Long::longValue)
                .sum());

        if (CollUtil.isEmpty(flightPlanList)) {
            flightWorkbenchCoreVO.setEndFlightPlans(StatCalcUtil.buildBasic(yesterdayCount, yesterdayCount));
            return;
        }

        flightWorkbenchCoreVO.setEndFlightPlans(StatCalcUtil.buildBasic(yesterdayCount + flightPlanList.size(), yesterdayCount));
    }

    private void queryNeedFlyFlightPlanCount(List<FlightPlanDO> flightPlanList, List<FlightDemandPlanStatDO> yesterdayUsersStat, FlightWorkbenchCoreVO flightWorkbenchCoreVO) {

        int yesterdayCount = CollUtil.isEmpty(yesterdayUsersStat) ? 0 : Convert.toInt(yesterdayUsersStat.stream()
                .map(FlightDemandPlanStatDO::getReadyPlanNum)
                .mapToLong(Long::longValue)
                .sum());

        if (CollUtil.isEmpty(flightPlanList)) {
            flightWorkbenchCoreVO.setFlightPlans(StatCalcUtil.buildBasic(yesterdayCount, yesterdayCount));
            return;
        }

        flightWorkbenchCoreVO.setFlightPlans(StatCalcUtil.buildBasic(yesterdayCount + flightPlanList.size(), yesterdayCount));
    }

    private void queryApprovedOrderCount(List<String> userNos, List<FlightDemandPlanStatDO> yesterdayUsersStat, FlightWorkbenchCoreVO flightWorkbenchCoreVO) {

        int yesterdayCount = CollUtil.isEmpty(yesterdayUsersStat) ? 0 : Convert.toInt(yesterdayUsersStat.stream()
                .map(FlightDemandPlanStatDO::getApprovedOrderNum)
                .mapToLong(Long::longValue)
                .sum());

        List<FlightOrderDO> todayList = flightOrderRepository.list(Wrappers.<FlightOrderDO>lambdaQuery()
                .in(CollUtil.isNotEmpty(userNos), FlightOrderDO::getUserNo, userNos)
                .eq(FlightOrderDO::getOrderType, OrderTypeEnum.DEMAND_PLAN.getCode())
                .ge(FlightOrderDO::getApprovedTime, DateTimeRange.today().getStart())
                .le(FlightOrderDO::getApprovedTime, DateTimeRange.today().getEnd())
                .in(FlightOrderDO::getStatus, Lists.newArrayList(OrderStatusEnum.IN_PROGRESS.getCode(), OrderStatusEnum.FINISHED.getCode()))
                .select(FlightOrderDO::getOrderTime, FlightOrderDO::getOrderNo));

        if (CollUtil.isEmpty(todayList)) {
            flightWorkbenchCoreVO.setApprovedOrders(StatCalcUtil.buildBasic(yesterdayCount, yesterdayCount));
            return;
        }

        flightWorkbenchCoreVO.setApprovedOrders(StatCalcUtil.buildBasic(todayList.size() + yesterdayCount, yesterdayCount));
    }

    private List<String> getAvailableUsers() {
        DataAccessDTO availableQueryData = userRemoteClient.getAvailableQueryData();

        if (ObjectUtil.isNull(availableQueryData)) {
            LogUtil.warn("当前暂无可用用户");
            return Lists.newArrayList();
        }

        List<String> userNos = Lists.newArrayList();

        // 需求计划订单塞入 用户编号 ｜ 部门编号
        if (CollUtil.isNotEmpty(availableQueryData.getSupportQueryUserNos())
                && !availableQueryData.getSupportQueryUserNos().contains(DataScopeConstants.SUPER_ADMIN)
                && ObjectUtil.notEqual(UserTypeEnum.OPERATION, UserUtil.getUserType())) {
            userNos.addAll(availableQueryData.getSupportQueryUserNos());
        }

        return userNos;
    }
}
