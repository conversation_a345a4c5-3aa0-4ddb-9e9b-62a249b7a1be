package com.deepinnet.skyflow.operationcenter.service.approval;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.skyflow.operationcenter.dto.ApprovalConfigRuleQueryDTO;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalInstance;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApprovalInstanceConfigRule;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.ApproveRuleExecEntity;

/**
 * Description:
 * Date: 2025/7/21
 * Author: lijunheng
 */
public interface ApproveRuleService {

    /**
     * 创建审批规则
     *
     * @param approvalInstanceConfigRule
     * @return
     */
    String createApproveRule(ApprovalInstanceConfigRule approvalInstanceConfigRule);

    /**
     * 编辑审批规则
     *
     * @param approvalInstanceConfigRule
     * @return
     */
    Boolean updateApproveRule(ApprovalInstanceConfigRule approvalInstanceConfigRule);

    /**
     * 获取审批规则
     *
     * @param configRuleCode 规则编码
     * @return
     */
    ApprovalInstanceConfigRule getApproveRule(String configRuleCode);

    /**
     * 获取审批规则
     *
     * @param userNo 查找用户所属的一级组织编码
     * @param ruleType 按规则业务类型查找
     * @return
     */
    ApprovalInstanceConfigRule getApproveRule(String userNo, String ruleType);

    /**
     * 分页查询审批规则
     *
     * @param queryDTO
     * @return
     */
    CommonPage<ApprovalInstanceConfigRule> getApprovalConfigRuleList(ApprovalConfigRuleQueryDTO queryDTO);

    /**
     * 执行审批规则，返回审批实例
     *
     * @param approveRuleExecEntity
     * @return
     */
    ApprovalInstance executeApproveRule(ApproveRuleExecEntity approveRuleExecEntity);
}
