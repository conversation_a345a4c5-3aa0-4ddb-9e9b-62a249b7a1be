package com.deepinnet.skyflow.operationcenter.service.demand.rule;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleResult;
import lombok.Data;

import java.util.List;

@Data
public class DemandMergeRuleResult extends RuleResult {

    /**
     * 过滤出来的需求组
     */
    private List<List<FlightDemandDTO>> filterDemandGroup;
    
    /**
     * 包含时间周期信息的需求分组（由ScheduleTimeRule设置）
     */
    private List<DemandGroupWithSchedule> demandGroupsWithSchedule;
    
    /**
     * 检查是否有时间周期信息
     * @return true如果有时间周期信息
     */
    public boolean hasScheduleInfo() {
        return demandGroupsWithSchedule != null && !demandGroupsWithSchedule.isEmpty();
    }
}
