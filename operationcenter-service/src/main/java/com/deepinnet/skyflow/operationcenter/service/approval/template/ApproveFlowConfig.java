package com.deepinnet.skyflow.operationcenter.service.approval.template;

import cn.hutool.core.collection.ListUtil;
import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveRuleService;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.*;
import com.deepinnet.skyflow.operationcenter.service.approval.property.ApproveOrgProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * Description:
 * Date: 2025/7/23
 * Author: lijunheng
 */
//@Component
public class ApproveFlowConfig {

    @Resource
    private ApproveRuleService approveRuleService;

    @Resource
    private ApproveOrgProperty approveOrgProperty;

    @PostConstruct
    public void init() {
        if (approveOrgProperty != null) {
            innerOrg();
            outerOrg();
        }
    }

    private void innerOrg() {
        ApprovalInstanceConfigRule rule = new ApprovalInstanceConfigRule();
        rule.setCode(ApproveConstants.ORG_LEVEL);
        rule.setRuleName("组织内层级");
        ApprovalChooseConfigRule chooseConfigRule = new ApprovalChooseConfigRule();
        chooseConfigRule.setChooseStrategy(ApproveChooseStrategyEnum.CONTINUOUS_MULTI_LEVEL_SUPERVISOR);
        chooseConfigRule.setApproveMode(ApproveModeEnum.ALL);
        chooseConfigRule.setIfEmptyStrategy(EmptyApproverStrategyEnum.AUTO_PASS);
        rule.setApprovalChooseConfigRuleList(ListUtil.toList(chooseConfigRule));
        approveRuleService.createApproveRule(rule);
    }

    private void outerOrg() {
        ApprovalInstanceConfigRule rule = new ApprovalInstanceConfigRule();
        rule.setCode(ApproveConstants.SPECIFIED_APPROVE);
        rule.setRuleName("组织内层级 + 指定审批人");
        ApprovalChooseConfigRule multiLevelRule = new ApprovalChooseConfigRule();
        multiLevelRule.setChooseStrategy(ApproveChooseStrategyEnum.CONTINUOUS_MULTI_LEVEL_SUPERVISOR);
        multiLevelRule.setApproveMode(ApproveModeEnum.ALL);
        multiLevelRule.setIfEmptyStrategy(EmptyApproverStrategyEnum.AUTO_PASS);

        ApprovalChooseConfigRule designatedRule = new ApprovalChooseConfigRule();
        designatedRule.setChooseStrategy(ApproveChooseStrategyEnum.DESIGNATED_APPROVES);
        List<String> designatedApproves = approveOrgProperty.getDesignate().getDirector();
        designatedRule.setApproveChooseConfigJson(JsonUtil.toJsonStr(designatedApproves));
        designatedRule.setApproveMode(ApproveModeEnum.ALL);
        designatedRule.setIfEmptyStrategy(EmptyApproverStrategyEnum.AUTO_PASS);

        rule.setApprovalChooseConfigRuleList(ListUtil.toList(multiLevelRule, designatedRule));
        approveRuleService.createApproveRule(rule);
    }
}
