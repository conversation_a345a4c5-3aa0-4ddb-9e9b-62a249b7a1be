package com.deepinnet.skyflow.operationcenter.service.log;

import com.deepinnet.skyflow.operationcenter.enums.OperationLogTemplateEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志上下文
 * 用于存储操作日志的模板变量
 *
 * <AUTHOR>
 */
@Data
public class OperationLogContext {

    /**
     * 操作日志模板
     */
    private OperationLogTemplateEnum template;

    /**
     * 模板变量
     */
    private Map<String, Object> variables;

    /**
     * 是否记录日志（默认true）
     */
    private boolean enabled = true;

    /**
     * 操作结果（默认成功）
     */
    private String operationResult = "SUCCESS";

    /**
     * 错误信息
     */
    private String errorMessage;

    public OperationLogContext() {
        this.variables = new HashMap<>();
    }

    public OperationLogContext(OperationLogTemplateEnum template) {
        this.template = template;
        this.variables = new HashMap<>();
    }

    /**
     * 设置模板变量
     */
    public OperationLogContext setVariable(String key, Object value) {
        this.variables.put(key, value);
        return this;
    }

    /**
     * 批量设置模板变量
     */
    public OperationLogContext setVariables(Map<String, Object> variables) {
        this.variables.putAll(variables);
        return this;
    }

    /**
     * 获取模板变量
     */
    public Object getVariable(String key) {
        return this.variables.get(key);
    }

    /**
     * 设置操作失败
     */
    public OperationLogContext setFailure(String errorMessage) {
        this.operationResult = "FAILURE";
        this.errorMessage = errorMessage;
        return this;
    }

    /**
     * 设置操作成功
     */
    public OperationLogContext setSuccess() {
        this.operationResult = "SUCCESS";
        this.errorMessage = null;
        return this;
    }

    /**
     * 禁用日志记录
     */
    public OperationLogContext disable() {
        this.enabled = false;
        return this;
    }

    /**
     * 启用日志记录
     */
    public OperationLogContext enable() {
        this.enabled = true;
        return this;
    }

    /**
     * 便捷方法：成员管理 - 添加成员
     */
    public static OperationLogContext memberAdd(String organizationName, String memberName) {
        return new OperationLogContext(OperationLogTemplateEnum.MEMBER_ADD)
                .setVariable("organizationName", organizationName)
                .setVariable("memberName", memberName);
    }

    /**
     * 便捷方法：成员管理 - 删除成员
     */
    public static OperationLogContext memberDelete(String organizationName, String memberName) {
        return new OperationLogContext(OperationLogTemplateEnum.MEMBER_DELETE)
                .setVariable("organizationName", organizationName)
                .setVariable("memberName", memberName);
    }

    /**
     * 便捷方法：成员管理 - 编辑成员
     */
    public static OperationLogContext memberEdit(String organizationName, String memberName) {
        return new OperationLogContext(OperationLogTemplateEnum.MEMBER_EDIT)
                .setVariable("organizationName", organizationName)
                .setVariable("memberName", memberName);
    }

    /**
     * 便捷方法：账号管理 - 添加账号
     */
    public static OperationLogContext accountAdd(String memberName, String accountNo) {
        return new OperationLogContext(OperationLogTemplateEnum.ACCOUNT_ADD)
                .setVariable("memberName", memberName)
                .setVariable("accountNo", accountNo);
    }

    /**
     * 便捷方法：飞行需求 - 发布需求
     */
    public static OperationLogContext demandPublish(String demandName) {
        return new OperationLogContext(OperationLogTemplateEnum.DEMAND_PUBLISH)
                .setVariable("demandName", demandName);
    }

    /**
     * 便捷方法：飞行规划 - 发布规划
     */
    public static OperationLogContext flightPlanPublish(String planName) {
        return new OperationLogContext(OperationLogTemplateEnum.FLIGHT_PLAN_PUBLISH)
                .setVariable("planName", planName);
    }

    /**
     * 便捷方法：登录成功
     */
    public static OperationLogContext loginSuccess() {
        return new OperationLogContext(OperationLogTemplateEnum.LOGIN_SUCCESS);
    }

    /**
     * 便捷方法：退出成功
     */
    public static OperationLogContext logoutSuccess() {
        return new OperationLogContext(OperationLogTemplateEnum.LOGOUT_SUCCESS);
    }
}
