package com.deepinnet.skyflow.operationcenter.service.client;

import com.deepinnet.digitaltwin.common.exception.BizException;
import com.deepinnet.digitaltwin.common.log.LogUtil;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.infra.api.client.DepartmentClient;
import com.deepinnet.infra.api.dto.UserRootDepartmentDTO;
import com.deepinnet.infra.api.dto.UserRootDepartmentQueryDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:
 * Date: 2025/6/16
 * Author: lijunheng
 */
@Component
public class FlightDepartmentClient {

    @Resource
    private DepartmentClient departmentClient;


    public List<UserRootDepartmentDTO> getUserRootDepartments(UserRootDepartmentQueryDTO queryDTO) {
        Result<List<UserRootDepartmentDTO>> result = departmentClient.getUserRootDepartments(queryDTO);
        if (!result.isSuccess()) {
            LogUtil.error("getUserRootDepartments error:{}", result.getErrorDesc());
            throw new BizException(result.getErrorCode(), result.getErrorDesc());
        }

        return result.getData();
    }
}
