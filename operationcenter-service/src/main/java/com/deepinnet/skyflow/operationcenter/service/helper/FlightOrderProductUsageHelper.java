package com.deepinnet.skyflow.operationcenter.service.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.deepinnet.skyflow.operationcenter.dal.dataobject.*;
import com.deepinnet.skyflow.operationcenter.dal.repository.*;
import com.deepinnet.skyflow.operationcenter.dto.FlightProductDTO;
import com.deepinnet.skyflow.operationcenter.enums.OrderTypeEnum;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightCycleConvert;
import com.deepinnet.skyflow.operationcenter.service.convert.FlightOrderConvert;
import com.deepinnet.skyflow.operationcenter.service.product.FlightProductService;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderFlyingInfoVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderProductUsageVO;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 飞行订单产品使用记录帮助类
 */

@Component
public class FlightOrderProductUsageHelper {

    @Resource
    private FlightOrderProductUsageRepository flightOrderProductUsageRepository;

    @Resource
    private FlightProductService flightProductService;

    @Resource
    private FlightOrderFileRepository flightOrderFileRepository;

    @Resource
    private FlightOrderFlyingInfoRepository flightOrderFlyingInfoRepository;

    @Resource
    private FlightOrderConvert flightOrderConvert;

    @Resource
    private FlightOrderApprovalRepository flightOrderApprovalRepository;

    @Resource
    private FlightPositionRepository flightPositionRepository;

    @Resource
    private FlightPlanCycleRepository flightPlanCycleRepository;

    @Resource
    private FlightCycleConvert flightCycleConvert;

    @Resource
    private FlightDemandRepository flightDemandRepository;

    /**
     * 批量查询产品使用记录
     *
     * @param orderNoList 订单号列表
     * @param tenantId    租户ID
     * @return 产品使用记录列表
     */
    public List<FlightOrderProductUsageDO> batchQueryProductUsage(List<String> orderNoList, String tenantId) {
        return flightOrderProductUsageRepository.list(
                Wrappers.lambdaQuery(FlightOrderProductUsageDO.class)
                        .in(FlightOrderProductUsageDO::getOrderNo, orderNoList)
                        .eq(FlightOrderProductUsageDO::getTenantId, tenantId)
                        .orderByDesc(FlightOrderProductUsageDO::getGmtCreated)
                        .orderByDesc(FlightOrderProductUsageDO::getId)
        );
    }

    /**
     * 批量查询飞行信息
     *
     * @param orderNoList 订单号列表
     * @param tenantId    租户ID
     * @return 飞行信息
     */
    public List<FlightOrderFlyingInfoDO> batchQueryFlyingInfo(List<String> orderNoList, String tenantId) {
        return flightOrderFlyingInfoRepository.list(
                Wrappers.lambdaQuery(FlightOrderFlyingInfoDO.class)
                        .in(FlightOrderFlyingInfoDO::getOrderNo, orderNoList)
                        .eq(FlightOrderFlyingInfoDO::getTenantId, tenantId)
                        .orderByDesc(FlightOrderFlyingInfoDO::getGmtCreated)
                        .orderByDesc(FlightOrderFlyingInfoDO::getId)
        );
    }

    /**
     * 设置产品信息到产品使用记录
     *
     * @param productUsageVOList 产品使用记录VO列表
     */
    public void setProductInfo(List<FlightOrderProductUsageVO> productUsageVOList) {
        if (CollUtil.isEmpty(productUsageVOList)) {
            return;
        }

        List<String> productNoList = productUsageVOList.stream()
                .map(FlightOrderProductUsageVO::getProductNo)
                .collect(Collectors.toList());

        List<FlightProductDTO> flightProductDTOS = flightProductService.listByProductNoList(productNoList);

        if (CollUtil.isNotEmpty(flightProductDTOS)) {
            Map<String, FlightProductDTO> productNoMap = flightProductDTOS.stream()
                    .collect(Collectors.toMap(FlightProductDTO::getProductNo, Function.identity()));

            productUsageVOList.forEach(productUsage ->
                    productUsage.setFlightProduct(productNoMap.get(productUsage.getProductNo()))
            );
        }
    }

    /**
     * 设置产品使用记录和产品信息
     *
     * @param flightOrderVO 订单VO
     * @param orderNo       订单号
     * @param tenantId      租户ID
     */
    public void setProductUsageAndProductInfo(FlightOrderVO flightOrderVO, String orderNo, String tenantId) {
        List<FlightOrderProductUsageDO> productUsageDOList = batchQueryProductUsage(List.of(orderNo), tenantId);
        //Integer totalUsedCount = 0;
        if (CollUtil.isNotEmpty(productUsageDOList)) {
            List<FlightOrderProductUsageVO> productUsageVOList = productUsageDOList.stream()
                    .map(flightOrderConvert::convertToProductUsageVO)
                    .collect(Collectors.toList());

            setProductInfo(productUsageVOList);
            flightOrderVO.setProductUsageList(productUsageVOList);
            // totalUsedCount = productUsageVOList.stream().map(FlightOrderProductUsageVO::getUseQuantity).reduce(Integer::sum).orElse(0);
        }

        List<FlightOrderFileDO> list = flightOrderFileRepository.list(Wrappers.<FlightOrderFileDO>lambdaQuery()
                .eq(FlightOrderFileDO::getOrderNo, orderNo));

        if (CollUtil.isNotEmpty(list)) {
            flightOrderVO.setFiles(flightOrderConvert.convertToFlightOrderFileList(list));
        }

        FlightOrderFlyingInfoDO flyingInfoDO = getFlightOrderFlyingInfoDO(orderNo);

        if (ObjectUtil.isNotNull(flyingInfoDO)) {
            Integer totalUsedCount = getTotalUsedCount(orderNo);
            FlightOrderFlyingInfoVO flightOrderFlyingInfoVO = flightOrderConvert.convertToFlightOrderFlyingInfoVO(flyingInfoDO);
            // 通过飞行用量列表，设置已用和剩余次数
            flightOrderFlyingInfoVO.setFlyingNumUsed(totalUsedCount);
            flightOrderFlyingInfoVO.setFlyingNumRemained(flightOrderFlyingInfoVO.getFlyingNum() - totalUsedCount);
            flightOrderVO.setFlyingInfo(flightOrderFlyingInfoVO);
        }

        List<FlightOrderApprovalDO> approvalInfoList = Lists.newArrayList();

        if (StrUtil.equals(flightOrderVO.getOrderType(), OrderTypeEnum.NORMAL.getCode())) {
            approvalInfoList = flightOrderApprovalRepository.list(Wrappers.<FlightOrderApprovalDO>lambdaQuery()
                    .eq(FlightOrderApprovalDO::getOrderNo, orderNo)
                    .orderByAsc(FlightOrderApprovalDO::getApprovalTime));
        }

        if (CollUtil.isNotEmpty(approvalInfoList)) {
            flightOrderVO.setApprovalInfos(flightOrderConvert.convertToFlightOrderApprovalListVO(approvalInfoList));
        }

        List<FlightPositionDO> positionList = flightPositionRepository.list(Wrappers.<FlightPositionDO>lambdaQuery()
                .eq(FlightPositionDO::getOrderNo, orderNo));

        if (CollUtil.isNotEmpty(positionList)) {
            flightOrderVO.setFlightPositions(flightOrderConvert.convertToFlightPositionVOList(positionList));
        }

        if (StrUtil.isEmpty(flightOrderVO.getCycleNo())) {
            return;
        }

        // 塞入周期计划信息
        FlightPlanCycleDO cycleDO = flightPlanCycleRepository.getOne(Wrappers.<FlightPlanCycleDO>lambdaQuery()
                .eq(FlightPlanCycleDO::getCycleNo, flightOrderVO.getCycleNo()));
        if (ObjectUtil.isNotNull(cycleDO)) {
            flightOrderVO.setCyclePlanInfo(flightCycleConvert.toVO(cycleDO));
        }
    }

    /**
     * 获取总剩余次数
     * @param orderNo
     * @return
     */
    public Integer getTotalRemainedCount(String orderNo) {
        Integer totalUsedCount = getTotalUsedCount(orderNo);
        FlightOrderFlyingInfoDO flightOrderFlyingInfoDO = getFlightOrderFlyingInfoDO(orderNo);
        return flightOrderFlyingInfoDO.getFlyingNum() - totalUsedCount;
    }

    /**
     * 获取总已用次数
     * @param orderNo
     * @return
     */
    public Integer getTotalUsedCount(String orderNo) {
        List<FlightDemandDO> flightDemandDOS = flightDemandRepository.list(Wrappers.lambdaQuery(FlightDemandDO.class).eq(FlightDemandDO::getFlightOrderNo, orderNo));
        Integer totalUsedCount = Optional.ofNullable(flightDemandDOS).orElse(Collections.emptyList()).stream().map(FlightDemandDO::getTotalFlyingNum).reduce(Integer::sum).get();
        return totalUsedCount;
    }

    /**
     * 获取飞行信息
     * @param orderNo
     * @return
     */
    public FlightOrderFlyingInfoDO getFlightOrderFlyingInfoDO(String orderNo) {
        FlightOrderFlyingInfoDO flyingInfoDO = flightOrderFlyingInfoRepository.getOne(Wrappers.<FlightOrderFlyingInfoDO>lambdaQuery()
                .eq(FlightOrderFlyingInfoDO::getOrderNo, orderNo));
        return flyingInfoDO;
    }

    /**
     * 批量设置产品使用记录和产品信息
     *
     * @param flightOrderList 订单VO列表
     * @param tenantId        租户ID
     */
    public void batchSetProductUsageAndProductInfo(List<FlightOrderVO> flightOrderList, String tenantId) {
        if (CollUtil.isEmpty(flightOrderList)) {
            return;
        }

        List<String> orderNoList = flightOrderList.stream()
                .map(FlightOrderVO::getOrderNo)
                .collect(Collectors.toList());

        List<String> cycleNoList = flightOrderList.stream()
                .map(FlightOrderVO::getCycleNo)
                .collect(Collectors.toList());

        List<FlightOrderProductUsageDO> productUsageDOList = batchQueryProductUsage(orderNoList, tenantId);

        List<FlightOrderFlyingInfoDO> flightOrderFlyingInfoDOS = batchQueryFlyingInfo(orderNoList, tenantId);

        List<FlightPositionDO> flightPositionDOS = batchQueryFlightPosition(orderNoList, tenantId);

        List<FlightPlanCycleDO> cycleDOList = batchQueryFlightCyclePlanList(cycleNoList, tenantId);

        Set<String> allProductNos = Optional.ofNullable(productUsageDOList)
                .orElse(Collections.emptyList())
                .stream()
                .map(FlightOrderProductUsageDO::getProductNo)
                .collect(Collectors.toSet());

        Map<String, FlightProductDTO> productMap;
        if (CollUtil.isNotEmpty(allProductNos)) {
            productMap = flightProductService.listByProductNoList(new ArrayList<>(allProductNos))
                    .stream()
                    .collect(Collectors.toMap(FlightProductDTO::getProductNo, Function.identity()));
        } else {
            productMap = Collections.emptyMap();
        }

        Map<String, List<FlightOrderProductUsageDO>> orderProductUsageMap =
                (productUsageDOList == null || productUsageDOList.isEmpty()) ?
                        Collections.emptyMap() :
                        productUsageDOList.stream().collect(Collectors.groupingBy(FlightOrderProductUsageDO::getOrderNo));

        Map<String, FlightOrderFlyingInfoDO> orderFlyingInfoMap =
                (flightOrderFlyingInfoDOS == null || flightOrderFlyingInfoDOS.isEmpty()) ?
                        Collections.emptyMap() :
                        flightOrderFlyingInfoDOS.stream().collect(Collectors.toMap(FlightOrderFlyingInfoDO::getOrderNo, Function.identity()));

        Map<String, List<FlightPositionDO>> flightPositionMap =
                (flightPositionDOS == null || flightPositionDOS.isEmpty()) ?
                        Collections.emptyMap() :
                        flightPositionDOS.stream().collect(Collectors.groupingBy(FlightPositionDO::getOrderNo));

        Map<String, FlightPlanCycleDO> flightPlanCycleDOMap = (cycleDOList == null || cycleDOList.isEmpty()) ?
                Collections.emptyMap() :
                cycleDOList.stream().collect(Collectors.toMap(FlightPlanCycleDO::getCycleNo, Function.identity()));

        flightOrderList.forEach(order -> {
            String orderNo = order.getOrderNo();

            List<FlightOrderProductUsageDO> usageList = orderProductUsageMap.get(orderNo);
            if (CollUtil.isNotEmpty(usageList)) {
                List<FlightOrderProductUsageVO> productUsageVOList = usageList.stream()
                        .map(flightOrderConvert::convertToProductUsageVO)
                        .peek(vo -> vo.setFlightProduct(productMap.get(vo.getProductNo()))) // O(1) 查 Map
                        .collect(Collectors.toList());

                order.setProductUsageList(productUsageVOList);
            }

            FlightOrderFlyingInfoDO flyingInfoDO = orderFlyingInfoMap.get(orderNo);
            if (ObjectUtil.isNotNull(flyingInfoDO)) {
                order.setFlyingInfo(flightOrderConvert.convertToFlightOrderFlyingInfoVO(flyingInfoDO));
            }

            List<FlightPositionDO> flightPositionDOList = flightPositionMap.get(orderNo);
            if (CollUtil.isNotEmpty(flightPositionDOList)) {
                order.setFlightPositions(flightOrderConvert.convertToFlightPositionVOList(flightPositionDOList));
            }

            FlightPlanCycleDO cycleDO = flightPlanCycleDOMap.get(order.getCycleNo());
            if (ObjectUtil.isNotNull(cycleDO)) {
                order.setCyclePlanInfo(flightCycleConvert.toVO(cycleDO));
            }
        });
    }

    private List<FlightPlanCycleDO> batchQueryFlightCyclePlanList(List<String> cycleNoList, String tenantId) {
        if (CollUtil.isEmpty(cycleNoList)) {
            return Collections.emptyList();
        }
        return flightPlanCycleRepository.list(Wrappers.<FlightPlanCycleDO>lambdaQuery()
                .in(FlightPlanCycleDO::getCycleNo, cycleNoList)
                .eq(FlightPlanCycleDO::getTenantId, tenantId));
    }

    private List<FlightPositionDO> batchQueryFlightPosition(List<String> orderNoList, String tenantId) {
        return flightPositionRepository.list(Wrappers.<FlightPositionDO>lambdaQuery()
                .in(FlightPositionDO::getOrderNo, orderNoList)
                .eq(FlightPositionDO::getTenantId, tenantId));
    }
} 