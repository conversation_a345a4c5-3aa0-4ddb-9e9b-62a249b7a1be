package com.deepinnet.skyflow.operationcenter.service.rule;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * Description:
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Component
@Slf4j
public class RuleEngine {

    @Resource
    private List<IRule<?>> ruleList;

    // 不同 RuleType 对应的规则列表
    private final Map<RuleTypeEnum, List<IRule<?>>> ruleTypeEnumListMap = new EnumMap<>(RuleTypeEnum.class);

    @PostConstruct
    public void init() {
        ruleList.forEach(rule -> {
            ruleTypeEnumListMap.computeIfAbsent(rule.supportRuleType(), k -> new ArrayList<>()).add(rule);
        });

        ruleTypeEnumListMap.values().forEach(list ->
                list.sort(Comparator.comparingInt(IRule::order))
        );
    }

    @SuppressWarnings("unchecked")
    public <T extends RuleResult> RuleContext<T> execute(RuleTypeEnum ruleType, Object inputParam, String tenantId) {
        List<IRule<?>> rules = ruleTypeEnumListMap.get(ruleType);
        if (CollectionUtil.isEmpty(rules)) {
            log.warn("未找到规则类型 [{}] 对应的规则", ruleType);
            return new RuleContext<>();
        }

        RuleContext<T> ruleContext = new RuleContext<>();
        ruleContext.setTenantId(tenantId);
        ruleContext.setInputParam(inputParam);
        log.info("规则引擎开始执行");
        int ruleIndex = 0;
        for (IRule<?> rawRule : ruleTypeEnumListMap.get(ruleType)) {
            IRule<T> rule = (IRule<T>) rawRule;
            if (!rule.canExec(ruleContext)) {
                continue;
            }
            ruleContext.setRuleIndex(ruleIndex++);
            T result = rule.apply(ruleContext);
            ruleContext.addRuleExecResult(rule.getClass(), result);
            ruleContext.setPreviousRuleExecResult(result);
        }
        log.info("规则引擎执行完毕，结果是：{}", ruleContext.getPreviousRuleExecResult());
        return ruleContext;
    }
}
