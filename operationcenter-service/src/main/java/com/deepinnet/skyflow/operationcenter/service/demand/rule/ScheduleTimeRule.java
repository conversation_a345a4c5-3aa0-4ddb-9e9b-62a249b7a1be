package com.deepinnet.skyflow.operationcenter.service.demand.rule;

import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.RoutineInspectionFlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlyingFrequencyEnum;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleContext;
import com.deepinnet.skyflow.operationcenter.service.util.BizTimeEntity;
import com.deepinnet.skyflow.operationcenter.service.util.MergedScheduleTimeEntity;
import com.deepinnet.skyflow.operationcenter.service.util.ScheduleTimeMergerUtil;
import com.deepinnet.skyflow.operationcenter.service.util.TimeFrequencyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.DayOfWeek;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2025/6/18
 * Author: lijunheng
 */
@Slf4j
@Component
public class ScheduleTimeRule extends AbstractDemandMergeRule {

    @Override
    protected DemandMergeRuleResult execute(RuleContext<DemandMergeRuleResult> context) {
        DemandMergeRuleResult previousRuleExecResult = context.getPreviousRuleExecResult();
        List<List<FlightDemandDTO>> fromFilterDemandGroup = previousRuleExecResult.getFilterDemandGroup();

        List<List<FlightDemandDTO>> toFilterDemandGroup = new ArrayList<>();
        List<DemandGroupWithSchedule> demandGroupsWithSchedule = new ArrayList<>();
        
        for (List<FlightDemandDTO> demandGroup : fromFilterDemandGroup) {
            if (CollectionUtils.isEmpty(demandGroup) || demandGroup.size() < 2) {
                // 单个需求或空分组直接过滤掉
                continue;
            }
            // 每个分组内的时间进行合并
            List<MergedScheduleTimeEntity> mergedScheduleTimeEntityList = mergeScheduleTime(demandGroup);

            //过滤掉孤立的时间组
            List<MergedScheduleTimeEntity> filterIntersectionMergeList = filterIntersectionMerge(mergedScheduleTimeEntityList);

            Map<String, FlightDemandDTO> demandDTOMap = demandGroup.stream().collect(Collectors.toMap(FlightDemandDTO::getDemandNo, Function.identity()));

            filterIntersectionMergeList.forEach(filterTimeMergeGroup -> {
                Set<String> demandCodeSet = filterTimeMergeGroup.getSourceBizEntityIds();
                List<FlightDemandDTO> filteredDemandList = demandCodeSet.stream().map(demandCode -> demandDTOMap.get(demandCode)).collect(Collectors.toList());
                toFilterDemandGroup.add(filteredDemandList);
                
                // 创建带时间周期信息的需求分组
                DemandGroupWithSchedule groupWithSchedule = new DemandGroupWithSchedule(filteredDemandList, filterTimeMergeGroup);
                demandGroupsWithSchedule.add(groupWithSchedule);
            });
        }

        DemandMergeRuleResult ruleResult = new DemandMergeRuleResult();
        ruleResult.setFilterDemandGroup(toFilterDemandGroup);
        ruleResult.setDemandGroupsWithSchedule(demandGroupsWithSchedule);
        return ruleResult;
    }

    @Override
    public int order() {
        return 500;
    }

    /**
     * 判断需求分组是否可以进行时间合并
     *
     * @param demandGroup 需求分组
     * @return true表示可以合并，false表示不能合并
     */
    private List<MergedScheduleTimeEntity> mergeScheduleTime(List<FlightDemandDTO> demandGroup) {
        try {
            // 将需求转换为时间实体列表
            List<BizTimeEntity> bizTimeEntities = demandGroup.stream()
                    .map(this::convertToBizTimeEntity)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (bizTimeEntities.size() != demandGroup.size()) {
                // 如果有需求无法转换为时间实体，则认为无法合并
                return new ArrayList<>();
            }

            // 使用时间合并工具进行合并
            return ScheduleTimeMergerUtil.mergeBizEntity(bizTimeEntities);
        } catch (Exception e) {
            // 如果合并过程出现异常，认为无法合并
            log.error("Failed to merge schedule time", e);
            throw new RuntimeException("Failed to merge schedule time", e);
        }
    }

    /**
     * 将飞行需求转换为业务时间实体
     *
     * @param demand 飞行需求
     * @return 业务时间实体，如果转换失败返回null
     */
    private BizTimeEntity convertToBizTimeEntity(FlightDemandDTO demand) {
        try {
            // 目前只支持日常巡检类型的需求时间合并
            if (demand.getType() != FlightDemandTypeEnum.ROUTINE_INSPECTION) {
                return null;
            }

            RoutineInspectionFlightDemandDTO routineDetail = demand.getRoutineInspectionDetail();
            if (routineDetail == null) {
                return null;
            }

            BizTimeEntity bizTimeEntity = new BizTimeEntity();
            bizTimeEntity.setId(demand.getDemandNo());
            bizTimeEntity.setStartDate(routineDetail.getDemandStartTime());
            bizTimeEntity.setEndDate(routineDetail.getDemandEndTime());
            //立即起飞场景下这两个起飞时间为空，默认为10点到11点
            if (routineDetail.getFlyingStartTime() == null) {
                bizTimeEntity.setStartTime(LocalTime.of(10, 0, 0));
                bizTimeEntity.setEndTime(LocalTime.of(11, 0, 0));
            } else {
                bizTimeEntity.setStartTime(routineDetail.getFlyingStartTime());
                bizTimeEntity.setEndTime(routineDetail.getFlyingEndTime());
            }

            // 设置频率
            FlyingFrequencyEnum flyingFrequency = routineDetail.getFlyingFrequency();
            Set<DayOfWeek> repeatDays = TimeFrequencyUtil.getDayOfWeekSetFromFrequency(flyingFrequency);
            bizTimeEntity.setRepeatDays(repeatDays);
            return bizTimeEntity;

        } catch (Exception e) {
            throw new RuntimeException("Failed to convert demand to time entity", e);
        }
    }

    /**
     * 过滤出时间组之间的元素有交集的时间组
     *
     * @param mergedScheduleTimeEntityList
     * @return
     */
    private List<MergedScheduleTimeEntity> filterIntersectionMerge(List<MergedScheduleTimeEntity> mergedScheduleTimeEntityList) {
        List<MergedScheduleTimeEntity> filterIntersectionMergeList = new ArrayList<>();
        for (int i = 0; i < mergedScheduleTimeEntityList.size(); i++) {
            Set<String> sourceIds = mergedScheduleTimeEntityList.get(i).getSourceBizEntityIds();
            if (sourceIds.size() > 1) {
                filterIntersectionMergeList.add(mergedScheduleTimeEntityList.get(i));
            } else {
                for (int j = 0; j < mergedScheduleTimeEntityList.size(); j++) {
                    Set<String> targetIds = mergedScheduleTimeEntityList.get(j).getSourceBizEntityIds();
                    if (i != j && sourceIds.stream().anyMatch(targetIds::contains)) {
                        filterIntersectionMergeList.add(mergedScheduleTimeEntityList.get(i));
                        break;
                    }
                }
            }
        }
        return filterIntersectionMergeList;
    }
}
