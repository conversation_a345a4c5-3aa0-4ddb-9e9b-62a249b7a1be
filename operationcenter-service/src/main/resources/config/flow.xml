<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE flow PUBLIC "liteflow" "liteflow.dtd">
<flow>

    <!-- 审核订单 -->
    <chain name="approveOrder">
        THEN(
            flightOrderDetailQueryNode,
            SWITCH(flightOrderTypeApproveSwitchNode).to(APPROVE_NORMAL)
        );
    </chain>

    <!-- 审核普通订单 -->
    <chain name="APPROVE_NORMAL">
        THEN(flightOrderApproveNode);
    </chain>

    <!-- 审核飞行规划单 -->
    <chain name="APPROVE_DEMAND_PLAN">
        THEN(flightDemandOrderApproveNode);
    </chain>

    <!-- 下单 -->
    <chain name="createOrder">
        SWITCH(flightOrderTypeSwitchNode).to(NORMAL, DEMAND_PLAN);
    </chain>

    <chain name="NORMAL">
        THEN(userCheckNode, flightOrderProductCheckNode, flightOrderCreateNode);
    </chain>

    <chain name="DEMAND_PLAN">
        THEN(userCheckNode,
            flightOrderProductCheckNode,
            IF(flightOrderCheckCreateOrUpdateNode, flightOrderCreateNode, flightOrderUpdateNode)
        );
    </chain>
</flow>