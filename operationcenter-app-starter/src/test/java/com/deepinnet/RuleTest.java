package com.deepinnet;

import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.lock.DatabaseDistributedLock;
import com.deepinnet.skyflow.operationcenter.OperationCenterApplication;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandAreaDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.service.context.TenantContext;
import com.deepinnet.skyflow.operationcenter.service.demand.FlightDemandService;
import com.deepinnet.skyflow.operationcenter.service.demand.ServiceProviderClient;
import com.deepinnet.skyflow.operationcenter.service.demand.rule.AreaDistanceRule;
import com.deepinnet.skyflow.operationcenter.service.demand.rule.DemandMergeRuleResult;
import com.deepinnet.skyflow.operationcenter.service.helper.DemandMergeRuleHelper;
import com.deepinnet.skyflow.operationcenter.service.helper.DemandTypeDistanceConfigItem;
import com.deepinnet.skyflow.operationcenter.service.rule.RuleContext;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description:
 * Date: 2025/5/15
 * Author: lijunheng
 */
@Slf4j
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OperationCenterApplication.class)
public class RuleTest {

    @Resource
    private DemandMergeRuleHelper demandMergeRuleHelper;

    @Resource
    private DatabaseDistributedLock databaseDistributedLock;

    @Resource
    private FlightDemandService flightDemandService;

    @Resource
    private ServiceProviderClient serviceProviderClient;

    @Resource
    private AreaDistanceRule areaDistanceRule;

    @Test
    public void testGetDemandTypeAndDistanceConfigItem() {
        List<DemandTypeDistanceConfigItem> demandTypeAndDistanceConfigItem = demandMergeRuleHelper.getDemandTypeAndDistanceConfigItem("deepinnet");
        List<String> allowDemandTypeList = demandMergeRuleHelper.getAllowDemandTypeList(demandTypeAndDistanceConfigItem);
        System.out.println(allowDemandTypeList);
    }

    @Test
    public void testGetDemandTypeDistanceLimit() {
        String json = "[\n" +
                "{\n" +
                "                \"demandCode\": \"FLIGHT_DEMAND_a0f7c09137ca48379690d1d9f6849d6f\",\n" +
                "                \"demandName\": \"6.23-合并1\",\n" +
                "                \"sequence\": null,\n" +
                "                \"centerPointLongitude\": \"114.22549208061866\",\n" +
                "                \"centerPointLatitude\": \"22.70038602627259\",\n" +
                "                \"areaCoordinate\": \"POLYGON((114.22343214409483 22.70432153514497, 114.22926863091152 22.704163175891438, 114.2289253081571 22.697398572550625, 114.22394712822637 22.69645051740021, 114.22326048271964 22.69645051740021, 114.22171553032581 22.698984386162664, 114.22343214409483 22.70432153514497))\",\n" +
                "                \"area\": 0.5543997572846519\n" +
                "            },\n" +
                "            {\n" +
                "                \"demandCode\": \"FLIGHT_DEMAND_00efa34a1354469c99e82ffcf51484e7\",\n" +
                "                \"demandName\": \"6.23-合并2\",\n" +
                "                \"sequence\": null,\n" +
                "                \"centerPointLongitude\": \"114.21483774508437\",\n" +
                "                \"centerPointLatitude\": \"22.696803649260616\",\n" +
                "                \"areaCoordinate\": \"POLYGON((114.2099453958408 22.699971026936126, 114.21973009432793 22.70076284560868, 114.21818514193421 22.692844452912553, 114.21046037997138 22.693636312778096, 114.21028871859522 22.694586538575024, 114.2099453958408 22.699971026936126))\",\n" +
                "                \"area\": 0.7150963999421742\n" +
                "            },\n" +
                "            {\n" +
                "                \"demandCode\": \"FLIGHT_DEMAND_a0f7c09137ca48379690d1d9f6849d6f\",\n" +
                "                \"demandName\": \"6.23-合并1\",\n" +
                "                \"sequence\": null,\n" +
                "                \"centerPointLongitude\": \"114.21334137311797\",\n" +
                "                \"centerPointLatitude\": \"22.69692020484449\",\n" +
                "                \"areaCoordinate\": \"POLYGON((114.2099453958408 22.700287754953877, 114.2202450784585 22.70092120879316, 114.21961654801794 22.69542457550702, 114.21828230278692 22.692919200895815, 114.21347578423143 22.694027802821736, 114.20643766777744 22.694819655848107, 114.2099453958408 22.700287754953877))\",\n" +
                "                \"area\": 0.8804431022005701\n" +
                "            }\n" +
                "\n" +
                "]";
        List<FlightDemandAreaDTO> flightDemandAreaDTOS = JsonUtil.parseToList(json, FlightDemandAreaDTO.class);
//        FlightRoutePlan flightRoutePlan = calculateFlightRoutePlan(flightDemandAreaDTOS);
//        System.out.println(flightRoutePlan);
    }

    @Test
    public void testLock() throws InterruptedException {
        TenantContext.disableTenantLine();
        boolean lock = databaseDistributedLock.tryLock("test2222");
        System.out.println(lock);
        new Thread(() -> {
            TenantContext.disableTenantLine();
            Boolean lock2 = databaseDistributedLock.tryLock("test2222");
            System.out.println(lock2);
        }).start();
        Thread.sleep(10000000L);
    }

    @Test
    public void test() {
        String json = "[\n" +
                "  [\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_b4e3ea37f7ae421d9a852c1657dd6afd\",\n" +
                "      \"demandName\": \"12222\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_8849074ad14344b6998ba43b2554b52b\",\n" +
                "      \"demandName\": \"3-7.03-7.16 上午\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_79f796a08e6941b6809216cd260ba536\",\n" +
                "      \"demandName\": \"长时间\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_894a1f15f30940d1b33efb217bdb2a44\",\n" +
                "      \"demandName\": \"需求名称16\"\n" +
                "    }\n" +
                "  ],\n" +
                "  [\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_91600623121d41fb953fe6b789fd278b\",\n" +
                "      \"demandName\": \"6 月 26 晚上创建的需求 1\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_81b1a42f86094da5b9510db53b5a9b95\",\n" +
                "      \"demandName\": \"6 月 26 日晚上创建的需求-单区域\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_0242f4ec283c453b9e33a3813b2dcbba\",\n" +
                "      \"demandName\": \"6 月 26 晚上创建的需求 2-多区域\"\n" +
                "    }\n" +
                "  ],\n" +
                "  [\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_a291ec7f1ccb47f7b26af7a37e6b70f5\",\n" +
                "      \"demandName\": \"3个增值服务飞行次数 1 次\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_56105fdf66444f238e445eaec6f28fa0\",\n" +
                "      \"demandName\": \"3 个增值服务飞行次数 1-02\"\n" +
                "    }\n" +
                "  ],\n" +
                "  [\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_0763769848d443fca77566aae59a6202\",\n" +
                "      \"demandName\": \"无增值服务 -下午时间段\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_739d839e98d64eb5be10936f8b19730d\",\n" +
                "      \"demandName\": \"无增值服务 3 下午\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_8fceee1b25934defb61a8fd486ab69e4\",\n" +
                "      \"demandName\": \"626-02晚上创建的需求1-多区域\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_01f55eaa681f4868a254d22792ff9cb2\",\n" +
                "      \"demandName\": \"626-02晚上创建的需求1-多区域\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_f6d78cd2bb8f41458f313ab1b2bf0719\",\n" +
                "      \"demandName\": \"无增值服务下午 3\"\n" +
                "    }\n" +
                "  ],\n" +
                "  [\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_2c545321a4d74b60814cfa385d9593e8\",\n" +
                "      \"demandName\": \"3个增值服务飞行次数 2 次 2\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"demandCode\": \"FLIGHT_DEMAND_c24bb62675ca4426a0d2288481448eff\",\n" +
                "      \"demandName\": \"3个增值服务飞行次数 2 次\"\n" +
                "    }\n" +
                "  ]\n" +
                "]";
        List<List> maps = JsonUtil.parseToList(json, List.class);
        List<List<FlightDemandDTO>> flightDemandDTOS = maps.stream().map(map -> {
            return JsonUtil.parseToList(JsonUtil.toJsonStr(map), Map.class).stream().map(innerMap -> {
                String demandNo = (String) innerMap.get("demandCode");
                return flightDemandService.getFlightDemandByNo(demandNo);
            }).collect(Collectors.toList());
        }).collect(Collectors.toList());
        RuleContext context = new RuleContext<>();
        DemandMergeRuleResult ruleResult = new DemandMergeRuleResult();
        ruleResult.setFilterDemandGroup(flightDemandDTOS);
        context.setPreviousRuleExecResult(ruleResult);
        context.setRuleIndex(2);
        context.setTenantId("sz_unifly");
        DemandMergeRuleResult result = areaDistanceRule.apply(context);
        System.out.println(result.getFilterDemandGroup());
    }

    @Test
    public void testSyncDemandToProvider() {
        String demandCode = "FLIGHT_DEMAND_91178d65608d4a53923b64aa772b040a";
        FlightDemandDTO flightDemandByNo = flightDemandService.getFlightDemandByNo(demandCode);
        String syncOrder = serviceProviderClient.syncDemandToProvider(flightDemandByNo, "uid_1927930294560292864");
        System.out.println(syncOrder);
    }
}

