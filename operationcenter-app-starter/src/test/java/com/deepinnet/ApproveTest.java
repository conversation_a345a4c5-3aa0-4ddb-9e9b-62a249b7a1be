package com.deepinnet;

import com.deepinnet.digitaltwin.common.util.JsonUtil;
import com.deepinnet.infra.api.dto.SimpleDepartmentDTO;
import com.deepinnet.skyflow.operationcenter.OperationCenterApplication;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveInstanceService;
import com.deepinnet.skyflow.operationcenter.service.approval.ApproveRuleService;
import com.deepinnet.skyflow.operationcenter.service.approval.entity.*;
import com.deepinnet.skyflow.operationcenter.service.approval.property.ApproveOrgProperty;
import com.deepinnet.skyflow.operationcenter.service.client.UserRemoteClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * Description:
 * Date: 2025/5/15
 * Author: lijunheng
 */
@Slf4j
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OperationCenterApplication.class)
public class ApproveTest {

    @Resource
    private UserRemoteClient userService;

    @Resource
    private ApproveOrgProperty approveOrgProperty;

    @Resource
    private ApproveInstanceService approveInstanceService;

    @Resource
    private ApproveRuleService approveRuleService;

    @Test
    public void testApproveUserInfo() {
        SimpleDepartmentDTO userRelatedDepartment = userService.getUserRelatedDepartment("uid_1948580084737343488");
        log.info("用户部门信息: {}", userRelatedDepartment);
    }

    @Test
    public void testApprove() {
        List<ApprovalRecord> approvalRecordMap = approveInstanceService.getApprovalRecordMap("1950469786147438592");
        log.info("审批记录: {}", approvalRecordMap);
    }

    @Test
    public void testCreateApprovalRule() {
        // 创建审批规则
        ApprovalInstanceConfigRule rule = new ApprovalInstanceConfigRule();
        rule.setCode("TEST_RULE_001");
        rule.setRuleName("测试审批规则");
        rule.setRuleType("ORGANIZATION");
        rule.setOrgCode("ORG_001");
        rule.setCreator("test_user");
        rule.setEditor("test_user");

        // 创建审批选择配置
        ApprovalChooseConfigRule chooseConfigRule = new ApprovalChooseConfigRule();
        chooseConfigRule.setChooseStrategy(ApproveChooseStrategyEnum.CONTINUOUS_MULTI_LEVEL_SUPERVISOR);
        chooseConfigRule.setApproveMode(ApproveModeEnum.ALL);
        chooseConfigRule.setIfEmptyStrategy(EmptyApproverStrategyEnum.AUTO_PASS);
        chooseConfigRule.setApproveChooseConfigJson("{\"maxLevel\":3}");

        rule.setApprovalChooseConfigRuleList(Arrays.asList(chooseConfigRule));

        // 保存规则
        String ruleCode = approveRuleService.createApproveRule(rule);
        log.info("创建审批规则成功，规则编码: {}", ruleCode);
    }

    @Test
    public void testExecuteApprovalRule() {
        // 创建审批提交实体
        ApproveSubmitEntity submitEntity = new ApproveSubmitEntity();
        submitEntity.setBizType("FLIGHT_DEMAND");
        submitEntity.setBizId("DEMAND_001");
        submitEntity.setSubmitUserId("user_001");
        submitEntity.setTenantId("tenant_001");

        // 创建规则执行实体
        ApproveRuleExecEntity execEntity = new ApproveRuleExecEntity();
        execEntity.setInstanceConfigRuleCode("TEST_RULE_001");
        execEntity.setApproveSubmitEntity(submitEntity);

        // 执行审批规则
        ApprovalInstance approvalInstance = approveRuleService.executeApproveRule(execEntity);

        log.info("执行审批规则成功，审批实例: {}", JsonUtil.toPrettyJsonStr(approvalInstance));
    }

    @Test
    public void testCreateAndExecuteRule() {
        // 1. 创建审批规则
        testCreateApprovalRule();

        // 2. 执行审批规则
        testExecuteApprovalRule();
    }
    
}

