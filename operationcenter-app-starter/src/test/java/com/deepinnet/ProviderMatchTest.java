package com.deepinnet;

import com.deepinnet.skyflow.operationcenter.OperationCenterApplication;
import com.deepinnet.skyflow.operationcenter.dto.EmergencyResponseFlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.RoutineInspectionFlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.service.demand.ServiceProviderClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * Description:
 * Date: 2025/4/24
 * Author: lijunheng
 */
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OperationCenterApplication.class)
public class ProviderMatchTest {

    private FlightDemandDTO baseDemandDTO;
    private RoutineInspectionFlightDemandDTO routineDemandDTO;
    private EmergencyResponseFlightDemandDTO emergencyDemandDTO;
    private List<String> flightUavBmList;

    @Resource
    private ServiceProviderClient serviceProviderClient;

    @Before
    public void setUp() {
        // 基础需求
        baseDemandDTO = new FlightDemandDTO();
        baseDemandDTO.setPublisherNo("test_publisher_001");
        baseDemandDTO.setFlightUavBm("test_uav_001");

        // 飞行器列表
        flightUavBmList = Arrays.asList("test_uav_001", "test_uav_002");
    }

    @Test
    public void testMatchProvider() {
        String result = serviceProviderClient.getBestMatchServiceProvider(baseDemandDTO);
        Assert.assertNotNull("应该找到服务提供商", result);
    }
}
