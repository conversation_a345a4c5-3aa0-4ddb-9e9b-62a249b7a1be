//package com.deepinnet;
//
//import com.deepinnet.digitaltwin.common.page.CommonPage;
//import com.deepinnet.skyflow.operationcenter.OperationCenterApplication;
//import com.deepinnet.skyflow.operationcenter.dto.FlightDemand90DayStatsDTO;
//import com.deepinnet.skyflow.operationcenter.dto.FlightDemandDTO;
//import com.deepinnet.skyflow.operationcenter.dto.FlightDemandQueryDTO;
//import com.deepinnet.skyflow.operationcenter.dto.RoutineInspectionFlightDemandDTO;
//import com.deepinnet.skyflow.operationcenter.enums.FlightDemandMatchStatusEnum;
//import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
//import com.deepinnet.skyflow.operationcenter.service.demand.FlightDemandService;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.slf4j.MDC;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * Description: 飞行需求服务测试类
// * Date: 2025/4/27
// * Author: lijunheng
// */
//@ActiveProfiles("local")
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = OperationCenterApplication.class)
//public class FlightBizTimeEntityServiceTest {
//
//    @Resource
//    private FlightDemandService flightDemandService;
//
//    private FlightDemandDTO routineDemandDTO;
//    private String savedRoutineDemandNo;
//
//    @Before
//    public void setUp() {
//        // 准备日常巡检需求数据
//        routineDemandDTO = new FlightDemandDTO();
//        routineDemandDTO.setFlightOrderNo("order");
//        routineDemandDTO.setName("测试日常巡检需求");
//        routineDemandDTO.setType(FlightDemandTypeEnum.ROUTINE_INSPECTION);
//        routineDemandDTO.setPublisherNo("test_publisher_001");
//        routineDemandDTO.setFlightUavBm("test_uav_001");
//        routineDemandDTO.setCategoryNo("CAT_ROUTINE_001");
//        routineDemandDTO.setTenantId("TENANT_001");
//        routineDemandDTO.setIncrementService(List.of("test_service_001"));
//        RoutineInspectionFlightDemandDTO routineDemandDTO2 = new RoutineInspectionFlightDemandDTO();
//        routineDemandDTO.setRoutineInspectionDetail(routineDemandDTO2);
//        routineDemandDTO.setCenterPointLongitude("120.1234");
//        routineDemandDTO.setCenterPointLatitude("30.1234");
//
//        MDC.put("Tenant-Id", "deepinnet");
//
//    }
//
//    @Test
//    public void testSaveFlightDemand() {
//        // 测试保存日常巡检需求
//        savedRoutineDemandNo = flightDemandService.saveFlightDemand(routineDemandDTO);
//        Assert.assertNotNull("保存日常巡检需求应返回需求编号", savedRoutineDemandNo);
//
//    }
//
//    @Test
//    public void testGetFlightDemandByNo() {
//        // 先保存一个需求
////        String demandNo = flightDemandService.saveFlightDemand(routineDemandDTO);
//
//        // 测试根据需求编号获取需求
//        FlightDemandDTO result = flightDemandService.getFlightDemandByNo("FLIGHT_DEMAND_8ddc2b4421e54797aa9c6170dc3dbdb0");
//
//        // 验证结果
//        Assert.assertNotNull("应能获取到需求", result);
//        Assert.assertEquals("需求名称应匹配", routineDemandDTO.getName(), result.getName());
//        Assert.assertEquals("需求类型应匹配", FlightDemandTypeEnum.ROUTINE_INSPECTION, result.getType());
//        Assert.assertEquals("需求状态应为未匹配", FlightDemandMatchStatusEnum.UNMATCHED, result.getMatchStatus());
//        Assert.assertEquals("发布者编号应匹配", routineDemandDTO.getPublisherNo(), result.getPublisherNo());
//
//    }
//
//    @Test
//    public void testPageQueryFlightDemandCustomerManage() {
//        // 先保存几个测试需求
//        flightDemandService.saveFlightDemand(routineDemandDTO);
//
//        // 创建查询条件
//        FlightDemandQueryDTO queryDTO = new FlightDemandQueryDTO();
//        queryDTO.setPageNum(1);
//        queryDTO.setPageSize(10);
//        queryDTO.setType(FlightDemandTypeEnum.ROUTINE_INSPECTION);
//
//        // 执行分页查询
//        CommonPage<FlightDemandDTO> page = flightDemandService.pageQueryFlightDemandCustomerManage(queryDTO);
//
//        // 验证结果
//        Assert.assertNotNull("分页结果不应为空", page);
//        Assert.assertTrue("应能查询到至少一条记录", page.getTotal() > 0);
//
//        // 验证结果中是否包含我们创建的需求
//        List<FlightDemandDTO> list = page.getList();
//        boolean found = false;
//        for (FlightDemandDTO item : list) {
//            if (item.getName().equals(routineDemandDTO.getName())) {
//                found = true;
//                break;
//            }
//        }
//        Assert.assertTrue("查询结果应包含已创建的日常巡检需求", found);
//    }
//
//    @Test
//    public void testUpdateFlightDemandMatchStatus() {
//        // 先保存一个需求
//        String demandNo = flightDemandService.saveFlightDemand(routineDemandDTO);
//
//        // 准备更新数据
//        FlightDemandDTO updateDTO = new FlightDemandDTO();
//        updateDTO.setDemandNo(demandNo);
//        updateDTO.setMatchStatus(FlightDemandMatchStatusEnum.MATCHED);
//        updateDTO.setSyncOrderNo("ORDER_001");
//        updateDTO.setServiceProviderNo("PROVIDER_001");
//        updateDTO.setServiceProviderName("测试服务提供商");
//
//        // 执行更新
//        flightDemandService.updateFlightDemandStatus(updateDTO);
//
//        // 获取更新后的需求并验证
//        FlightDemandDTO updatedDemand = flightDemandService.getFlightDemandByNo(demandNo);
//        Assert.assertNotNull("应能获取到更新后的需求", updatedDemand);
//        Assert.assertEquals("需求状态应已更新", FlightDemandMatchStatusEnum.MATCHED, updatedDemand.getMatchStatus());
//        Assert.assertEquals("同步订单号应已更新", "ORDER_001", updatedDemand.getSyncOrderNo());
//        Assert.assertEquals("服务提供商编号应已更新", "PROVIDER_001", updatedDemand.getServiceProviderNo());
//        Assert.assertEquals("服务提供商名称应已更新", "测试服务提供商", updatedDemand.getServiceProviderName());
//    }
//
//    @Test
//    public void testGet30DayStatistics() {
//
//        FlightDemand90DayStatsDTO statsDTO = flightDemandService.get90DayStatistics("uid_1920791444604985344");
//
//        System.out.println(statsDTO);
//        Assert.assertNotNull(statsDTO);
//    }
//}
