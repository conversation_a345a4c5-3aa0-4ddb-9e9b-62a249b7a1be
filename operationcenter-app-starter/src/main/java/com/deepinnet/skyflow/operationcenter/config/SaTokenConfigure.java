package com.deepinnet.skyflow.operationcenter.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.*;

import javax.annotation.Resource;

@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {

    @Resource
    private SaInterceptor saInterceptor;

    /**
     * 放行目录
     */
    private static final String[] EXCLUDE_PATH_PATTERNS = {
            "/user/login",
            // Swagger
            "**/swagger-ui.html",
            "/swagger-resources/**",
            "/webjars/**",
            "/v2/**",
            "/swagger-ui.html/**",
            "/doc.html/**",
            "/error",
            "/favicon.ico",
            "sso/auth",
            "/v3/api-docs",
            "/user/register",
            "/user/password/forget",
            "/sms/code/send",
            "/sms/code/verify",
            "/category/page",
            "/dictionary/**",
            "/flight/product/get",
            "/flight/product/page",
            "/flight/product/categoryStat",
            "/flight/uav/station/page",
            "/flight/uav/station/customerPage",

            "/content/**",
            "/demand/sync/plan/bind",
            "/task/notify/start",
            "/task/notify/end",

            "/demand/pass/list",

            //这几个是mock的远程接口需要放行
            "/create/flight/monitor/task",
            "/notify/flight/task/end",
            "/api/task/demand/normal",
            "/api/plan/flyRelation/index/queryList",
            "/api/plan/flightPlan/detail/**",
            "/api/device/live-address/getBySn",
            "/api/task/demand/insertDemand",
            "/actuator/**",

    };

    /**
     * 注册 Sa-Token 拦截器，打开注解式鉴权功能
     *
     * @param registry
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
        registry.addInterceptor(saInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(EXCLUDE_PATH_PATTERNS);
    }
}