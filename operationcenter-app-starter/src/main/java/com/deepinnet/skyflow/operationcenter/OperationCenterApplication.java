package com.deepinnet.skyflow.operationcenter;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Operation Center Application
 *
 * <AUTHOR>
 */
@EnableScheduling
@SpringBootApplication
@ComponentScan(value = {"com.deepinnet"}, excludeFilters = @ComponentScan.Filter(
        type = FilterType.ASSIGNABLE_TYPE
))
@MapperScan({"com.deepinnet.skyflow.operationcenter.dal.mapper", "com.deepinnet.lock.mapper"})
@EnableFeignClients(basePackages = "com.deepinnet")
@EnableAspectJAutoProxy
public class OperationCenterApplication {

    public static void main(String[] args) {
        SpringApplication.run(OperationCenterApplication.class, args);
    }

} 