
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ***************************************************************************************************************************
          username: postgres
          password: Shendu188
          driver-class-name: org.postgresql.Driver

stpf:
  service:
    url: http://*************:60316
    #url: http://localhost:60106

out-api:
  yuan-fei:
    #    管控
    control:
      username: 13904014313
      password: zhjg2023
      serverUrl: https://jt-gk.kitegogo.cn
    #      serverUrl: http://localhost:8080

    #    运营端
    op:
      username: 17647654305
      password: 123456aA!
      serverUrl: https://lg-yy.kitegogo.cn
  #      serverUrl: http://localhost:8080

  gps:
    address: http://*************:8090
    username: 衢州交警
    password: Hgs@123456

mqtt:
  protocol: ws
  host: mqtest.kitegogo.cn
  port: 2416
  username: admin
  password: public

mock:
  # 鸳飞
  yf: true


gaode:
  enterprise:
    map:
      ak: ec85d3648154874552835438ac6a02b2
      server-url: http://*************:35001

approve:
  org:
    - org-code: 228
      director:
        - uid_1948580084737343488
    - org-code: 229
      director:
        - uid_1948592942166540288

    - org-code: 230
      director:
        - uid_1948592261732990976
    - org-code: 231
      director:
        - uid_1948592602268532736
    - org-code: 232
      director:
        - uid_1948592701270884352


    - org-code: 233
      director:
        - uid_1948592787828736000

    # 政数局
    - org-code: 140
      director:
        - uid_1928027131879292928
    - org-code: 142
      director:
        - uid_1928024945262788608

  designate:
    director:
      - uid_1928024945262788608

  # 政数局的组织编号
  zsj-org-code: 140