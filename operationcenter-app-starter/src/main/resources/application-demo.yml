spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: **************************************************************************************************************************
          username: postgres
          password: sdzl123
          driver-class-name: org.postgresql.Driver

  # Redis配置
  redis:
    # Redis数据库索引（默认为0）
    database: 6
    # Redis服务器地址
    host: ************
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password: shendu188
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
stpf:
  service:
    url: http://************:60106
    #url: http://localhost:60106


approve:
  org:
    - org-code: 228
      director:
        - uid_1948580084737343488
    - org-code: 229
      director:
        - uid_1948592942166540288

    - org-code: 230
      director:
        - uid_1948592261732990976
    - org-code: 231
      director:
        - uid_1948592602268532736
    - org-code: 232
      director:
        - uid_1948592701270884352


    - org-code: 233
      director:
        - uid_1948592787828736000

    # 政数局
    - org-code: 140
      director:
        - uid_1928027131879292928
    - org-code: 142
      director:
        - uid_1928024945262788608

  designate:
    director:
      - uid_1928024945262788608

  # 政数局的组织编号
  zsj-org-code: 140