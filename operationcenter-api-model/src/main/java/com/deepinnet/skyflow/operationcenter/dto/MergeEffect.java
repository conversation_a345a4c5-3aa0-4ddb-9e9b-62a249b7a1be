package com.deepinnet.skyflow.operationcenter.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: 合并效果
 * Date: 2025/6/16
 * Author: lijunheng
 */
@Data
public class MergeEffect implements Serializable {

    /**
     * 原始需求总飞行次数
     */
    private Integer flightCountBefore;

    /**
     * 合并需求总飞行次数
     */
    private Integer flightCountAfter;

    /**
     * 合并需求总飞行次数减少的次数
     */
    private Integer reduceFlightCount;

    /**
     * 原始需求数量
     */
    private Integer demandCountBefore;

    /**
     * 合并需求数量
     */
    private Integer demandCountAfter;
}
