package com.deepinnet.skyflow.operationcenter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * Creator zengjuerui
 * Date 2025-06-03
 **/

@Data
public class RealTimeUavFlightPositionDTO {


    @ApiModelProperty("飞行当前经度")
    private String currentLng;

    @ApiModelProperty("飞行当前纬度")
    private String currentLat;

    @ApiModelProperty("飞行当前高度")
    private String currentAlt;

    @ApiModelProperty("距离 米")
    private String distanceToTarget;

    @ApiModelProperty("到达时间 分钟")
    private String timeMinToArrival;

    @JsonProperty("isArrived")
    @ApiModelProperty("已到达")
    private Boolean arrived;

    @ApiModelProperty("飞行过的轨迹 wkt")
    private List<UavFlightPositionMeta> flownTrajectory;


    @Data
    public static class UavFlightPositionMeta {

        private String lng;

        private String lat;

        private String alt;
    }
}
