package com.deepinnet.skyflow.operationcenter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MergeDemandRuleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank(message = "规则配置不能为空")
    private String demandRuleConfig;

}
