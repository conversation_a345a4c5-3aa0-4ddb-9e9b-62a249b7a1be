package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightPositionTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/14
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FlightPositionDTO implements Serializable {

    private final static long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号", hidden = true)
    private String orderNo;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型(POINT-点位; POLYGON-区域)")
    private FlightPositionTypeEnum type;

    /**
     * 名称(点位/区域)
     */
    @ApiModelProperty(value = "名称(点位/区域)")
    private String name;

    /**
     * 飞行点位
     */
    @ApiModelProperty(value = "飞行点位(区域)")
    private String flightPosition;

    /**
     * 区域中心点
     */
    @ApiModelProperty(value = "区域中心点")
    private String centerPoint;

}
