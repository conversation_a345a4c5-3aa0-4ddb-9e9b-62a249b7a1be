package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;

@ApiModel
@Data
public class DemandMergeScheduleDTO implements Serializable {

    @ApiModelProperty("合并需求周期的开始日期")
    private LocalDate mergeScheduleStartDate;

    @ApiModelProperty("合并需求周期的结束日期")
    private LocalDate mergeScheduleEndDate;

    @ApiModelProperty("合并需求周期的最早开始时间")
    private LocalTime mergeScheduleStartTime;

    @ApiModelProperty("合并需求周期的最晚结束时间")
    private LocalTime mergeScheduleEndTime;

    @ApiModelProperty("合并需求周期的持续天数")
    private int continuousDays;
}
