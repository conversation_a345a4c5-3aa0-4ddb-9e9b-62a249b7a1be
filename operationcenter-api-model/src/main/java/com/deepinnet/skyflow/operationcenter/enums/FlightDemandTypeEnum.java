package com.deepinnet.skyflow.operationcenter.enums;

import com.deepinnet.skyflow.operationcenter.dto.EmergencyResponseFlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.LogisticsTransportationFlightDemandDTO;
import com.deepinnet.skyflow.operationcenter.dto.RoutineInspectionFlightDemandDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 * Date: 2025/4/16
 * Author: lijunheng
 */
@Getter
@AllArgsConstructor
public enum FlightDemandTypeEnum {
    ROUTINE_INSPECTION("ROUTINE_INSPECTION", RoutineInspectionFlightDemandDTO.class, "日常巡检"),
    LOGISTICS_TRANSPORTATION("LOGISTICS_TRANSPORTATION",  LogisticsTransportationFlightDemandDTO.class, "物流运输"),
    EMERGENCY_RESPONSE("EMERGENCY_RESPONSE",  EmergencyResponseFlightDemandDTO.class, "应急处置");

    private final String type;

    private final Class clazz;

    private final String desc;
}
