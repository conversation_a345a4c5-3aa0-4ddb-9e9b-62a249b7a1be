package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 合并处理分组统计DTO
 */
@Data
@ApiModel("合并处理分组统计DTO")
public class MergeHandleGroupDTO {

    @ApiModelProperty("合并处理编号")
    private String mergeHandleCode;

    @ApiModelProperty("原始需求")
    private List<FlightMergeDemandListVO> originDemandList;
} 