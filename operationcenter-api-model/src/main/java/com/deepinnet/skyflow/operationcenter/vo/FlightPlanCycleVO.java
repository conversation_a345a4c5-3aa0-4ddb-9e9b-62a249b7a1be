package com.deepinnet.skyflow.operationcenter.vo;

import com.deepinnet.skyflow.operationcenter.enums.FlightPlanCycleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 *     周期计划
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Getter
@Setter
public class FlightPlanCycleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 周期编号
     */
    @ApiModelProperty(value = "周期编号")
    private String cycleNo;

    /**
     * 周期名称
     */
    @ApiModelProperty(value = "周期名称")
    private String cycleName;

    /**
     * 周期类型(YEAR; QUARTER; MONTH)
     */
    @ApiModelProperty(value = "周期类型")
    private FlightPlanCycleEnum cycleType;

    /**
     * 周期开始时间
     */
    @ApiModelProperty(value = "周期开始时间")
    private LocalDate cycleStart;

    /**
     * 周期结束时间
     */
    @ApiModelProperty(value = "周期结束时间")
    private LocalDate cycleEnd;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creatorName;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否激活")
    private Boolean isActive;

    /**
     * 规划数
     */
    @ApiModelProperty(value = "规划数量")
    private Integer planNums = 0;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long gmtCreated;
}
