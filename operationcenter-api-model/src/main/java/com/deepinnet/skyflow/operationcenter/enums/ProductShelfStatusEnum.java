package com.deepinnet.skyflow.operationcenter.enums;

import lombok.Getter;

/**
 * 产品上下架状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ProductShelfStatusEnum {

    /**
     * 待上架
     */
    PENDING("PENDING", "待上架"),
    
    /**
     * 已上架
     */
    ONLINE("ONLINE", "已上架");

    private final String code;
    private final String desc;

    ProductShelfStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ProductShelfStatusEnum getByCode(String code) {
        for (ProductShelfStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 通过code获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(String code) {
        ProductShelfStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : null;
    }
} 