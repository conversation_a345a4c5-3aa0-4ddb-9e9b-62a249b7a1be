package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightDemandAssignServiceProviderWayEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 飞行需求分配服务商DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行需求分配服务商信息")
public class FlightDemandAssignDTO {

    /**
     * 需求编号
     */
    @ApiModelProperty("需求编号")
    private String demandCode;

    /**
     * 服务提供商编号
     */
    @ApiModelProperty("服务提供商编号")
    private String serviceProviderNo;

    /**
     * 服务商分配方式
     */
    @ApiModelProperty("服务商分配方式")
    private FlightDemandAssignServiceProviderWayEnum assignWay;

    /**
     * 分配服务商的用户
     */
    @ApiModelProperty("分配服务商的用户")
    private String assignOperatorUserNo;

    /**
     * 分配服务商的时间
     */
    @ApiModelProperty("分配服务商的时间")
    private LocalDateTime assignTime;

    @ApiModelProperty("租户ID")
    private String tenantId;
}
