package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.format.DateTimeFormatter;

/**
 * <PERSON><PERSON> z<PERSON>
 * Date 2025-06-04
 **/

@Data
public class FlightDemandDayStatsDTO {


    public static final DateTimeFormatter DF = DateTimeFormatter.ofPattern("MM/dd");

    @ApiModelProperty("数量")
    private Integer num;

    @ApiModelProperty("日期")
    private String date;
}
