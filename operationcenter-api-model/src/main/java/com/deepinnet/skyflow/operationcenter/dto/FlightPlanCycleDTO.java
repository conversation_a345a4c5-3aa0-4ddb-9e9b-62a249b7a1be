package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightPlanCycleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 *     周期计划
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Getter
@Setter
public class FlightPlanCycleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 周期编号
     */
    @ApiModelProperty(value = "周期编号")
    private String cycleNo;

    /**
     * 周期名称
     */
    @ApiModelProperty(value = "周期名称")
    private String cycleName;

    /**
     * 周期类型(YEAR; QUARTER; MONTH)
     */
    @ApiModelProperty(value = "周期类型")
    private FlightPlanCycleEnum cycleType;

    /**
     * 周期开始时间
     */
    @ApiModelProperty(value = "周期开始时间")
    private LocalDate cycleStart;

    /**
     * 周期结束时间
     */
    @ApiModelProperty(value = "周期结束时间")
    private LocalDate cycleEnd;
}
