package com.deepinnet.skyflow.operationcenter.vo;

import com.deepinnet.skyflow.operationcenter.dto.FlightUavBmDTO;
import com.deepinnet.skyflow.operationcenter.dto.PriceDTO;
import com.deepinnet.skyflow.operationcenter.enums.FlightProductTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.ProductServiceTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.ProductShelfStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 飞行产品VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行产品信息")
public class FlightProductVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 产品编码
     */
    @ApiModelProperty(value = "产品编码")
    private String productNo;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品描述
     */
    @ApiModelProperty(value = "产品描述")
    private String productDescription;

    /**
     * 产品详情
     */
    @ApiModelProperty(value = "产品详情")
    private String productDetail;

    /**
     * 产品内容
     */
    @ApiModelProperty(value = "产品内容")
    private String productContent;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private FlightProductTypeEnum productType;

    /**
     * 产品服务类型
     */
    @ApiModelProperty(value = "产品服务类型: PILOT_SERVICE-飞手服务，NORMAL_SERVICE-普通服务")
    private ProductServiceTypeEnum productServiceType;

    /**
     * 关联类目编号
     */
    @ApiModelProperty(value = "关联类目编号")
    private String categoryNo;

    /**
     * 关联类目名称
     */
    @ApiModelProperty(value = "关联类目名称")
    private String categoryName;

    /**
     * 服务提供商编号
     */
    @ApiModelProperty(value = "服务提供商编号")
    private String serviceProviderNo;

    /**
     * 产品状态
     */
    @ApiModelProperty(value = "产品状态")
    private String productStatus;

    /**
     * 产品上下架状态
     */
    @ApiModelProperty(value = "产品上下架状态: PENDING-待上架，ONLINE-已上架")
    private ProductShelfStatusEnum productShelfStatus;

    /**
     * 最低价
     */
    @ApiModelProperty(value = "最低价")
    private BigDecimal productMinPrice;

    /**
     * 图片列表
     */
    @ApiModelProperty(value = "图片列表")
    private String[] productPictures;

    /**
     * 机型列表
     */
    @ApiModelProperty(value = "机型列表")
    private String[] flightUavBmList;

    /**
     * 关联类目编号列表
     */
    @ApiModelProperty(value = "关联类目编号列表")
    private String[] categoryNoList;

    /**
     * 价格列表
     */
    @ApiModelProperty(value = "价格列表")
    private List<PriceDTO> priceList;

    /**
     * 飞行无人机品牌型号列表
     */
    @ApiModelProperty(value = "飞行无人机品牌型号列表")
    private List<FlightUavBmDTO> flightUavBmModelList;

    /**
     * 是否提供飞手服务
     */
    @ApiModelProperty(value = "是否提供飞手服务")
    private Boolean supportUavPilot;

    /**
     * 飞行无人机品牌型号
     */
    @ApiModelProperty(value = "飞行无人机品牌型号")
    private FlightUavBmDTO flightUavBm;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
} 