package com.deepinnet.skyflow.operationcenter.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/18
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlightWorkbenchCoreVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 已审核规划数
     */
    private FlightStatisticsBasicVO approvedOrders;

    /**
     * 有效飞行需求数
     */
    private FlightStatisticsBasicVO effectiveDemands;

    /**
     * 飞行计划数
     */
    private FlightStatisticsBasicVO flightPlans;

    /**
     * 飞行记录数
     */
    private FlightStatisticsBasicVO endFlightPlans;

}
