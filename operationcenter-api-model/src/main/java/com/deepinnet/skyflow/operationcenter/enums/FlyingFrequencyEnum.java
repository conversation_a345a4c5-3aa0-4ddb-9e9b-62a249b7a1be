package com.deepinnet.skyflow.operationcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description:
 * Date: 2025/4/16
 * Author: lijunheng
 */
@AllArgsConstructor
@Getter
public enum FlyingFrequencyEnum {
    DAILY("DAILY", "每天"),
    WEEKLY("WEEKLY", "每周"),
    MONTHLY("MONTHLY", "每月"),
    YEARLY("YEARLY", "每年"),
    WEEKLY_MONDAY("WEEKLY_MONDAY", "每周一"),
    WEEKLY_TUESDAY("WEEKLY_TUESDAY", "每周二"),
    WEEKLY_WEDNESDAY("WEEKLY_WEDNESDAY", "每周三"),
    WEEKLY_THURSDAY("WEEKLY_THURSDAY", "每周四"),
    WEEKLY_FRIDAY("WEEKLY_FRIDAY", "每周五"),
    WEEKLY_SATURDAY("WEEKLY_SATURDAY", "每周六"),
    WEEKLY_SUNDAY("WEEKLY_SUNDAY", "每周日");

    private final String frequency;
    private final String desc;
}
