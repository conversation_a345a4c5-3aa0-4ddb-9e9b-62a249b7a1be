package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 飞行需求查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "飞行需求查询条件")
public class FlightDemandQueryDTO extends PageQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    private String demandNo;

    /**
     * 需求名称
     */
    @ApiModelProperty(value = "需求名称")
    private String name;

    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private FlightDemandTypeEnum type;

    /**
     * 发布者编号
     */
    @ApiModelProperty(value = "发布者编号")
    private String publisherNo;

    /**
     * 前端传递，发布者名称
     */
    @ApiModelProperty(value = "发布者名称")
    private String publisherName;

    /**
     * 客户角色使用-发布者列表，前端不用传，后台根据发布者组织查询
     */
    private List<String> publisherNoList;

    /**
     * 飞行订单编号
     */
    @ApiModelProperty(value = "飞行订单编号")
    private String flightOrderNo;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "服务名称(主产品服务名称)")
    private String productName;

    /**
     * 飞行无人机型号
     */
    @ApiModelProperty(value = "飞行无人机型号")
    private String flightUavBm;

    /**
     * 服务类型
     */
    @ApiModelProperty(value = "服务类型")
    private FlightServiceTypeEnum serviceType;

    /**
     * 详细类目编号
     */
    @ApiModelProperty(value = "类目编号")
    private String categoryNo;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private FlightDemandMatchStatusEnum matchStatus;

    @ApiModelProperty(value = "服务商编号")
    private String serviceProviderNo;

    @ApiModelProperty(value = "服务商名称")
    private String serviceProviderName;

    @ApiModelProperty(value = "服务商所属公司名称")
    private String companyName;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty("需求编号列表")
    private List<String> demandNoList;

    /**
     * 审核状态
     * {@link com.deepinnet.approval.ApproveStatusEnum}
     */
    @ApiModelProperty(value = "审核状态")
    private List<String> approveStatusList;

    /**
     * 待我审核
     */
    @ApiModelProperty(value = "待我审核")
    private Boolean waitMeToApprove = Boolean.FALSE;

    /**
     * 当前用户编码
     */
    @ApiModelProperty(value = "当前用户编码")
    private String curUserCode;

    @ApiModelProperty(value = "客户组织ID")
    private String organizationId;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "客户组织名称")
    private String organizationName;
} 