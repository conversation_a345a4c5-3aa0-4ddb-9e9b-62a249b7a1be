package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Description:
 * Date: 2025/8/7
 * Author: lijunheng
 */
@Data
public class ApprovalConfigRuleQueryDTO extends PageQueryDTO {

    @ApiModelProperty("用户编号")
    private String userNo;

    @ApiModelProperty("规则编码")
    private String code;

    @ApiModelProperty("规则名称")
    private String ruleName;

    @ApiModelProperty("规则类型")
    private String ruleType;

    @ApiModelProperty("组织编码")
    private String orgCode;
}
