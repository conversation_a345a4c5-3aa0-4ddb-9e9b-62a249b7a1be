package com.deepinnet.skyflow.operationcenter.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/11
 */

@Getter
@AllArgsConstructor
public enum MergeRuleTypeEnum {

    /**
     * 需求分类
     */
    DEMAND_RULE_CONFIG("DEMAND_RULE_CONFIG", "需求规则配置"),

    /**
     * 直线距离
     */
    FLIGHT_AREA("FLIGHT_AREA", "直线距离"),

    ;

    private final String ruleType;

    private final String ruleDesc;

    public static MergeRuleTypeEnum getRuleEnumByRuleType(String ruleType) {
        for (MergeRuleTypeEnum typeEnum : MergeRuleTypeEnum.values()) {
            if (StrUtil.equals(typeEnum.getRuleType(), ruleType)) {
                return typeEnum;
            }
        }
        return null;
    }

}
