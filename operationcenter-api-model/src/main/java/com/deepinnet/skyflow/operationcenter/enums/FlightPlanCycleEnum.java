package com.deepinnet.skyflow.operationcenter.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/14
 */
@Getter
public enum FlightPlanCycleEnum {

    /**
     * 年度计划
     */
    YEAR("YEAR", "年度计划"),

    /**
     * 季度计划
     */
    QUARTER("QUARTER", "季度计划"),

    /**
     * 月度计划
     */
    MONTH("MONTH", "月度计划");

    private final String code;

    private final String desc;

    FlightPlanCycleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FlightPlanCycleEnum getEnumByCode(String code) {
        for (FlightPlanCycleEnum cycleEnum : FlightPlanCycleEnum.values()) {
            if (StrUtil.equals(code, cycleEnum.getCode())) {
                return cycleEnum;
            }
        }
        return null;
    }
}
