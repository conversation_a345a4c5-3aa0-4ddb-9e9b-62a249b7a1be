package com.deepinnet.skyflow.operationcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作日志模板枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OperationLogTemplateEnum {

    // 成员管理
    MEMBER_ADD("MEMBER_ADD", "成员管理", "在【{organizationName}】添加了成员【{memberName}】"),
    MEMBER_DELETE("MEMBER_DELETE", "成员管理", "在【{organizationName}】删除了成员【{memberName}】"),
    MEMBER_EDIT("MEMBER_EDIT", "成员管理", "在【{organizationName}】编辑了成员【{memberName}】"),

    // 账号管理
    ACCOUNT_ADD("ACCOUNT_ADD", "账号管理", "添加了【{memberName}】的管理账号【{accountNo}】"),
    ACCOUNT_DELETE("ACCOUNT_DELETE", "账号管理", "删除了【{memberName}】的管理账号【{accountNo}】"),
    ACCOUNT_FREEZE("ACCOUNT_FREEZE", "账号管理", "冻结了【{memberName}】的管理账号【{accountNo}】"),
    ACCOUNT_EDIT("ACCOUNT_EDIT", "账号管理", "编辑了【{memberName}】的管理账号【{accountNo}】"),

    // 角色管理
    ROLE_ADD("ROLE_ADD", "角色管理", "在【{organizationName}】添加了角色【{roleName}】"),
    ROLE_DELETE("ROLE_DELETE", "角色管理", "在【{organizationName}】删除了角色【{roleName}】"),
    ROLE_EDIT("ROLE_EDIT", "角色管理", "在【{organizationName}】编辑了角色【{roleName}】"),

    // 飞行需求
    DEMAND_PUBLISH("DEMAND_PUBLISH", "飞行需求", "发布了飞行需求【{demandName}】"),
    DEMAND_EDIT("DEMAND_EDIT", "飞行需求", "编辑了飞行需求【{demandName}】"),
    DEMAND_DELETE("DEMAND_DELETE", "飞行需求", "删除了飞行需求【{demandName}】"),
    DEMAND_APPROVE("DEMAND_APPROVE", "飞行需求", "审批通过了飞行需求【{demandName}】"),
    DEMAND_REJECT("DEMAND_REJECT", "飞行需求", "驳回了飞行需求【{demandName}】"),

    // 数据下载
    DATA_DOWNLOAD("DATA_DOWNLOAD", "数据下载", "在工作台首页下载了行业务数据报表【{reportName}】"),

    // 审批流设置
    APPROVAL_PLAN_UPDATE("APPROVAL_PLAN_UPDATE", "审批流设置", "更新了飞行规划审批设置"),
    APPROVAL_DEMAND_UPDATE("APPROVAL_DEMAND_UPDATE", "审批流设置", "更新了飞行需求审批设置"),
    APPROVAL_SCHEDULE_UPDATE("APPROVAL_SCHEDULE_UPDATE", "审批流设置", "更新了飞行调度审批设置"),

    // 组织管理
    ORGANIZATION_ADD("ORGANIZATION_ADD", "组织管理", "新增了组织【{organizationName}】"),
    ORGANIZATION_DELETE("ORGANIZATION_DELETE", "组织管理", "删除了组织【{organizationName}】"),

    // 登录
    LOGIN_SUCCESS("LOGIN_SUCCESS", "登录", "登录成功"),
    LOGOUT_SUCCESS("LOGOUT_SUCCESS", "登录", "退出成功"),

    // 信息管理
    PROFILE_UPDATE("PROFILE_UPDATE", "信息管理", "更新了个人信息"),

    // 规划周期
    PLANNING_CYCLE_ADD("PLANNING_CYCLE_ADD", "规划周期", "新增了规划周期【{cycleName}】"),
    PLANNING_CYCLE_EDIT("PLANNING_CYCLE_EDIT", "规划周期", "编辑了规划周期【{cycleName}】"),
    PLANNING_CYCLE_DELETE("PLANNING_CYCLE_DELETE", "规划周期", "删除了规划周期【{cycleName}】"),

    // 飞行规划
    FLIGHT_PLAN_PUBLISH("FLIGHT_PLAN_PUBLISH", "飞行规划", "发布了飞行规划【{planName}】"),
    FLIGHT_PLAN_EDIT("FLIGHT_PLAN_EDIT", "飞行规划", "编辑了飞行规划【{planName}】"),
    FLIGHT_PLAN_DELETE("FLIGHT_PLAN_DELETE", "飞行规划", "删除了飞行规划【{planName}】"),
    FLIGHT_PLAN_APPROVE("FLIGHT_PLAN_APPROVE", "飞行规划", "审批通过了飞行规划【{planName}】"),
    FLIGHT_PLAN_REJECT("FLIGHT_PLAN_REJECT", "飞行规划", "驳回了飞行规划【{planName}】");

    /**
     * 模板代码
     */
    private final String code;

    /**
     * 操作模块
     */
    private final String module;

    /**
     * 操作详情模板
     */
    private final String template;

    /**
     * 根据代码获取枚举
     */
    public static OperationLogTemplateEnum getByCode(String code) {
        for (OperationLogTemplateEnum template : values()) {
            if (template.getCode().equals(code)) {
                return template;
            }
        }
        return null;
    }
}
