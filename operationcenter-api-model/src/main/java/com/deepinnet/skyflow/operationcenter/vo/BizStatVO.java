package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/21
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizStatVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    @ApiModelProperty(value = "日期")
    private LocalDate statDate;

    // —— 原始汇总值 ——
    @ApiModelProperty(value = "有效需求数")
    private long validDemandNum;

    @ApiModelProperty(value = "总计划数")
    private long totalPlanNum;

    @ApiModelProperty(value = "待飞行计划数")
    private long readyPlanNum;

    @ApiModelProperty(value = "已完成飞行数")
    private long completePlanNum;

    @ApiModelProperty(value = "审核通过规划数")
    private long approvedOrderNum;

    // —— 环比差值（今日‑昨日） ——
    @ApiModelProperty(value = "有效需求差值")
    private long validDemandDiff;

    @ApiModelProperty(value = "总飞行计划差值")
    private long totalPlanDiff;

    @ApiModelProperty(value = "待飞行计划差值")
    private long readyPlanDiff;

    @ApiModelProperty(value = "已飞行计划差值")
    private long completePlanDiff;

    @ApiModelProperty(value = "已审核规划差值")
    private long approvedOrderDiff;

    // —— 环比百分比（保留两位小数，格式 “15.23 / 99.12”） ——
    @ApiModelProperty(value = "有效需求比值")
    private String validDemandPct;

    @ApiModelProperty(value = "总飞行计划比值")
    private String totalPlanPct;

    @ApiModelProperty(value = "待飞行计划比值")
    private String readyPlanPct;

    @ApiModelProperty(value = "已飞行计划比值")
    private String completePlanPct;

    @ApiModelProperty(value = "审核通过规划比值")
    private String approvedOrderPct;

    // —— 环比百分比带符号（保留两位小数，格式 “+15.23% / +99.12% / -12.55%”） ——
    @ApiModelProperty(value = "带符号有效需求比值")
    private String signedValidDemandPct;

    @ApiModelProperty(value = "带符号总飞行计划比值")
    private String signedTotalPlanPct;

    @ApiModelProperty(value = "带符号待飞行计划比值")
    private String signedReadyPlanPct;

    @ApiModelProperty(value = "带符号已飞行计划比值")
    private String signedCompletePlanPct;

    @ApiModelProperty(value = "带符号审核通过规划比值")
    private String signedApprovedOrderPct;
}
