package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/14
 */

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FlightStatisticsBasicVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前数量
     */
    @ApiModelProperty(value = "当前数量")
    private Integer value;

    /**
     * 昨日数量
     */
    @ApiModelProperty(value = "昨日数量")
    private Integer baseValue;

    /**
     * 增长数量(value - baseValue)
     */
    @ApiModelProperty(value = "增长数量")
    private Integer delta;

    /**
     * 增长率 (value - baseValue) / baseValue
     */
    @ApiModelProperty(value = "增长率")
    private String deltaRate;

    /**
     * 带符号的增长率 (value - baseValue) / baseValue
     * +10%
     * -10%
     */
    @ApiModelProperty(value = "带符号增长率")
    private String signedDeltaRate;

    /**
     * 完成率
     */
    @ApiModelProperty(value = "完成率")
    private String finishRate;

    /**
     * 昨日完成率
     */
    @ApiModelProperty(value = "昨日完成率")
    private String baseFinishRate;

}
