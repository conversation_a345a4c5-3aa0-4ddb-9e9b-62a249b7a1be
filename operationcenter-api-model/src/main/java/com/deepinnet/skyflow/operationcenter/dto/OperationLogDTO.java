package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 操作日志传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "操作日志")
public class OperationLogDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 操作用户编号
     */
    @ApiModelProperty("操作用户编号")
    private String userNo;

    /**
     * 操作用户名称
     */
    @ApiModelProperty("操作用户名称")
    private String userName;

    /**
     * 操作模块
     */
    @ApiModelProperty("操作模块")
    private String module;

    /**
     * 操作详情
     */
    @ApiModelProperty("操作详情")
    private String operationDetail;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private LocalDateTime operationTime;

    /**
     * 请求IP地址
     */
    @ApiModelProperty("请求IP地址")
    private String ipAddress;

    /**
     * 用户代理信息
     */
    @ApiModelProperty("用户代理信息")
    private String userAgent;

    /**
     * 请求URI
     */
    @ApiModelProperty("请求URI")
    private String requestUri;

    /**
     * 请求方法
     */
    @ApiModelProperty("请求方法")
    private String requestMethod;

    /**
     * 操作结果 (SUCCESS-成功, FAILURE-失败)
     */
    @ApiModelProperty("操作结果")
    private String operationResult;

    /**
     * 错误信息
     */
    @ApiModelProperty("错误信息")
    private String errorMessage;

    /**
     * 租户ID
     */
    @ApiModelProperty("租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime gmtModified;
}
