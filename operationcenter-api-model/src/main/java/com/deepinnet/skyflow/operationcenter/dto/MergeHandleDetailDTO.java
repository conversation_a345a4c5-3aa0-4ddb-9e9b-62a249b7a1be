package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.MergeStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 合并处理详情DTO
 */
@Data
@ApiModel("合并处理详情DTO")
public class MergeHandleDetailDTO {

    @ApiModelProperty("合并处理编号")
    private String mergeHandleCode;

    @ApiModelProperty("原始需求列表")
    private List<FlightMergeDemandListVO> originDemands;

    @ApiModelProperty("合并需求列表")
    private List<FlightMergeDemandListVO> mergeDemands;

    @ApiModelProperty("合并状态")
    private MergeStatusEnum mergeStatus;

    @ApiModelProperty("合并需求的优化效果")
    private MergeEffect mergeEffect;

    @ApiModelProperty("处理时间")
    private LocalDateTime handleTime;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
} 