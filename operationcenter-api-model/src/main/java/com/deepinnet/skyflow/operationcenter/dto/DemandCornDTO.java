package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlyingFrequencyEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/14
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DemandCornDTO {

    /**
     * 需求编号
     */
    private String demandNo;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 开始时间
     */
    private LocalTime startTime;

    /**
     * 结束时间
     */
    private LocalTime endTime;

    /**
     * 飞行频次
     */
    private FlyingFrequencyEnum flyingFrequencyEnum;

    /**
     * 飞行次数
     */
    private Integer flightCount;

}
