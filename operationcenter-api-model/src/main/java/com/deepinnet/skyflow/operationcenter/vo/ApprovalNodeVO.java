package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Description:
 * Date: 2025/7/11
 * Author: lijunheng
 */
@ApiModel(description = "审批节点信息")
@Data
public class ApprovalNodeVO implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "审批id")
    private String approvalId;

    @ApiModelProperty(value = "审批层级")
    private Integer stepOrder;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    private String approveUserId;

    @ApiModelProperty(value = "审批人名称")
    private String approveUserName;

    @ApiModelProperty(value = "审批人部门id")
    private String approveDepartmentId;

    @ApiModelProperty(value = "审批人部门名称")
    private String approveDepartmentName;

    @ApiModelProperty(value = "审批人手机号")
    private String approveUserPhone;

    /**
     * 审批结果
     */
    @ApiModelProperty(value = "审批结果")
    private String status;

    /**
     * 审批结果备注
     */
    @ApiModelProperty(value = "审批结果备注")
    private String remark;

    /**
     * 审批结果操作时间
     */
    @ApiModelProperty(value = "审批结果操作时间")
    private LocalDateTime approvalTime;
}
