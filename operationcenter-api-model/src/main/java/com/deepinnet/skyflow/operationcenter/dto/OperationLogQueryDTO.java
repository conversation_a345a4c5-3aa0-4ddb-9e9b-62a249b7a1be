package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作日志查询传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "操作日志查询条件")
public class OperationLogQueryDTO extends PageQueryDTO {

    /**
     * 操作用户编号
     */
    @ApiModelProperty("操作用户编号")
    private String userNo;

    /**
     * 操作用户名称
     */
    @ApiModelProperty("操作用户名称")
    private String userName;

    /**
     * 操作模块
     */
    @ApiModelProperty("操作模块")
    private String module;

    /**
     * 操作详情（模糊查询）
     */
    @ApiModelProperty("操作详情")
    private String operationDetail;

    /**
     * 操作结果 (SUCCESS-成功, FAILURE-失败)
     */
    @ApiModelProperty("操作结果")
    private String operationResult;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    /**
     * 租户ID
     */
    @ApiModelProperty("租户ID")
    private String tenantId;
}
