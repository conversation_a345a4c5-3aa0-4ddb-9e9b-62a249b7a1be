package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 类目传输对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "类目传输对象")
public class CategoryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类目ID")
    private Integer id;

    @NotBlank(message = "类目编码不能为空")
    @ApiModelProperty(value = "类目编码", required = true)
    private String categoryNo;

    @NotBlank(message = "类目名称不能为空")
    @ApiModelProperty(value = "类目名称", required = true)
    private String categoryName;

    @ApiModelProperty(value = "类目描述")
    private String categoryDescription;

    @ApiModelProperty(value = "父类目编码")
    private String parentCategoryNo;

    @ApiModelProperty(value = "类目层级")
    private Integer categoryLevel;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "类目图片列表")
    private String[] categoryPictureList;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "子类目列表")
    private List<CategoryDTO> children;
} 