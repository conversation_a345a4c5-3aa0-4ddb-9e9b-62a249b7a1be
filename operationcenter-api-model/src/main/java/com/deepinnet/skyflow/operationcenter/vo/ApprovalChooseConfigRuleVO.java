package com.deepinnet.skyflow.operationcenter.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 审批人选择配置规则VO
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Data
public class ApprovalChooseConfigRuleVO implements Serializable {

    /**
     * 审批人选择策略
     */
    private String chooseStrategy;

    /**
     * 审批人选择策略的配置json
     */
    private String approveChooseConfigJson;

    /**
     * 审批方式
     */
    private String approveMode;

    /**
     * 没找到审批人时的处理策略
     */
    private String ifEmptyStrategy;

    /**
     * 没找到审批人时的处理策略的配置json
     */
    private String emptyApproveConfigJson;
} 