package com.deepinnet.skyflow.operationcenter.vo;

import com.deepinnet.skyflow.operationcenter.enums.MergeRuleTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/11
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MergeRuleConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键自增ID
     */
    private Long id;

    /**
     * 规则类型
     */
    private MergeRuleTypeEnum ruleType;

    /**
     * 规则值
     */
    private String ruleValue;

    /**
     * 是否激活
     */
    private Boolean isActive;

    private String tenantId;

}
