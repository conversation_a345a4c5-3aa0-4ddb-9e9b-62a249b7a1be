package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightDemandAssignServiceProviderWayEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlightDemandSyncStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 需求匹配到的服务商
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Data
@ApiModel(description = "飞行需求分配服务商信息")
public class FlightDemandMatchServiceProviderDTO implements Serializable {

    /**
     * 需求编号
     */
    @ApiModelProperty("需求编号（原始或者合并需求）")
    private String demandCode;

    /**
     * 服务提供商编号
     */
    @ApiModelProperty("服务提供商编号")
    private String serviceProviderNo;

    /**
     * 服务商名称
     */
    @ApiModelProperty("服务商名称")
    private String serviceProviderName;

    /**
     * 服务提供商公司名称
     */
    @ApiModelProperty("服务提供商公司名称")
    private String companyName;

    /**
     * 服务提供者组织id
     */
    @ApiModelProperty("服务提供者组织id")
    private String organizationId;

    /**
     * 服务商分配方式
     */
    @ApiModelProperty("服务商分配方式")
    private FlightDemandAssignServiceProviderWayEnum assignWay;

    /**
     * 分配服务商的用户
     */
    @ApiModelProperty("分配服务商的用户")
    private String assignOperatorUserNo;

    /**
     * 分配服务商的时间
     */
    @ApiModelProperty("分配服务商的时间")
    private LocalDateTime assignTime;

    /**
     * 同步状态
     */
    @ApiModelProperty("同步状态")
    private FlightDemandSyncStatusEnum syncStatus;

    /**
     * 同步订单号
     */
    @ApiModelProperty("同步订单号")
    private String syncOrderNo;

    /**
     * 提供商匹配到的服务周期
     */
    @ApiModelProperty("提供商匹配到的服务周期")
    private DemandMergeScheduleDTO mergeScheduleTime;

    /**
     * 是否是合并需求
     */
    @ApiModelProperty("是否是合并需求")
    private Boolean isMergeDemand;
}
