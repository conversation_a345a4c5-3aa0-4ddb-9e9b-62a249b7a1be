package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 创建合并需求DTO
 */
@Data
@ApiModel("创建合并需求DTO")
public class MergeDemandCreateDTO {

    @ApiModelProperty("原始需求编号列表")
    @NotEmpty(message = "原始需求编号列表不能为空")
    private List<FlightDemandDTO> originDemandDTOList;

    @ApiModelProperty("合并需求时间周期")
    private DemandMergeScheduleDTO mergeScheduleDTO;

    @ApiModelProperty("合并需求的飞行轨迹")
    private FlightRoutePlan flightRoutePlan;

    @ApiModelProperty("租户ID")
    private String tenantId;
}