package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 飞行额度次数申请DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行额度次数申请DTO")
public class FlightOrderFlyingNumApplyDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 飞行订单编号
     */
    @ApiModelProperty(value = "飞行订单编号")
    private String flightOrderNo;

    /**
     * 申请数量
     */
    @ApiModelProperty(value = "申请数量")
    private Integer applyFlyingNum;

    /**
     * 申请理由
     */
    @ApiModelProperty(value = "申请理由")
    private String applyReason;

} 