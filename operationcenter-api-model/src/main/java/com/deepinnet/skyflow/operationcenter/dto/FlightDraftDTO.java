package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightDraftBizTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 飞行草稿DTO
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
public class FlightDraftDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 草稿编码
     */
    private String code;
    
    /**
     * 草稿名称
     */
    private String name;

    /**
     * 草稿创建人
     */
    private String creator;

    /**
     * 草稿创建时间
     */
    private LocalDateTime createTime;

    /**
     * 草稿类型
     */
    private FlightDraftBizTypeEnum bizType;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 租户ID
     */
    private String tenantId;
}