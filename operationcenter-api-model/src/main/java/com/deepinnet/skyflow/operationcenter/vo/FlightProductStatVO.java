package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 飞行产品统计结果VO
 */
@Data
@ApiModel("飞行产品统计结果VO")
public class FlightProductStatVO {

    @ApiModelProperty("总数量")
    private Long totalCount;
    
    @ApiModelProperty("类目列表")
    private List<FlightProductCategoryStatVO> categoryStatList;

    @ApiModelProperty("飞行方式列表")
    private List<FlightProductCategoryStatVO.FlightFlyTypeStatVO> flyTypeList;
} 