package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel
@Data
public class ApprovalRecordVO implements Serializable {

    private String approvalId;

    /**
     * 审批发起人ID
     */
    @ApiModelProperty(value = "审批发起人ID")
    private String submitUserId;

    @ApiModelProperty(value = "审批发起人名称")
    private String submitUserName;

    @ApiModelProperty(value = "审批发起人部门ID")
    private String submitDepartmentId;

    @ApiModelProperty(value = "审批发起人部门名称")
    private String submitDepartmentName;

    @ApiModelProperty(value = "审批发起人手机号")
    private String submitUserPhone;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private LocalDateTime applyTime;

    /**
     * 审批结果
     */
    @ApiModelProperty(value = "审批结果")
    private String status;

    @ApiModelProperty(value = "审批节点记录列表")
    private List<ApprovalNodeVO> nodeRecordList;
}

