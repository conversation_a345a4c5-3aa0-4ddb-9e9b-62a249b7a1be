package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 飞行额度次数申请结果DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行额度次数申请结果DTO")
public class FlightOrderFlyingNumApplyResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 剩余飞行次数
     */
    @ApiModelProperty(value = "剩余飞行次数")
    private Integer remainedFlyingNum;


    /**
     * 是否存在待审批申请
     */
    @ApiModelProperty(value = "是否存在待审批申请")
    private Boolean hasApply = false;

    /**
     * 申请单ID
     */
    @ApiModelProperty(value = "申请单ID")
    private String approvalId;

}