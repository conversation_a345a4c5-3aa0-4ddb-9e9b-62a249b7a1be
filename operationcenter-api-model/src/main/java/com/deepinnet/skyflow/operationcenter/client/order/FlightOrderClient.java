package com.deepinnet.skyflow.operationcenter.client.order;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.FlightOrderProductUsageDTO;
import com.deepinnet.skyflow.operationcenter.dto.FlightPlanPageQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.OrderPageQueryDTO;
import com.deepinnet.skyflow.operationcenter.vo.FlightOrderVO;
import com.deepinnet.spatiotemporalplatform.vo.FlightPlanVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/24
 */
@FeignClient(name = "flightOrderClient", url = "${sfoc.service.url}")
public interface FlightOrderClient {

    @PostMapping("/flight/order/orderList")
    Result<CommonPage<FlightOrderVO>> getFlightOrderList(@RequestBody @NotNull(message = "订单查询参数不能为空") OrderPageQueryDTO dto);

    @PostMapping("/flight/order/orderUsage")
    Result<Boolean> orderProductUsage(@RequestBody FlightOrderProductUsageDTO dto);

    @PostMapping("/flight/order/planList")
    Result<CommonPage<FlightPlanVO>> getPlanList(@RequestBody @NotNull(message = "计划查询参数不能为空") FlightPlanPageQueryDTO dto);

    @GetMapping("/flight/order/planDetail")
    Result<FlightPlanVO> getPlanDetail(@RequestParam(value = "planId") String planId);

    @GetMapping("/flight/order/plan/list")
    Result<CommonPage<FlightPlanVO>> planList(@RequestBody @NotNull(message = "计划查询参数不能为空") FlightPlanPageQueryDTO queryDTO);
}
