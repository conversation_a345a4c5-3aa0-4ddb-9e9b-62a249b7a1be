package com.deepinnet.skyflow.operationcenter.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 审批配置规则VO
 *
 * <AUTHOR>
 * @date 2025/1/17
 */
@Data
public class ApprovalConfigRuleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 规则编码
     */
    private String code;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 一级组织编号
     */
    private String orgCode;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 编辑人
     */
    private String editor;

    /**
     * 审批人选择配置列表
     */
    private List<ApprovalChooseConfigRuleVO> approvalChooseConfigRuleList;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
} 