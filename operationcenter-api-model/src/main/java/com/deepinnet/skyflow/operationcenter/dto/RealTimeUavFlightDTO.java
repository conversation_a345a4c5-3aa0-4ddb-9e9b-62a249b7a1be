package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Creator zengjuerui
 * Date 2025-06-05
 **/

@Data
public class RealTimeUavFlightDTO {

    @ApiModelProperty("主键id")
    private Long id;
    @ApiModelProperty("飞行计划ID")
    private String planId;
    @ApiModelProperty("飞行单位")
    private String flightUnit;
    @ApiModelProperty("无人机ID")
    private String uavId;
    @ApiModelProperty("无人机状态")
    private String status;
    @ApiModelProperty("飞手经纬度位置")
    private String operatorPosition;
    @ApiModelProperty("飞手高度")
    private String operatorAltitude;
    @ApiModelProperty("无人机飞行速度")
    private String flightSpeed;
    @ApiModelProperty("无人机位置")
    private String uavPosition;
    @ApiModelProperty("无人机高度")
    private String uavAltitude;
    @ApiModelProperty("海拔高度")
    private String elevation;
    @ApiModelProperty("飞行时长")
    private String flightDuration;
    @ApiModelProperty("电池电压:伏")
    private String voltage;
    @ApiModelProperty("电量百分比")
    private Integer soc;
    @ApiModelProperty("上报时间")
    private Long reportTime;
}
