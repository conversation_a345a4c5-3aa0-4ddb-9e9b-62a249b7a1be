package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.digitaltwin.common.page.CommonPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 * Date: 2025/6/24
 * Author: lijunheng
 */
@ApiModel
@Data
public class MergeHandleGroupAdvice implements Serializable {

    @ApiModelProperty("待合并的原始需求总数")
    private Long originDemandTotalNum;

    @ApiModelProperty("分页数据")
    private CommonPage<MergeHandleGroupDTO> commonPage;
}
