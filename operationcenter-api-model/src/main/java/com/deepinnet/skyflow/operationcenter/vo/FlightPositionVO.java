package com.deepinnet.skyflow.operationcenter.vo;

import com.deepinnet.skyflow.operationcenter.enums.FlightPositionTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/14
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FlightPositionVO implements Serializable {

    private final static long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 飞行规划名称
     */
    @ApiModelProperty(value = "飞行规划名称")
    private String orderName;

    /**
     * 位置编码
     */
    @ApiModelProperty(value = "位置编码")
    private String positionNo;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型(POINT-点位; AREA-区域)")
    private FlightPositionTypeEnum type;

    /**
     * 名称(点位/区域)
     */
    @ApiModelProperty(value = "名称(点位/区域)")
    private String name;

    /**
     * 飞行点位
     */
    @ApiModelProperty(value = "飞行点位(区域)")
    private String flightPosition;

}
