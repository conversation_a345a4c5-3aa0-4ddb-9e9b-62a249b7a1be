package com.deepinnet.skyflow.operationcenter.client.uav;

import com.deepinnet.digitaltwin.common.response.Result;
import com.deepinnet.skyflow.operationcenter.dto.FlightUavRealtimePositionQueryDTO;
import com.deepinnet.skyflow.operationcenter.dto.RealTimeUavFlightPositionDTO;
import com.deepinnet.spatiotemporalplatform.dto.PositionQueryDTO;
import com.deepinnet.spatiotemporalplatform.vo.RealTimeUavFlightVO;
import com.deepinnet.spatiotemporalplatform.vo.UavFlightTrackVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Creator zengjuerui
 * Date 2025-06-16
 **/

@FeignClient(name = "centerFlightUavClient", url = "${sfoc.service.url}")
public interface CenterFlightUavClient {

    @GetMapping("/flight/uav//liveUrl")
    Result<String> liveUrl(@RequestParam("sn") String sn, @RequestParam(value = "httpProtocol",
            defaultValue = "http") String httpProtocol);

    @PostMapping("/flight/uav/queryPlanUavFlightTrack")
    Result<List<UavFlightTrackVO>> queryPlanUavFlightTrack(@RequestBody PositionQueryDTO positionQueryDTO);

    @PostMapping("/flight/uav/queryPlanUavRealtimeFlightTrack")
    Result<List<RealTimeUavFlightVO>> queryPlanUavRealtimeFlightTrack(@RequestBody PositionQueryDTO positionQueryDTO);

    @PostMapping("/flight/uav/queryPlanUavRealtimeFlightStatus")
    Result<RealTimeUavFlightPositionDTO> queryPlanUavRealtimeFlightStatus(@RequestBody FlightUavRealtimePositionQueryDTO positionQueryDTO);
}
