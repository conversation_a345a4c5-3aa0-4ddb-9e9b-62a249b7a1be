package com.deepinnet.skyflow.operationcenter.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * Description: 飞行计划状态枚举
 */
@AllArgsConstructor
@Getter
public enum FlightPlanApplyTypeEnum {

    /**
     * 长期
     */
    LONG_TREM(1, "长期"),

    /**
     * 明日
     */
    TOMORROW(2, "明日"),

    /**
     * 今日
     */
    TODAY(3, "今日"),
    ;

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static FlightPlanApplyTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FlightPlanApplyTypeEnum status : FlightPlanApplyTypeEnum.values()) {
            if (ObjectUtil.equals(code, status.getCode())) {
                return status;
            }
        }
        return null;
    }

    public final static List<Integer> SHORT_TERM = Arrays.asList(
            TOMORROW.getCode(),
            TODAY.getCode());
} 