package com.deepinnet.skyflow.operationcenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Creator zengjuerui
 * Date 2025-05-27
 **/

@Data
public class FlightDemandPlanDTO {

    /**
     * 主键，自增ID
     */
    private Long id;

    private String missionId;

    /**
     * 飞行计划唯一标识
     */
    @ApiModelProperty("飞行计划唯一标识")
    private String planId;

    /**
     * 无人机ID
     */
    @ApiModelProperty("无人机ID")
    private String uavId;

    /**
     * 飞行计划状态
     */
    @ApiModelProperty("飞行计划状态")
    private Integer status;

    /**
     * 计划起飞时间
     */
    @ApiModelProperty("计划起飞时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime planedTakeoffTime;

    /**
     * 计划降落时间
     */
    @ApiModelProperty("计划降落时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime planedLandingTime;

    /**
     * 飞手（操作员）
     */
    @ApiModelProperty("飞手（操作员）")
    private String operatorUser;

    /**
     * 飞手联系电话
     */
    @ApiModelProperty("飞手联系电话")
    private String operatorPhone;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime gmtModified;

    /**
     * 起降场
     */
    @ApiModelProperty("起降场")
    private String landingAerodromeId;

    /**
     * 计划飞行高度
     */
    @ApiModelProperty("计划飞行高度")
    private String planedAltitude;

    /**
     * 是否实名
     */
    @ApiModelProperty("是否实名")
    private Integer realAmeVerification;

    /**
     * 飞行单位
     */
    @ApiModelProperty("飞行单位")
    private String flightUnit;

    /**
     * 飞行单位ID
     */
    @ApiModelProperty("飞行单位ID")
    private String flightUnitId;

    /**
     * 申请用户名称
     */
    @ApiModelProperty("申请用户名称")
    private String applyUserName;

    /**
     * 空域id
     */
    @ApiModelProperty("空域id")
    private String airspaceId;

    /**
     * 场景code
     */
    @ApiModelProperty("场景code")
    private String scenceCode;

    /**
     * 业务单据号(需求编号)
     */
    @ApiModelProperty("业务单据号(需求编号)")
    private String bizNo;

    /**
     * 租户ID
     */
    @ApiModelProperty("租户ID")
    private String tenantId;

    /**
     * 关联长期计划的ID
     */
    @ApiModelProperty("关联长期计划的ID")
    private String relatedId;

    /**
     * 申请计划类型: 1-长期;2-次日;3-当日
     */
    @ApiModelProperty(" 申请计划类型: 1-长期;2-次日;3-当日")
    private Integer applyType;

    /**
     * 服务商计划编号
     */
    @ApiModelProperty("服务商计划编号")
    private String outPlanNo;

    /**
     * 计划名称
     */
    @ApiModelProperty("计划名称")
    private String planName;

    /**
     * 需求名称
     */
    @ApiModelProperty("需求名称")
    private String requirementName;

    /**
     * 无人机机型
     */
    @ApiModelProperty("无人机机型")
    private String uavModel;

    @ApiModelProperty("无人机")
    private FlightUavDTO flightUav;

    @ApiModelProperty("无人机实时位置")
    private List<RealTimeUavFlightDTO> realtimeUavInfoList;

    @ApiModelProperty("无人机机巢[可能为空]")
    private FlightUavStationDTO flightUavStation;

    @ApiModelProperty("计划对应的需求")
    private FlightDemandDTO flightDemand;

    @ApiModelProperty("无人机起降场[可能为空]")
    private AerodromeDTO aerodrome;

    @ApiModelProperty("计划创建人名称")
    private String crtUserName;


    @ApiModelProperty("计划创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime planCreateTime;
}

