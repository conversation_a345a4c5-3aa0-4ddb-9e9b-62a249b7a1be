package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:
 * Date: 2025/6/14
 * Author: lijunheng
 */
@Data
@ApiModel("合并需求额外属性")
public class FlightMergeDemandPropDTO implements Serializable {

    @ApiModelProperty("合并需求的时间周期")
    private DemandMergeScheduleDTO mergeScheduleDTO;

    @ApiModelProperty("合并需求的飞行轨迹")
    private FlightRoutePlan flightRoutePlan;

}
