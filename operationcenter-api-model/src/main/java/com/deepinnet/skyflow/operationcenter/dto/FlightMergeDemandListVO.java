package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightDemandTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Description: 合并需求展示列表
 * Date: 2025/6/16
 * Author: lijunheng
 */
@ApiModel
@Data
public class FlightMergeDemandListVO implements Serializable {

    private Long id;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    private String demandNo;

    /**
     * 需求名称
     */
    @ApiModelProperty(value = "需求名称")
    private String name;

    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private FlightDemandTypeEnum type;

    /**
     * 发布者编号
     */
    @ApiModelProperty(value = "发布者编号")
    private String publisherNo;

    /**
     * 发布者名称
     */
    @ApiModelProperty(value = "发布者名称")
    private String publisherName;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "周期开始日期")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "周期结束日期")
    private LocalDate endDate;
    /**
     * 最早的开始时间
     */
    @ApiModelProperty(value = "最早起飞时间")
    private LocalTime earliestStartTime;

    /**
     * 最晚的结束时间
     */
    @ApiModelProperty(value = "最晚起飞时间")
    private LocalTime latestEndTime;
}
