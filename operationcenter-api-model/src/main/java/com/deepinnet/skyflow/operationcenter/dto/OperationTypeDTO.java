package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 操作类型传输对象
 * 用于前端下拉选择操作类型
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "操作类型")
public class OperationTypeDTO {

    /**
     * 操作类型代码
     */
    @ApiModelProperty("操作类型代码")
    private String code;

    /**
     * 操作类型名称
     */
    @ApiModelProperty("操作类型名称")
    private String name;

    /**
     * 操作类型描述
     */
    @ApiModelProperty("操作类型描述")
    private String description;
}
