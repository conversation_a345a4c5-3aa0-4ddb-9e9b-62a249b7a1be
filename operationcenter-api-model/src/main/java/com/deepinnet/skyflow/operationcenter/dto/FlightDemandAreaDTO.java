package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 飞行需求区域DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行需求区域信息")
public class FlightDemandAreaDTO {

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "原始需求编号")
    private String demandCode;

    @ApiModelProperty(value = "需求名称")
    private String demandName;

    @ApiModelProperty(value = "区域编号")
    private String areaCode;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    /**
     * 区域的序号，从1开始
     */
    @ApiModelProperty(value = "区域的序号")
    private Integer sequence;

    /**
     * 类型(POINT; AREA)
     */
    @ApiModelProperty("类型(POINT; AREA)")
    private String type;

    /**
     * 中心点坐标-经度
     */
    @ApiModelProperty(value = "中心点坐标-经度")
    private String centerPointLongitude;

    /**
     * 中心点坐标-纬度
     */
    @ApiModelProperty(value = "中心点坐标-纬度")
    private String centerPointLatitude;

    /**
     * 区域坐标，WKT字符串
     */
    @ApiModelProperty(value = "区域坐标，WKT字符串")
    private String areaCoordinate;

    /**
     * 区域面积，平方公里
     */
    @ApiModelProperty(value = "区域面积，平方公里")
    private Double area;

    @ApiModelProperty(value = "是否新增")
    private Boolean isAdd = Boolean.FALSE;

    @ApiModelProperty(value = "是否勾选")
    private Boolean isChecked = Boolean.FALSE;
} 