package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightDemandCycleTypeEnum;
import com.deepinnet.skyflow.operationcenter.enums.FlyingFrequencyEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * Description:
 * Date: 2025/4/16
 * Author: lijunheng
 */
@Data
@ApiModel(description = "日常巡检飞行需求信息")
public class RoutineInspectionFlightDemandDTO {

    @ApiModelProperty(value = "周期类型")
    private FlightDemandCycleTypeEnum cycleType;

    @ApiModelProperty(value = "需求开始时间")
    private LocalDate demandStartTime;

    @ApiModelProperty(value = "需求结束时间")
    private LocalDate demandEndTime;

    @ApiModelProperty(value = "飞行开始时间")
    private LocalTime flyingStartTime;

    @ApiModelProperty(value = "飞行结束时间")
    private LocalTime flyingEndTime;

    @ApiModelProperty(value = "飞行频率")
    private FlyingFrequencyEnum flyingFrequency;

    @ApiModelProperty(value = "飞行次数")
    private Integer flyingNum;
}
