package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.FlightDraftBizTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 飞行草稿查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
public class FlightDraftQueryDTO extends PageQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 草稿名称
     */
    private String name;

    /**
     * 草稿编号
     */
    private String draftCode;

    /**
     * 创建人
     */
    private List<String> creatorList;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 草稿类型
     */
    private List<FlightDraftBizTypeEnum> typeList;

    /**
     * 业务编号
     */
    private String bizCode;
}