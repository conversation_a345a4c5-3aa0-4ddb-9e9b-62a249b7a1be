package com.deepinnet.skyflow.operationcenter.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/7
 */

@Getter
public enum FlightPositionTypeEnum {

    /**
     * POINT
     */
    POINT("POINT", "点位"),

    /**
     * POLYGON
     */
    POLYGON("POLYGON", "区域");

    private final String code;

    private final String name;

    FlightPositionTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static FlightPositionTypeEnum getByCode(String code) {
        for (FlightPositionTypeEnum flightPositionTypeEnum : FlightPositionTypeEnum.values()) {
            if (StrUtil.equals(flightPositionTypeEnum.getCode(), code)) {
                return flightPositionTypeEnum;
            }
        }
        return null;
    }

}
