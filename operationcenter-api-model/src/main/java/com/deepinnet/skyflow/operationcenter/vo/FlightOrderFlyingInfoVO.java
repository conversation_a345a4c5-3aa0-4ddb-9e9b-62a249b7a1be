package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/14
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FlightOrderFlyingInfoVO implements Serializable {

    private final static long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 飞行频次
     */
    @ApiModelProperty(value = "飞行频次")
    private String flyingFrequency;

    /**
     * 飞行次数
     */
    @ApiModelProperty(value = "飞行次数")
    private Integer flyingNum;

    /**
     * 已用飞行次数
     */
    @ApiModelProperty(value = "已用飞行次数")
    private Integer flyingNumUsed;

    /**
     * 剩余飞行次数
     */
    @ApiModelProperty(value = "剩余飞行次数")
    private Integer flyingNumRemained;

    /**
     * 预估飞行次数
     */
    @ApiModelProperty(value = "飞行次数")
    private Integer exceptFlyingNum;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 飞行区域
     */
    @ApiModelProperty(value = "飞行区域")
    private String flyingArea;

    /**
     * 应用场景
     */
    @ApiModelProperty(value = "应用场景")
    private String applyScenarios;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 有效期开始时间
     */
    @ApiModelProperty(value = "有效期开始时间", example = "2025-04-18T10:00:00")
    private Long validityPeriodStart;

    /**
     * 有效期结束时间
     */
    @ApiModelProperty(value = "有效期结束时间", example = "2025-04-18T10:00:00")
    private Long validityPeriodEnd;

}
