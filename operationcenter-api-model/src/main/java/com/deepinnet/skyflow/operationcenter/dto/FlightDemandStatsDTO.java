package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * C<PERSON> zeng<PERSON>erui
 * Date 2025-05-27
 **/

@Data
public class FlightDemandStatsDTO {

    @ApiModelProperty("飞行需求数")
    private Integer demandNum = 0;
    @ApiModelProperty("飞行计划数量")
    private Integer planNum = 0;
    @ApiModelProperty("已完成飞行计划数量")
    private Integer planFinishedNum = 0;
    @ApiModelProperty("飞行小时")
    private String flightHours = "0";
    @ApiModelProperty("飞行里程")
    private String flightDistance = "0";
    @ApiModelProperty("应急任务平均到达现场时间，分钟")
    private String averageResponseTime = "0";
    @ApiModelProperty("空中巡逻面积，平方公里")
    private String aerialPatrolArea = "0";
    @ApiModelProperty("空中巡逻发现事件, 日常")
    private String aerialPatrolEventDetected = "0";
    @ApiModelProperty("空中巡逻发现事件, 应急")
    private String aerialPatrolEventEmergencyDetected = "0";
}
