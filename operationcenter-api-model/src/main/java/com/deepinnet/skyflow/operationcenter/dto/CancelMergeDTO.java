package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 取消合并DTO
 */
@Data
@ApiModel("取消合并DTO")
public class CancelMergeDTO {

    @ApiModelProperty("合并处理编号")
    @NotBlank(message = "合并处理编号不能为空")
    private String mergeHandleCode;

    @ApiModelProperty("合并处理操作人编号")
    private String operatorUserNo;

    @ApiModelProperty("合并处理操作人名称")
    private String operatorUserName;
} 