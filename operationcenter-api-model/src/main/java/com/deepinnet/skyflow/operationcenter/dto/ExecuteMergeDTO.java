package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 执行合并需求DTO
 */
@Data
@ApiModel("执行合并需求DTO")
public class ExecuteMergeDTO {

    @ApiModelProperty("合并处理编号")
    @NotBlank(message = "合并处理编号不能为空")
    private String mergeHandleCode;

    @ApiModelProperty("合并处理操作人编号")
    private String operatorUserNo;

    @ApiModelProperty("合并处理操作人名称")
    private String operatorUserName;
} 