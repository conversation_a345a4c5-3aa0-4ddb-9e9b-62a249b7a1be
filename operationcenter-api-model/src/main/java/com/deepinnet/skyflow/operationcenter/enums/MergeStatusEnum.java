package com.deepinnet.skyflow.operationcenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合并状态枚举
 */
@Getter
@AllArgsConstructor
public enum MergeStatusEnum {

    /**
     * 待合并
     */
    PENDING("PENDING", "待合并"),

    /**
     * 未参与合并
     */
    NOT_PARTICIPATED("NOT_PARTICIPATED", "未参与合并"),

    /**
     * 不能合并
     */
    CANNOT_MERGE("CANNOT_MERGE", "不能合并"),

    /**
     * 已合并
     */
    MERGED("MERGED", "已合并"),

    /**
     * 取消合并
     */
    CANCELLED("CANCELLED", "取消合并");

    private final String code;
    private final String desc;

    public static MergeStatusEnum getByCode(String code) {
        for (MergeStatusEnum statusEnum : MergeStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
} 