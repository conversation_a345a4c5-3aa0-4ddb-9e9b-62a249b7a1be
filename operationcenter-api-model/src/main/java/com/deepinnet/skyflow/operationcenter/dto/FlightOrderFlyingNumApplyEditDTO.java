package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 飞行额度次数申请编辑DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行额度次数申请编辑DTO")
public class FlightOrderFlyingNumApplyEditDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 申请id
     */
    @ApiModelProperty(value = "申请id")
    private String approvalId;


    /**
     * 飞行订单编号
     */
    @ApiModelProperty(value = "飞行订单编号")
    private String flightOrderNo;

    /**
     * 申请数量
     */
    @ApiModelProperty(value = "申请数量")
    private Integer applyFlyingNum;

    /**
     * 申请理由
     */
    @ApiModelProperty(value = "申请理由")
    private String applyReason;

} 