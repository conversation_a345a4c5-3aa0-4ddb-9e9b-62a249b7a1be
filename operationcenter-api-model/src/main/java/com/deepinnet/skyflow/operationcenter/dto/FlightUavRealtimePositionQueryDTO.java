package com.deepinnet.skyflow.operationcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Creator zengjuerui
 * Date 2025-06-03
 **/

@Data
public class FlightUavRealtimePositionQueryDTO {

    @NotNull
    @ApiModelProperty(value = "计划状态", required = true)
    private Integer planStatus;

    @NotBlank
    @ApiModelProperty(value = "计划 id", required = true)
    private String planId;

    @ApiModelProperty(value = "巡逻区域 wkt")
    private String areaWkt;

    @ApiModelProperty(value = "紧急事件中心点经度")
    private String centerPointLng;

    @ApiModelProperty(value = "紧急事件中心点纬度")
    private String centerPointLat;

    @ApiModelProperty("是否需要历史轨迹")
    private Boolean needFlownTrajectoryWkt = false;


}
