package com.deepinnet.skyflow.operationcenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 飞行订单VO
 *
 * <AUTHOR>
 */
@Data
public class FlightOrderVO {

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 订单状态(APPROVING-审核中; IN_PROGRESS-进行中; FINISHED-已结束; CLOSED-已关闭,对应审核拒绝)
     */
    @ApiModelProperty(value = "订单状态(APPROVING-审核中; IN_PROGRESS-进行中; FINISHED-已结束; CLOSED-已关闭,对应审核拒绝)")
    private String status;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "服务名称(主产品服务名称)")
    private String productName;

    /**
     * 主产品类型(flight_uav - 机型服务产品；flight_scenario - 场景服务产品)
     */
    @ApiModelProperty(value = "主产品类型(flight_uav - 机型服务产品；flight_scenario - 场景服务产品)")
    private String mainProductType;

    /**
     * 用户编号
     */
    @ApiModelProperty(value = "用户编号")
    private String userNo;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String userName;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private String orderAmount;

    /**
     * 付款方式(MONTHLY_PAY-月结; IMMEDIATE_PAY-立即支付)
     */
    @ApiModelProperty(value = "付款方式(MONTHLY_PAY-月结; IMMEDIATE_PAY-立即支付)")
    private String payType;

    /**
     * 支付渠道(alipay; wechat; unionpay)
     */
    @ApiModelProperty(value = "支付渠道(alipay; wechat; unionpay)")
    private String paySource;

    /**
     * 审核状态(IN_REVIEW-审核中; APPROVED-审核通过; REJECT-审核拒绝)
     */
    @ApiModelProperty(value = "审核状态(IN_REVIEW-审核中; APPROVED-审核通过; REJECT-审核拒绝)")
    private String approveStatus;

    /**
     * 支付状态（UNPAY-未支付; PAID-已支付）
     */
    @ApiModelProperty(value = "支付状态（UNPAY-未支付; PAID-已支付）")
    private String payStatus;

    /**
     * 退款状态(UNREFUND-未退款; REFUNDING-退款中; REFUNDED-已退款)
     */
    @ApiModelProperty(value = "退款状态(UNREFUND-未退款; REFUNDING-退款中; REFUNDED-已退款)")
    private String refundStatus;

    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    private String payAmount;

    /**
     * 退款金额
     */
    @ApiModelProperty(value = "退款金额")
    private String refundAmount;

    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    private Long orderTime;

    /**
     * 类目编号
     */
    @ApiModelProperty(value = "类目编号")
    private String categoryNo;

    /**
     * 类目名称
     */
    @ApiModelProperty(value = "类目名称")
    private String categoryName;

    /**
     * 场景
     */
    @ApiModelProperty(value = "场景")
    private String scene;

    /**
     * 订单类型(NORMAL-普通订单; DEMAND_PLAN-需求计划订单)
     */
    @ApiModelProperty(value = "订单类型(NORMAL-普通订单; DEMAND_PLAN-需求计划订单)")
    private String orderType;

    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    /**
     * 周期编号
     */
    @ApiModelProperty(value = "周期编号")
    private String cycleNo;

    /**
     * 周期名称
     */
    @ApiModelProperty(value = "周期名称")
    private String cycleName;

    @ApiModelProperty(value = "是否允许审核")
    private Boolean allowApprove = false;

    @ApiModelProperty("是否可编辑")
    private Boolean allowEdit;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 周期计划信息
     */
    private FlightPlanCycleVO cyclePlanInfo;

    /**
     * 飞行信息
     */
    private FlightOrderFlyingInfoVO flyingInfo;

    /**
     * 飞行点位列表
     */
    private List<FlightPositionVO> flightPositions;

    /**
     * 文件列表
     */
    private List<FlightOrderFileVO> files;

    /**
     * 审核信息
     */
    private List<FlightOrderApprovalVO> approvalInfos;

    /**
     * 订单产品使用记录列表
     */
    private List<FlightOrderProductUsageVO> productUsageList;

    /**
     * 审批单
     *   - 审批节点
     */
    private List<ApprovalRecordVO> approvalRecords;
} 