package com.deepinnet.skyflow.operationcenter.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * Description: 飞行计划状态枚举
 */
@AllArgsConstructor
@Getter
public enum FlightPlanStatusEnum {

    /**
     * 0-待申报
     */
    NEED_APPLIED(0, "待申报"),

    /**
     * 2-已申报
     */
    APPLIED(2, "已申报"),

    /**
     * 5-执行状态
     */
    EXECUTING(5, "执行状态"),

    /**
     * 6-完成状态
     */
    COMPLETED(6, "完成状态"),

    /**
     * 7-撤销状态
     */
    REVOKED(7, "撤销状态"),

    /**
     * 8-取消状态
     */
    CANCELLED(8, "取消状态"),

    /**
     * 9-已过期
     */
    EXPIRED(9, "已过期"),
    ;

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static FlightPlanStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FlightPlanStatusEnum status : FlightPlanStatusEnum.values()) {
            if (ObjectUtil.equals(code, status.getCode())) {
                return status;
            }
        }
        return null;
    }

    public final static List<Integer> FLIGHT_STATUS = Arrays.asList(
            EXECUTING.getCode(),
            COMPLETED.getCode());
} 