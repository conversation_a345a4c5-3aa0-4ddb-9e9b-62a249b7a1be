package com.deepinnet.skyflow.operationcenter.dto;

import com.deepinnet.skyflow.operationcenter.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 飞行需求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行需求信息")
public class FlightDemandDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 需求编号
     */
    @ApiModelProperty(value = "需求编号")
    private String demandNo;

    /**
     * 需求名称
     */
    @ApiModelProperty(value = "需求名称")
    private String name;

    /**
     * 需求描述
     */
    @ApiModelProperty(value = "需求描述")
    private String demandDesc;

    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private FlightDemandTypeEnum type;

    @ApiModelProperty(value = "需求来源场景")
    private FlightDemandSceneEnum scene = FlightDemandSceneEnum.SKY_FLOW_ECONOMY_MATCHING_PLATFORM;

    /**
     * 发布者编号
     */
    @ApiModelProperty(value = "发布者编号")
    private String publisherNo;

    /**
     * 发布者名称
     */
    @ApiModelProperty(value = "发布者名称")
    private String publisherName;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private LocalDateTime publishTime;

    /**
     * 编辑者编号
     */
    @ApiModelProperty(value = "编辑者编号")
    private String editorNo;

    /**
     * 编辑者名称
     */
    @ApiModelProperty(value = "编辑者名称")
    private String editorName;

    /**
     * 编辑时间
     */
    @ApiModelProperty(value = "编辑时间")
    private LocalDateTime editTime;

    /**
     * 飞行订单编号
     */
    @ApiModelProperty(value = "飞行订单编号")
    private String flightOrderNo;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "服务名称(主产品服务名称)")
    private String productName;

    /**
     * 飞行无人机型号
     */
    @ApiModelProperty(value = "飞行无人机型号，要么是单个无人机机型，要么是不限机型，直接是中文名称，只用于列表展示")
    private String flightUavBm;

    /**
     * 巡检区域名称
     */
    @ApiModelProperty("巡检区域名称")
    private String inspectionAreaName;

    /**
     * 巡检区域编码, 格式: provinceCode/cityCode/countryCode/streetCode
     */
    @ApiModelProperty("巡检区域编码, 格式: provinceCode/cityCode/countryCode/streetCode")
    private String inspectionAreaCode;

    /**
     * 增值服务
     */
    @ApiModelProperty(value = "增值服务")
    private List<String> incrementService = new ArrayList<>();

    /**
     * 机型列表
     */
    @ApiModelProperty(value = "机型列表")
    private List<String> modelCodeList = new ArrayList<>();

    /**
     * 附加文件请求
     */
    @ApiModelProperty(value = "附加文件请求")
    private List<FileDTO> requestAdditionalFiles;

    /**
     * 详细类目编号
     */
    @ApiModelProperty(value = "类目编号")
    private String categoryNo;

    @ApiModelProperty(value = "类目名称")
    private String categoryFullName;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private FlightDemandMatchStatusEnum matchStatus;

    @ApiModelProperty(value = "区域面积，平方公里")
    private Double area;

    @ApiModelProperty(value = "中心点坐标-经度")
    private String centerPointLongitude;

    @ApiModelProperty(value = "中心点坐标-纬度")
    private String centerPointLatitude;

    @ApiModelProperty(value = "区域坐标，WKT字符串")
    private String areaCoordinate;

    @ApiModelProperty(value = "客户组织ID")
    private String organizationId;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "客户组织名称")
    private String organizationName;

    /**
     * 周期类型
     */
    @ApiModelProperty(value = "周期类型")
    private FlightDemandCycleTypeEnum cycleType;

    /**
     * 需求开始时间
     */
    @ApiModelProperty(value = "需求开始时间")
    private LocalDate demandStartTime;

    /**
     * 需求结束时间
     */
    @ApiModelProperty(value = "需求结束时间")
    private LocalDate demandEndTime;

    /**
     * 飞行开始时间
     */
    @ApiModelProperty(value = "飞行开始时间")
    private LocalTime flyingStartTime;

    /**
     * 飞行结束时间
     */
    @ApiModelProperty(value = "飞行结束时间")
    private LocalTime flyingEndTime;

    @ApiModelProperty(value = "飞行频率")
    private FlyingFrequencyEnum flyingFrequency;

    @ApiModelProperty(value = "飞行次数")
    private Integer flyingNum;

    /**
     * 总飞行次数
     */
    @ApiModelProperty(value = "预计总飞行次数")
    private Integer totalFlyingNum;

    /**
     * 飞行次数
     */
    @ApiModelProperty(value = "实时飞行次数")
    private Integer realtimeFlightCount;

    /**
     * 区域数量
     */
    @ApiModelProperty(value = "区域数量")
    private Integer areaNum;

    @ApiModelProperty(value = "是否需要审核")
    private Boolean needApprove = Boolean.TRUE;

    /**
     * 审核状态
     * {@link com.deepinnet.approval.ApproveStatusEnum}
     */
    @ApiModelProperty(value = "审核状态")
    private String approveStatus;

    /**
     * 审核结束时间
     */
    @ApiModelProperty(value = "审核结束时间")
    private LocalDateTime approveEndTime;

    @ApiModelProperty(value = "审批ID")
    private String approvalId;

    /**
     * 上个版本的需求编号
     */
    @ApiModelProperty("上个版本的需求编号")
    private String parentCode;

    /**
     * 是否最新数据
     */
    @ApiModelProperty("是否最新数据")
    private Boolean isLatest;

    @ApiModelProperty("需求组编号")
    private String groupCode;

    @ApiModelProperty("版本")
    private Integer version;

    @ApiModelProperty("是否补报")
    private Boolean isSupplement;

    /**
     * 业务数据
     */
    @ApiModelProperty(value = "业务数据")
    private String bizData;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 日常巡检需求详情（当type=ROUTINE_INSPECTION时使用）
     */
    @ApiModelProperty(value = "日常巡检需求详情", notes = "当需求类型为ROUTINE_INSPECTION时使用")
    private RoutineInspectionFlightDemandDTO routineInspectionDetail;

    /**
     * 物流运输需求详情（当type=LOGISTICS_TRANSPORTATION时使用）
     */
    @ApiModelProperty(value = "物流运输需求详情", notes = "当需求类型为LOGISTICS_TRANSPORTATION时使用")
    private LogisticsTransportationFlightDemandDTO logisticsTransportationDetail;

    /**
     * 应急处置需求详情（当type=EMERGENCY_RESPONSE时使用）
     */
    @ApiModelProperty(value = "应急处置需求详情", notes = "当需求类型为EMERGENCY_RESPONSE时使用")
    private EmergencyResponseFlightDemandDTO emergencyResponseDetail;


    @ApiModelProperty(value = "需求分配服务商列表")
    private List<FlightDemandMatchServiceProviderDTO> serviceProviderList;

    /**
     * 区域列表
     */
    @ApiModelProperty(value = "区域列表")
    private List<FlightDemandAreaDTO> areaList = new ArrayList<>();

    /**
     * 合并状态，待合并、未参与合并、不能合并、已合并、取消合并
     */
    @ApiModelProperty("合并状态，待合并、未参与合并、不能合并、已合并、取消合并")
    private MergeStatusEnum mergeStatus;

    /**
     * 合并处理编号
     */
    @ApiModelProperty("合并处理编号")
    private String mergeHandleCode;

    /**
     * 是否是合并需求
     */
    @ApiModelProperty("是否是合并需求")
    private Boolean isMergeDemand = Boolean.FALSE;

    @ApiModelProperty("原始需求列表")
    private List<FlightDemandDTO> originDemandList;

    @ApiModelProperty("合并需求额外属性")
    private FlightMergeDemandPropDTO mergeDemandProp;

    @ApiModelProperty("原始需求订单号列表")
    private List<String> flightOrderNoList;

    @ApiModelProperty("是否可审批")
    private Boolean allowApprove;

    @ApiModelProperty("是否可编辑")
    private Boolean allowEdit;

    public Object queryDetail() {
        switch (this.type) {
            case ROUTINE_INSPECTION:
                return this.routineInspectionDetail;
            case LOGISTICS_TRANSPORTATION:
                return this.logisticsTransportationDetail;
            case EMERGENCY_RESPONSE:
                return this.emergencyResponseDetail;
            default:
                return null;
        }
    }

    public void createDetail(Object detail) {
        switch (this.type) {
            case ROUTINE_INSPECTION:
                this.routineInspectionDetail = (RoutineInspectionFlightDemandDTO) detail;
                break;
            case LOGISTICS_TRANSPORTATION:
                this.logisticsTransportationDetail = (LogisticsTransportationFlightDemandDTO) detail;
                break;
            case EMERGENCY_RESPONSE:
                this.emergencyResponseDetail = (EmergencyResponseFlightDemandDTO) detail;
        }
    }
} 