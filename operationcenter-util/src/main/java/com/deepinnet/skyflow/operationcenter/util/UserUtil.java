package com.deepinnet.skyflow.operationcenter.util;

import cn.dev33.satoken.stp.StpUtil;
import com.deepinnet.skyflow.operationcenter.util.enums.UserTypeEnum;

import java.util.List;

/**
 * <AUTHOR> wong
 * @create 2025/4/19 16:14
 * @Description
 */
public class UserUtil {

    public static String getUserNo() {
        return (String) StpUtil.getExtra("userNo");
    }

    public static String getUserName() {
        return (String) StpUtil.getExtra("name");
    }
    public static String getAccount() {
        return (String) StpUtil.getExtra("account");
    }

    public static UserTypeEnum getUserType() {
        String userType = (String) StpUtil.getExtra("userType");
        return UserTypeEnum.getByCode(userType);
    }

    public static String getSceneEnum() {
        return (String) StpUtil.getExtra("scene");
    }

    public static List<Long> getDepartmentIds() {
        return (List<Long>) StpUtil.getExtra("departmentIds");
    }
}
