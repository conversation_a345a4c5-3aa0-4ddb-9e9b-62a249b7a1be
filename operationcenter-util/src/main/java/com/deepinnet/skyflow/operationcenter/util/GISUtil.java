package com.deepinnet.skyflow.operationcenter.util;

import cn.hutool.core.collection.CollectionUtil;
import com.deepinnet.digitaltwin.common.util.PointCoordinate;
import com.deepinnet.spatiotemporalplatform.model.util.WktUtil;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.LineSegment;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.math.Vector2D;

import java.util.Comparator;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Creator zengjuerui
 * Date 2024-11-25
 **/

public class GISUtil {

    private static final double EARTH_RADIUS = 6371000; // 地球平均半径(米)

    /**
     * 计算方位角
     * */
    public static double calculateAzimuth(Point p1, Point p2) {
        // 提取点的坐标
        Coordinate c1 = p1.getCoordinate();
        Coordinate c2 = p2.getCoordinate();

        // 计算 Δx 和 Δy
        double deltaX = c2.x - c1.x;
        double deltaY = c2.y - c1.y;

        // 使用 atan2 计算弧度，并转换为角度
        double azimuthRadians = Math.atan2(deltaX, deltaY); // 注意参数顺序
        double azimuthDegrees = Math.toDegrees(azimuthRadians);

        // 确保角度范围在 0 ~ 360 度
        if (azimuthDegrees < 0) {
            azimuthDegrees += 360;
        }

        return azimuthDegrees;
    }

    /**
     * 判断 LineString 是否有弧度
     * @param lineString 线段
     * @param angleThreshold 角度阈值
     * */
    public static boolean hasCurvature(LineString lineString, double angleThreshold) {
        if(lineString == null) {
            return false;
        }

        Coordinate[] coordinates = lineString.getCoordinates();
        if(coordinates == null || coordinates.length < 2) {
            return false;
        }

        // 遍历每一对连续线段
        for (int i = 1; i < coordinates.length - 1; i++) {
            // 当前线段方向向量
            Vector2D vec1 = Vector2D.create(coordinates[i - 1], coordinates[i]);

            // 下一线段方向向量
            Vector2D vec2 = Vector2D.create(coordinates[i], coordinates[i + 1]);

            // 计算两向量的夹角
            double angle = vec1.angleTo(vec2);

            // 转为角度
            double angleInDegrees = Math.toDegrees(angle);

            // 如果夹角大于阈值，认为存在弧度
            if (Math.abs(angleInDegrees) > angleThreshold) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断 LineString 是否有弧度
     * @param lineString 线段
     * @param distanceThreshold 两点间直线距离阈值
     * */
    public static boolean hasCurvatureByOffset(LineString lineString, double distanceThreshold) {
        Coordinate[] coordinates = lineString.getCoordinates();
        LineSegment baseSegment = new LineSegment(coordinates[0], coordinates[coordinates.length - 1]);

        for (int i = 1; i < coordinates.length - 1; i++) {
            double distance = baseSegment.distance(coordinates[i]);
            if (distance > distanceThreshold) {
                return true; // 存在弧度
            }
        }
        return false;
    }

    public static double calculateDistanceInMeters(Coordinate coord1, Coordinate coord2) {
        double lon1 = Math.toRadians(coord1.x);
        double lat1 = Math.toRadians(coord1.y);
        double lon2 = Math.toRadians(coord2.x);
        double lat2 = Math.toRadians(coord2.y);

        double dLat = lat2 - lat1;
        double dLon = lon2 - lon1;

        double a = Math.pow(Math.sin(dLat / 2), 2)
                + Math.cos(lat1) * Math.cos(lat2)
                * Math.pow(Math.sin(dLon / 2), 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS * c;
    }

    public static <T> List<T> sortToTargetPoint(List<T> list, Point target, Function<T, Point> pointFunction) {
        if(CollectionUtil.isEmpty(list)) {
            return list;
        }

        return list.stream()
                .sorted(Comparator.comparing(c -> {
                    Point tmp = pointFunction.apply(c);
                    if(tmp == null) return Double.MAX_VALUE;

                    return GISUtil.calculateDistanceInMeters(tmp.getCoordinate(), target.getCoordinate());
                }))
                .collect(Collectors.toList());
    }

    /**
     * 计算多个点之间的中心点
     * @param pointList
     * @return
     */
    public static PointCoordinate calculateSphericalCenter(List<PointCoordinate> pointList) {
        if (pointList == null || pointList.isEmpty()) {
            throw new IllegalArgumentException("Point list is empty");
        }

        double x = 0.0;
        double y = 0.0;
        double z = 0.0;

        for (PointCoordinate point : pointList) {
            double latRad = Math.toRadians(point.getLatitude());
            double lonRad = Math.toRadians(point.getLongitude());

            // Convert to Cartesian coordinates
            x += Math.cos(latRad) * Math.cos(lonRad);
            y += Math.cos(latRad) * Math.sin(lonRad);
            z += Math.sin(latRad);
        }

        int total = pointList.size();
        x /= total;
        y /= total;
        z /= total;

        // Convert average x, y, z coordinate to latitude and longitude
        double hyp = Math.sqrt(x * x + y * y);
        double centerLat = Math.toDegrees(Math.atan2(z, hyp));
        double centerLon = Math.toDegrees(Math.atan2(y, x));

        return new PointCoordinate(centerLon, centerLat);
    }
}
